const fs = require('fs');
const path = require('path');

// Files to update (found from grep search)
const filesToUpdate = [
  'src/Pages/UserConsole/userData/Studies.js',
  'src/Pages/UserConsole/userData/Sherut.js',
  'src/Pages/UserConsole/userData/PersonalInformation.js',
  'src/Pages/UserConsole/userData/Emergency.js',
  'src/Pages/UserConsole/userData/Address.js',
  'src/Pages/UserConsole/UserConsoleDataIndex.js',
  'src/Pages/UserConsole/search/SearchResultRow.js.bak',
  'src/Pages/UserConsole/search/SearchResultRow.js',
  'src/Pages/UserConsole/files/UploadFile.js',
  'src/Pages/UserConsole/files/ServiceDocs.js',
  'src/Pages/UserConsole/files/ReadAndSign.js',
  'src/Pages/UserConsole/files/OpenPDFfiles.js',
  'src/Pages/UserConsole/files/OLD/OpenPDFfiles.js',
  'src/Pages/UserConsole/files/FileItem.js',
  'src/Pages/UserConsole/files/FilesPage.js',
  'src/Pages/UserConsole/files/ExternalLink.js',
  'src/Pages/UserConsole/files/DocsTromSherut.js',
  'src/Pages/training/TrainingPage.js'
];

// Update each file
filesToUpdate.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  try {
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      console.log(`File not found: ${fullPath}`);
      return;
    }
    
    // Read file content
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Replace @material-ui/core imports with @mui/material
    const updatedContent = content.replace(/@material-ui\/core/g, '@mui/material');
    
    // Check if content changed
    if (content !== updatedContent) {
      // Write updated content back to file
      fs.writeFileSync(fullPath, updatedContent, 'utf8');
      console.log(`Updated: ${filePath}`);
    } else {
      console.log(`No changes needed for: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error.message);
  }
});

console.log('Update complete!'); 
