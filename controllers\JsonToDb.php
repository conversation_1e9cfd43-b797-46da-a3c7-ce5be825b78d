<?php defined('BASEPATH') OR exit('No direct script access allowed');

class JsonToDb extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }

    
    //https://sherut-leumi.wdev.co.il/api/JsonToDb/saveCities
    public function saveCities() {
        
        $this->sherutLeumi->saveCities();
        $this->sherutLeumi->saveCitiesDev();

        
    }
    
    //https://sherut-leumi.wdev.co.il/api/JsonToDb/saveSchools
    public function saveSchools() {
    
        $this->sherutLeumi->saveSchoolsDev();
        $this->sherutLeumi->saveSchools();

    }
        
    
    
}