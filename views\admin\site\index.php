<style>
    .migration-field {
        padding: 10px;
    }
</style>
<div class="row">
    <div class="col-lg-3 col-sm-12"> 
        <div class="list-group">
            <a href="<?php echo base_url('admin/site/index'); ?>" class="list-group-item <?php echo ! $selected_page ? "active" : ""; ?>">הגדרות אתר</a>
            
            <?php if($pages)foreach($pages as $row) { ?>
            <a href="<?php echo base_url('admin/site/index/' . $row->Id() . '/'); ?>" class="list-group-item <?php echo $selected_page && ($selected_page->Id() === $row->Id() || $selected_page->Arg('parent_id') === $row->Id()) ? "active" : ""; ?>"><?php echo $row->Arg('title'); ?></a>
            
                <?php if($row->Arr('childs')) { ?>
                <div class="list-group-item">
                    <?php foreach($row->Arr('childs') as $child) { ?>
                    <a href="<?php echo base_url('admin/site/index/' . $child->Id() . '/'); ?>" class="list-group-item <?php echo $selected_page && $selected_page->Id() === $child->Id() ? "active" : ""; ?>"><?php echo $child->Arg('title'); ?></a>
                    <?php } ?>
                </div>
                <?php } ?>
            <?php } ?>
        </div>
    </div>
    
    <?php if(IS_DEVELOPER) { ?>
    <div class="col-lg-9 col-sm-12">
        <?php if( ( $selected_page && $selected_page->Arg('parent_id') === 0 ) || !$selected_page) { ?>

            <?php echo form_open('admin/' . $this->router->fetch_class() . '/put_page/' . getQS(), array("class" => "ajax")); ?>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <?php echo $selected_page ? "הוספת עמוד בן של: " . $selected_page->Arg('title') : "הוספת עמוד חדש"; ?>
                    <div class="btn-group pull-left" role="group">
                        <button class="btn btn-primary btn-xs btn-hover" type="submit">
                            הוסף
                        </button>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row">   
                        <div class="form-group col-md-4">
                            
                            <label for="page_title">כותרת</label>
                            <input id="page_title" type="text" class="form-control" name="title">
                            <?php if($langs = $this->config->item('available_lang'))foreach($langs as $lkey => $lang) { ?>
                            <label for="page_title<?php echo $lkey; ?>">כותרת (<?php echo $lang; ?>)</label>
                            <input id="page_title<?php echo $lkey; ?>" type="text" class="form-control" name="<?php echo $lkey; ?>_title">
                            <?php } ?>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="page_sort">סדר</label>
                            <input id="page_sort" type="number" class="form-control" name="sort">

                        </div>
                        <div class="form-group col-md-4">
                            <label for="page_parent">דף ראשי</label>
                            <select id="page_parent" class="form-control" name="parent_id">
                                <option value="0">דף ראשי</option>
                                <?php if($pages)foreach($pages as $row) { ?>
                                <option value="<?php echo $row->Id(); ?>"><?php echo $row->Arg('title'); ?></option>
                                <?php } ?>
                            </select>

                        </div>
<!--                        <div class="form-group col-md-3">
                            <label for="page_lang">שפה</label>
                            <select id="page_lang" class="form-control" name="lang">
                                <?php foreach(Obj::$Langs as $lkey => $lang) { ?>
                                <option value="<?php echo $lkey; ?>"><?php echo $lang; ?></option>
                                <?php } ?>
                            </select>
                        </div>-->
<!--                        <div class="checkbox col-md-3">
                            <label for="page_status">
                                <input type="checkbox" id="page_status" name="status" value="true" checked /> פעיל?
                            </label>
                        </div>-->
                    </div>
                    <div class="row"> 
                        <div class="form-group col-md-4">
                            <label for="page_controller">CONTROLLER</label>
                            <input id="page_controller" type="text" dir="ltr" class="form-control" name="controller" value="welcome">

                        </div>
                        <div class="form-group col-md-4">
                            <label for="page_method">METHOD</label>
                            <input id="page_method" type="text" dir="ltr" class="form-control" name="method" value="">

                        </div>
<!--                        <div class="form-group col-md-4">
                            <label for="page_param">PARAM</label>
                            <input id="page_param" type="text" dir="ltr" class="form-control" name="param">

                        </div>-->

                    </div>
                </div>
            </div>
        
            <?php echo form_close(); ?>
        <?php } ?>

        
        
        <?php if($selected_page) { ?>
            <?php echo form_open('admin/' . $this->router->fetch_class() . '/update_page/' . $selected_page_id . getQS(), array("class" => "ajax")); ?>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <?php echo "עדכון דף: " . $selected_page->Arg('title'); ?>
                    <div class="btn-group pull-left" role="group">
                        <button class="btn btn-success pulse-hover btn-xs" type="submit">
                            עדכן
                        </button>
                        <button type="button" class="btn pulse-hover btn-xs btn-danger delete" data-url="<?php echo base_url('admin/site/destroy_page/' . $selected_page->Id()); ?>">
                            מחק
                        </button>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="form-group col-md-4">
                            <label for="page_title">כותרת</label>
                            <input id="page_title" type="text" class="form-control" name="title" value="<?php echo $selected_page->Arg('title'); ?>">
                            <?php if($langs = $this->config->item('available_lang'))foreach($langs as $lkey => $lang) { ?>
                            <label for="page_title<?php echo $lkey; ?>">כותרת (<?php echo $lang; ?>)</label>
                            <input id="page_title<?php echo $lkey; ?>" type="text" class="form-control" name="<?php echo $lkey; ?>_title" value="<?php echo $selected_page->Arg($lkey . '_title'); ?>">
                            <?php } ?>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="page_sort">סדר</label>
                            <input id="page_sort" type="number" class="form-control" name="sort" value="<?php echo $selected_page->Number('sort'); ?>">

                        </div>
                        <div class="form-group col-md-4">
                            <label for="page_parent">דף ראשי</label>
                            <select id="page_parent" class="form-control" name="parent_id">
                                <option value="0">דף ראשי</option>
                                <?php if($pages)foreach($pages as $row) { ?>
                                <option value="<?php echo $row->Id(); ?>" <?php echo $row->Id() === $selected_page->Arg('parent_id') ? "selected" : ""; ?>><?php echo $row->Arg('title'); ?></option>
                                <?php } ?>
                            </select>
                        </div>
<!--                        <div class="form-group col-md-3">
                            <label for="page_lang">שפה</label>
                            <select id="page_lang" class="form-control" name="lang">
                                <?php foreach(Obj::$Langs as $lkey => $lang) { ?>
                                <option value="<?php echo $lkey; ?>" <?php echo $row->Arg('lang') === $lkey ? "selected" : ""; ?>><?php echo $lang; ?></option>
                                <?php } ?>
                            </select>
                        </div>-->
<!--                        <div class="checkbox col-md-3">
                            <label for="page_status<?php echo $row->Id(); ?>">
                                <input type="checkbox" id="page_status<?php echo $row->Id(); ?>" name="status" value="true" <?php echo $selected_page->Boolean('status') > 0 ? "checked" : ""; ?> /> פעיל?
                            </label>
                        </div>-->
                    </div>
                    <div class="row">
                        <div class="form-group col-md-4">
                            <label for="page_controller">CONTROLLER</label>
                            <input id="page_controller" type="text" dir="ltr" class="form-control" name="controller" value="<?php echo $selected_page->Arg('controller'); ?>">

                        </div>
                        <div class="form-group col-md-4">
                            <label for="page_method">METHOD</label>
                            <input id="page_method" type="text" dir="ltr" class="form-control" name="method" value="<?php echo $selected_page->Arg('method'); ?>">

                        </div>
<!--                        <div class="form-group col-md-3">
                            <label for="page_param">PARAM</label>
                            <input id="page_param" type="text" dir="ltr" class="form-control" name="param" value="<?php echo $selected_page->Arg('param'); ?>">

                        </div>-->
                        
                    </div> 
                </div>
            </div>
            <?php echo form_close(); ?>
        <?php } ?>
        
        <?php echo form_open_multipart('admin/' . $this->router->fetch_class() . '/put_object/' . $selected_page_id . '/' . getQS(), array("class" => "ajax")); ?>
        <div class="panel panel-primary">
            <div class="panel-heading">
                הוספת מאפיין חדש
                <div class="btn-group pull-left" role="group">
                    <button class="btn btn-primary btn-xs pulse-hover" type="submit">
                        הוסף
                    </button>
                </div>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="form-group col-md-2">
                        <label for="page_object_keyword">מזהה</label>
                        <input id="page_object_keyword" type="text" dir="ltr" class="form-control" name="keyword">
                    </div>
                    <div class="form-group col-md-3">
                        <label for="page_object_explain">שם בעברית</label>
                        <input id="page_object_explain" type="text" class="form-control" name="explain">
                    </div>
                    <div class="form-group col-md-3">
                        <label for="page_object_type">סוג השדה</label>
                        <select id="page_object_type" name="type" class="form-control">
                            <option value="" disabled selected>בחר את סוג השדה</option>
                            <?php foreach(Obj::$Field_types as $key_type => $type) { ?>
                            <option value="<?php echo isset($key_type) ? $key_type : ""; ?>"><?php echo isset($type) ? $type : ""; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label for="page_object_can_update">עדכון</label>
                        <select id="page_object_can_update" class="form-control" name="can_update">
                            <option value="1">כן</option>
                            <option value="0">לא</option>
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label for="page_object_lang">שפה</label>
                        <select id="page_object_lang" class="form-control" name="lang">
                            <option value="0">כל השפות</option>
                            <?php if($langs = $this->config->item('available_lang'))foreach($langs as $lkey => $lang) { ?>
                            <option value="<?php echo $lkey; ?>"><?php echo $lang; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <?php echo form_close(); ?>
        
        
        
        <?php if(isset($objects) && !empty($objects)) { ?>
            <?php foreach($objects as $fkey => $row) { ?>
            <?php echo form_open('admin/' . $this->router->fetch_class() . '/update_object/' . $row->Id() . '/' . getQS(), array("class" => "ajax")); ?>
            <div class="panel panel-success">
                <div class="panel-heading">
                    עריכת מאפיין <?php echo $row->Arg('explain'); ?>
                    <div class="btn-group pull-left" role="group">
                        <button class="btn btn-xs pulse-hover btn-success" type="submit">
                            עדכן
                        </button>
                        <button type="button" class="btn btn-xs pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/site/destroy_object/' . $row->Id()); ?>">
                            מחק
                        </button> 
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="form-group col-md-2">
                            <label for="page_object_keyword<?php echo $fkey; ?>">מזהה</label>
                            <input id="page_object_keyword<?php echo $fkey; ?>" type="text" dir="ltr" class="form-control" name="keyword" value="<?php echo $row->Arg('keyword'); ?>">

                        </div>
                        <div class="form-group col-md-3">
                            <label for="page_object_explain<?php echo $fkey; ?>">שם בעברית</label>
                            <input id="page_object_explain<?php echo $fkey; ?>" type="text" class="form-control" name="explain" value="<?php echo $row->Arg('explain'); ?>">

                        </div>
                        <div class="form-group col-md-3">
                            <label for="page_object_type<?php echo $fkey; ?>">סוג השדה</label>
                            <select id="page_object_type<?php echo $fkey; ?>" name="type" class="form-control">
                                <option value="" disabled selected>בחר את סוג השדה</option>
                                <?php foreach(Obj::$Field_types as $key_type => $type) { ?>
                                <option value="<?php echo isset($key_type) ? $key_type : ""; ?>" <?php echo $key_type === $row->Arg('type') ? "selected" : ""; ?>><?php echo isset($type) ? $type : ""; ?></option>
                                <?php } ?>
                            </select>

                        </div>
                        <div class="form-group col-md-2">
                            <label for="page_object_can_update<?php echo $fkey; ?>">עדכון</label>
                            <select id="page_object_can_update<?php echo $fkey; ?>" class="form-control" name="can_update">
                                <option value="1" <?php echo '1' === $row->Arg('can_update') ? "selected" : ""; ?>>כן</option>
                                <option value="0" <?php echo '0' === $row->Arg('can_update') ? "selected" : ""; ?>>לא</option>
                            </select>
                        </div>
                        <div class="form-group col-md-2">
                            <label for="page_object_lang<?php echo $fkey; ?>">שפה</label>
                            <select id="page_object_lang<?php echo $fkey; ?>" class="form-control" name="lang">
                                <option value="0">כל השפות</option>
                                <?php if($langs = $this->config->item('available_lang'))foreach($langs as $lkey => $lang) { ?>
                                <option value="<?php echo $lkey; ?>" <?php echo $lkey === $row->Arg('lang') ? "selected" : ""; ?>><?php echo $lang; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <?php if($row->Arg('type') === 'short') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'long') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="value"><?php echo $row->Arg('value'); ?></textarea>

                            </div>
                            <?php } else if($row->Arg('type') === 'html') { ?>
                            <div class="form-group ck">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="materialize-textarea ckeditor" name="value"><?php echo $row->Arg('value'); ?></textarea>

                            </div>
                            <?php } else if($row->Arg('type') === 'integer') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="number" step="1" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'double') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="number" step="0.00000001" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'datetime') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control datetime" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'date') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control date" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'time') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control time" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'image') { ?>
                            <div class="form-group cropper">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="value" accept="image/*" onchange="PreviewImage(this, 'preview<?php echo $fkey; ?>');">

                                <div dir="ltr">
                                    <?php
                                    $extra = $row->JsonToArray('extra');
                                    $width = isset($extra['width']) ? $extra['width'] : 0;
                                    $height = isset($extra['height']) ? $extra['height'] : 0;
                                    ?>
                                    <img id="preview<?php echo $fkey; ?>" class="materialboxed" src="<?php echo $row->Img('value'); ?>" alt="preview" style="max-width: 100%;" data-width="<?php echo $width; ?>" data-height="<?php echo $height; ?>" data-input-name="<?php echo $row->Arg('keyword'); ?>"/>
                                </div>
                            </div>
                            <?php } else if($row->Arg('type') === 'file') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="value">

                            </div>
                            <div class="form-group">
                                <a href="<?php echo $row->File('value'); ?>" target="_blank">צפה בקובץ  -<?php echo $row->Arg('value'); ?></a>
                            </div>
                            <?php } else if($row->Arg('type') === 'email') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="email" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'tel') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="tel" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                            </div>
                            <?php } else if($row->Arg('type') === 'video') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <input id="input_object_<?php echo $fkey; ?>" type="text" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>" onchange="PreviewVideo(this, 'videoPreview<?php echo $fkey; ?>');">

                            </div>
                            <div class="form-group">
                                <!-- 16:9 aspect ratio -->
                                <div class="embed-responsive embed-responsive-16by9">
                                    <iframe id="videoPreview<?php echo $fkey; ?>" class="embed-responsive-item" src="<?php echo $row->Arg('value'); ?>"></iframe>
                                </div>
                            </div>
                            <?php } else if($row->Arg('type') === 'boolean') { ?>
                            <div class="form-group">
                                <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="value" value="true" <?php echo $row->Arg('value') > 0 ? "checked" : ""; ?>> <?php echo $row->Arg('explain'); ?>
                                    </label>
                                </div>

                            </div>
                            <?php } else if($row->Arg('type') === 'choice') { ?>

                            <?php } else if($row->Arg('type') === 'multiple') { ?>

                            <?php } else if($row->Arg('type') === 'gallery') { ?>

                            <?php } ?>
                        </div>

                    </div>
                </div>
            </div>
            <?php echo form_close(); ?>
            <?php } ?>
        <?php } ?>
    </div>
    
    <?php } else { ?>
    <div class="col-lg-9 col-sm-12">
        <?php if(isset($objects) && !empty($objects)) { ?>
        <?php $langs = $this->config->item('available_lang'); ?>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#langglobal" aria-controls="global" role="tab" data-toggle="tab">כללי</a></li>
            <?php foreach($langs as $lkey => $lang) { ?>
            <li role="presentation" class=""><a href="#lang<?php echo $lkey; ?>" aria-controls="<?php echo $lang; ?>" role="tab" data-toggle="tab"><?php echo $lang; ?></a></li>
            <?php } ?>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="langglobal">
                <?php foreach($objects as $fkey => $row) { if($row->Arg('can_update') < 1) continue; if($row->Arg('lang')) continue; ?>
                <?php echo form_open('admin/' . $this->router->fetch_class() . '/update_object_value/' . $row->Id() . '/' . getQS(), array("class" => "ajax")); ?>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        עריכת מאפיין <?php echo $row->Arg('explain'); ?>
                        <div class="btn-group pull-left" role="group">
                            <button class="btn btn-xs pulse-hover btn-success" type="submit">
                                עדכן
                            </button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <?php if($row->Arg('type') === 'short') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'long') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="value"><?php echo $row->Arg('value'); ?></textarea>

                                </div>
                                <?php } else if($row->Arg('type') === 'html') { ?>
                                <div class="form-group ck">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="materialize-textarea ckeditor" name="value"><?php echo $row->Arg('value'); ?></textarea>

                                </div>
                                <?php } else if($row->Arg('type') === 'integer') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="number" step="1" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'double') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="number" step="0.00000001" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'datetime') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control datetime" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'date') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control date" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'time') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control time" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'image') { ?>
                                <div class="form-group cropper">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="value" accept="image/*" onchange="PreviewImage(this, 'preview<?php echo $fkey; ?>');">

                                    <div dir="ltr">
                                        <?php
                                        $extra = $row->JsonToArray('extra');
                                        $width = isset($extra['width']) ? $extra['width'] : 0;
                                        $height = isset($extra['height']) ? $extra['height'] : 0;
                                        ?>
                                        <img id="preview<?php echo $fkey; ?>" class="materialboxed" src="<?php echo $row->Img('value'); ?>" alt="preview" style="max-width: 100%;" data-width="<?php echo $width; ?>" data-height="<?php echo $height; ?>" data-input-name="<?php echo $row->Arg('keyword'); ?>"/>
                                    </div>
                                </div>
                                <?php } else if($row->Arg('type') === 'file') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="value">

                                </div>
                                <div class="form-group">
                                    <a href="<?php echo $row->File('value'); ?>" target="_blank">צפה בקובץ  -<?php echo $row->Arg('value'); ?></a>
                                </div>
                                <?php } else if($row->Arg('type') === 'email') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="email" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'tel') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="tel" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'video') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>" onchange="PreviewVideo(this, 'videoPreview<?php echo $fkey; ?>');">

                                </div>
                                <div class="form-group">
                                    <div class="embed-responsive embed-responsive-16by9">
                                        <iframe id="videoPreview<?php echo $fkey; ?>" class="embed-responsive-item" src="<?php echo $row->Arg('value'); ?>"></iframe>
                                    </div>
                                </div>
                                <?php } else if($row->Arg('type') === 'boolean') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="value" value="true" <?php echo $row->Arg('value') > 0 ? "checked" : ""; ?>> <?php echo $row->Arg('explain'); ?>
                                        </label>
                                    </div>

                                </div>
                                <?php } else if($row->Arg('type') === 'choice') { ?>

                                <?php } else if($row->Arg('type') === 'multiple') { ?>

                                <?php } else if($row->Arg('type') === 'gallery') { ?>

                                <?php } ?>
                            </div>

                        </div>
                    </div>
                </div>
                <?php echo form_close(); ?>
                <?php } ?>
            </div>
            <?php foreach($langs as $lkey => $lang) { ?>
            <div role="tabpanel" class="tab-pane" id="lang<?php echo $lkey; ?>">
                <?php foreach($objects as $fkey => $row) { if($row->Arg('can_update') < 1) continue; if($row->Arg('lang') !== $lkey) continue; ?>
                <?php echo form_open('admin/' . $this->router->fetch_class() . '/update_object_value/' . $row->Id() . '/' . getQS(), array("class" => "ajax")); ?>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        עריכת מאפיין <?php echo $row->Arg('explain'); ?>
                        <div class="btn-group pull-left" role="group">
                            <button class="btn btn-xs pulse-hover btn-success" type="submit">
                                עדכן
                            </button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <?php if($row->Arg('type') === 'short') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'long') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="value"><?php echo $row->Arg('value'); ?></textarea>

                                </div>
                                <?php } else if($row->Arg('type') === 'html') { ?>
                                <div class="form-group ck">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="materialize-textarea ckeditor" name="value"><?php echo $row->Arg('value'); ?></textarea>

                                </div>
                                <?php } else if($row->Arg('type') === 'integer') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="number" step="1" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'double') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="number" step="0.00000001" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'datetime') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control datetime" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'date') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control date" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'time') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control time" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'image') { ?>
                                <div class="form-group cropper">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="value" accept="image/*" onchange="PreviewImage(this, 'preview<?php echo $fkey; ?>');">

                                    <div dir="ltr">
                                        <?php
                                        $extra = $row->JsonToArray('extra');
                                        $width = isset($extra['width']) ? $extra['width'] : 0;
                                        $height = isset($extra['height']) ? $extra['height'] : 0;
                                        ?>
                                        <img id="preview<?php echo $fkey; ?>" class="materialboxed" src="<?php echo $row->Img('value'); ?>" alt="preview" style="max-width: 100%;" data-width="<?php echo $width; ?>" data-height="<?php echo $height; ?>" data-input-name="<?php echo $row->Arg('keyword'); ?>"/>
                                    </div>
                                </div>
                                <?php } else if($row->Arg('type') === 'file') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="value">

                                </div>
                                <div class="form-group">
                                    <a href="<?php echo $row->File('value'); ?>" target="_blank">צפה בקובץ  -<?php echo $row->Arg('value'); ?></a>
                                </div>
                                <?php } else if($row->Arg('type') === 'email') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="email" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'tel') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="tel" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>">

                                </div>
                                <?php } else if($row->Arg('type') === 'video') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <input id="input_object_<?php echo $fkey; ?>" type="text" dir="ltr" class="form-control" name="value" value="<?php echo $row->Arg('value'); ?>" onchange="PreviewVideo(this, 'videoPreview<?php echo $fkey; ?>');">

                                </div>
                                <div class="form-group">
                                    <div class="embed-responsive embed-responsive-16by9">
                                        <iframe id="videoPreview<?php echo $fkey; ?>" class="embed-responsive-item" src="<?php echo $row->Arg('value'); ?>"></iframe>
                                    </div>
                                </div>
                                <?php } else if($row->Arg('type') === 'boolean') { ?>
                                <div class="form-group">
                                    <label for="input_object_<?php echo $fkey; ?>"><?php echo $row->Arg('explain'); ?></label>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="value" value="true" <?php echo $row->Arg('value') > 0 ? "checked" : ""; ?>> <?php echo $row->Arg('explain'); ?>
                                        </label>
                                    </div>

                                </div>
                                <?php } else if($row->Arg('type') === 'choice') { ?>

                                <?php } else if($row->Arg('type') === 'multiple') { ?>

                                <?php } else if($row->Arg('type') === 'gallery') { ?>

                                <?php } ?>
                            </div>

                        </div>
                    </div>
                </div>
                <?php echo form_close(); ?>
                <?php } ?>
            </div>
            <?php } ?>
        </div>
        <?php } ?>
    </div>
    <?php } ?>
</div>