<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Land<PERSON>pi extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    public function landCheck() {
        
        //https://sherut-leumi.wdev.co.il/api/LandApi/landCheck
        
        //$output['tz'] = $this->input->post('tz', true);
        $this->load->helper('string');
        $random = random_string('numeric', 8);
        
        $password = $random;
        //$password = '12345678';
        
        $getPosts = array(
            'IDNO' => $this->input->post('tz', true),
            'Password' => $password,
            'FirstName' => $this->input->post('name', true),
            'LastName' => $this->input->post('surname', true),
            'Mobile' => $this->sherutLeumi->cleanPhone($this->input->post('phone', true)),
            
            'Email' => $this->input->post('email', true),
            'text' => $this->input->post('text', true),
            'divur'=> $this->input->post('divur', true) ? '1' : '0',
            'utm_source'=> $this->input->post('utm_source', true),
            'utm_medium'=> $this->input->post('utm_medium', true),
            
            'BirthDate' => '',
            'CityCode' => '',
            'PrvSchool' => $this->input->post('school', true),
            'sex' => '',
            'Category' => '',
            'YearYad' => ''            
            
        );
        
        $output['responseClient'] = $this->sherutLeumi->ApiClient($url = 'v2/volunteer/registerSend', $getPosts);
        $output['error'] = false;
        
        $getPosts['response'] = json_encode($output['responseClient']);
        
        $insertLid = $this->insertLid($getPosts);
        
        
        if(!isset($output['responseClient']['SessionKey'])) {
            
            $output['responseClient']['SessionKey'] = '';
            $output['error'] = '1';
            
        }
        
        if( isset($output['responseClient']['ErrorMessage']) && 
            $output['responseClient']['ErrorMessage'] == 'משתמש קיים - סיסמה שגויה' ) {
            
            $output['error'] = '2';
        }
        
        
        
        if($insertLid && isset($output['responseClient']['SessionKey']) && !empty($output['responseClient']['SessionKey']) ) {
            
            $message = 'היי '.$getPosts['FirstName'].'. ';
            $message .= 'תודה שנרשמת לאגודה להתנדבות.';
            
            $message .= ' '.'לכניסה בפעם הבאה: ';
            $message .= 'שם משתמש: '.$getPosts['IDNO'].' | ';
            $message .= 'סיסמה: '.$getPosts['Password'];
            $message .= ' | '.'קישור: '.'https://bit.ly/3HNykYv';
            $message .= ' | '.'לכל שאלה ניתן לפנות אלינו בטלפון 1800233133';
            
            $sms = $this->sherutLeumi->sendSMS($getPosts['Mobile'], $message );
            
        }
                
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function insertLid($pagePosts) {
        
        $this->db->select('IDNO');
        $this->db->from('leadsLandpage');
        $this->db->where('IDNO',$pagePosts['IDNO']);
        $result= $this->db->get();
        
        if(!$result->num_rows()) {
            
            $this->sendLeadManager($pagePosts);
            
            $data = array(
                    'status' => 1,
                    'created_at' => date("Y-m-d H:i:s"),

                    'FirstName' => $pagePosts['FirstName'],
                    'LastName' => $pagePosts['LastName'],
                    'IDNO' => $pagePosts['IDNO'],
                    'Mobile' => $pagePosts['Mobile'],
                    'Email' => $pagePosts['Email'],
                    'text' => $pagePosts['text'],
                    'divur' => $pagePosts['divur'],
                    'utm_source'=> $pagePosts['utm_source'],
                    'utm_medium'=> $pagePosts['utm_medium'],
                    'PrvSchool'=> $pagePosts['PrvSchool'],
                    'response' => $pagePosts['response']

            );

            $insert = $this->db->insert('leadsLandpage', $data); 
            $insert_id = $this->db->insert_id();

            return $insert_id;
            
        } else {
            
            return false;
            
        }
        
    }
    
    
    public function sendLeadManager($values = false) {
        
        
        /* Lead Managaer Logic */
        $ch = curl_init();
        $postfields = array(
                
                'phone' => $values['Mobile'],
                'email' => $values['Email'],
            
                'first_name' => $values['FirstName'],
                'last_name' => $values['LastName'],
            
                'id' => $values['IDNO'],
                'school' => $values['PrvSchool'],
                'remark' => $values['text'],
                'divur' =>  $values['divur'],
            
                'utm_medium' => $values['utm_medium'], // ספק
                'platform' => $values['utm_source'].'_'.$values['utm_medium'],
                'lm_supplier' => $values['utm_medium'], // ספק,
                'lm_bc'  => $values['utm_medium'] // {כלי פרסומי}
         );

         $sendUrl = 'https://api.leadmanager.co.il/handlers/lm/submit.cms?lm_form=60256&lm_key=e750491d8dcc4cae8196c40aff48240f';

         // open a curl connection
         $url = $sendUrl. '&' . http_build_query($postfields);

         //echo $url;

        curl_setopt($ch, CURLOPT_URL,$url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS,"");

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $server_output = curl_exec ($ch);

        $result1 = json_decode($server_output, TRUE);
        //print_r($result1);

        curl_close ($ch);
        
//        if($values['utm_medium'] === 'test') {
//            print_r($result1);
//            die();
//        };
        
        
    }
    
    
    public function leadsCvs() {
        
        //https://sherut-leumi.wdev.co.il/api/landApi/leadsCvs?token=toK3en7105964seb
        
        //$bit = $this->sherutLeumi->getBitlyUrl('https://sherut-leumi.wdev.co.il');
        //die($bit);
        
        $token = $this->input->get('token');
        if($token !== 'toK3en7105964seb') {die('CODE ERROR');}
        
            $this->db->select('*');
            $this->db->from('leadsLandpage');
            $this->db->where('status', 1);
            
            $this->db->order_by('created_at', 'ASC');
            
            $result= $this->db->get();
            $data = $result->result_array();
            
            if(!empty($data) ) {
                
                $csv = array();
        
                $csv[] = array (
                    '9' => 'תאריך',
                    '1' => 'שם פרטי',
                    '2' => 'שם משפחה',
                    '3' => 'נייד',
                    '4' => 'מייל',                    
                    '5' => 'תעודת זהות',
                    '6' => 'בית ספר',
                    '7' => 'הערות',
                    '8' => 'דיוור?',
                    '10' => 'utm_source',
                    '11' => 'utm_medium',
                );
                
                
                
                //echo "<pre>";
                //print_r($data);die();
                
                foreach ($data as $value) {
                    
                    $csv[] = array (
                        '9' => changeDateFormat($value['created_at'], 'Y-m-d H:i:s', 'd-m-Y H:i'),
                        '1' => $value['FirstName'],
                        '2' => $value['LastName'],
                        '3' => $value['Mobile'],
                        '4' => $value['Email'],
                        '5' => $value['IDNO'],
                        '6' => $value['PrvSchool'],
                        '7' => $value['text'],
                        '8' => $value['divur'] == '1' ? 'כן' : 'לא',
                        '10' => $value['utm_source'],
                        '11' => $value['utm_medium']
                    );
                    
                }
                
                $data = $csv;
                $month = date('M');  //date('m');
                $filename = 'leads_'.$month.'_'.date('Y').'_'.rand(1,9999);
                
                header('Content-Encoding: UTF-8'); 
                header('Content-type: text/csv; charset=UTF-8');
                header('Content-Disposition: attachment; filename='.$filename.'.csv');
                header("Pragma: no-cache");
                header("Expires: 0");

                $handle = fopen('php://output', 'w');
                fwrite($handle, "\xEF\xBB\xBF");


                foreach ($data as $data_array) {
                    fputcsv($handle, $data_array);
                }
                    fclose($handle);
                exit;
                
                //echo "<pre>";
                //print_r($csv);
                
            }
        
        
    }
    
}