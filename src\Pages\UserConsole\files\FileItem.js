/**
 * FileItem - קומפוננט להצגת מסמך בודד במערכת
 * 
 * מציג את מצב המסמך (Missing/Pending/Exist) ומאפשר העלאה של קובץ חדש
 * כאשר הסטטוס הוא Exist, כפתור ההעלאה מושבת
 * 
 * Props:
 * - formData: נתוני הטופס (FormID, Status, FormType, FormName, Msg)
 * - texts: טקסטים להצגה
 * - noHistoryForm: האם להציג כטופס ללא היסטוריה
 * - allowReupload: האם לאפשר העלאה מחדש
 * - countDaysFirst: האם נדרש לבחור תאריכים (למשל באישור מחלה)
 */
import React, { useState, useRef } from 'react';
import fileBgQuad from '../../../img/sherut-leumi/svg/files/fileBgQuad.png';
import v from '../../../img/sherut-leumi/svg/files/v.svg';
import x from '../../../img/sherut-leumi/svg/files/x.svg';
import UploadFile from './UploadFile';
import { Button } from '@mui/material';
import { Grid } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

export default function FileItem({
    formData,
    texts,
    noHistoryForm,
    allowReupload,
    title,
    countDaysFirst,
    userJ,
    ...props
}) {
    const [showUpload, setShowUpload] = useState(false);
    const displayTitle = title || texts?.title;
    const processIdRef = useRef(Date.now());
    
    console.log('FileItem rendering', {
        formID: formData?.FormID,
        status: formData?.Status,
        allowReupload,
        hasTexts: !!texts,
        hasUserJ: !!userJ
    });

    const handleReuploadClick = () => {
        processIdRef.current = Date.now();
        setShowUpload(true);
    };

    const renderUploadButton = () => {
        console.log('renderUploadButton called:', {
            formID: formData?.FormID,
            status: formData?.Status,
            formIDType: typeof formData?.FormID,
            allowReupload
        });
        
        // Only block re-upload for FormID1 when status is Exist
        if (formData?.Status === "Exist" && formData?.FormID === "1") {
            console.log('Blocking upload for FormID1');
            return null;
        }

        return (
            <div className='flex flex justify-center'>
                <button
                    onClick={handleReuploadClick}
                    className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                >
                    העלאת קובץ
                    <CloudUploadIcon />
                </button>
            </div>
        );
    };

    if (!formData || !formData.FormID) {
        console.error("FileItem missing critical formData", {formData});
        return (
            <div className="bg-white rounded-lg shadow-md p-6 mb-4">
                <div className="mb-4">
                    <h3 className="text-xl font-semibold mb-2">{displayTitle || "מסמך"}</h3>
                    <p className="text-gray-600">{texts?.filesType || ""}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-md">
                    <p className="text-red-600">שגיאה: מידע הטופס חסר, אנא רענן את הדף</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-lg shadow-md p-6 mb-4">
            <div className="mb-4">
                <h3 className="text-xl font-semibold mb-2 flex items-center gap-2">
                  
                    <span>{displayTitle}</span>
                </h3>
                <p className="text-gray-600">{texts?.filesType}</p>
            </div>

            {countDaysFirst && formData.Status === "Exist" && !showUpload ? (
                <div className="bg-blue-50 p-6 rounded-lg flex flex-col items-center justify-center">
                    <img src={v} alt='המסמך קיים במערכת' className="w-12 h-12 mb-4" />
                    <p className="text-blue-800 text-center mb-4">{'המסמך אושר, ניתן להעלות מסמך חדש במידת הצורך'}</p>
                    {allowReupload && renderUploadButton()}
                </div>
            ) : countDaysFirst && showUpload ? (
                <UploadFile 
                    {...props} 
                    formData={formData}
                    texts={texts}
                    userJ={userJ}
                    countDaysFirst={countDaysFirst} 
                    customTitle={displayTitle} 
                    onCancel={() => setShowUpload(false)}
                    processId={processIdRef.current}
                    key={`upload-${processIdRef.current}`}
                    isReupload={true}
                    refreshForms={props.refreshForms}
                />
            ) : noHistoryForm ? (
                <UploadFile 
                    {...props} 
                    formData={formData}
                    texts={texts}
                    userJ={userJ}
                    customTitle={displayTitle}
                    processId={processIdRef.current}
                    key={`upload-${processIdRef.current}`}
                    isReupload={false}
                    refreshForms={props.refreshForms}
                />
            ) : showUpload ? (
                <UploadFile 
                    {...props} 
                    formData={formData}
                    texts={texts}
                    userJ={userJ}
                    customTitle={displayTitle} 
                    onCancel={() => setShowUpload(false)}
                    processId={processIdRef.current}
                    key={`upload-${processIdRef.current}`}
                    isReupload={true}
                    refreshForms={props.refreshForms}
                />
            ) : formData.Status === "Pending" ? (
                <div className="bg-yellow-50 p-6 rounded-lg flex flex-col items-center justify-center">
                    <img src={v} alt='המסמך הועלה וממתין לאישור' className="w-12 h-12 mb-4" />
                    <p className="text-yellow-800 text-center mb-4">
                        המסמך הועלה וממתין לאישור 
                    </p>
                    {allowReupload && renderUploadButton()}
                </div>
            ) : formData.Status === "Missing" ? (
                <div className="bg-red-50 p-6 rounded-lg">
                    <div className="flex flex-col items-center justify-center">
                        <img src={x} alt="המסמך לא קיים" className="w-12 h-12 mb-4" />
                        <p className="text-red-800 text-center mb-4">{'המסמך לא קיים'}</p>
                        {renderUploadButton()}
                    </div>
                </div>
            ) : (
                <div className="bg-green-50 p-6 rounded-lg flex flex-col items-center justify-center">
                    <img src={v} alt='המסמך קיים במערכת' className="w-12 h-12 mb-4" />
                    <p className="text-green-800 text-center mb-4">{'המסמך קיים במערכת'}</p>
                    {renderUploadButton()}
                </div>
            )}
        </div>
    );
}
