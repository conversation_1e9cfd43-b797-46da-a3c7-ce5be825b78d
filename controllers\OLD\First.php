<?php defined('BASEPATH') OR exit('No direct script access allowed');

class First extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+asd';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+asdseb-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
        
        $this->load->model('msiteWs');
        $this->load->model('foreignWorkers');
        
        $controller = 'first';
        
        $this->data['current_language'] = 'he';
        $this->data['controller'] = $this->router->fetch_class();
        $this->data['method'] = $this->router->fetch_method();
        $this->data['param'] = $param;
        $this->data['settings'] = $this->msiteWs->get_settings();
        $this->data['pages'] = $this->msite->get_all_pages($parent_id = 0, $lang = NULL, $controller);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages'][$controller.'_index'];
        
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id(),$this->data['current_language']);
        
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }

//        
        
        
    }
    
    public function SiteDataItems ($param = FALSE) {
        $this->_loaderWS($param);
        
        $output['pages'] =  $this->msiteWs->get_pagesWs($this->data['pages']);
        $output['settings'] = $this->data['settings'];
        
        $output['SystemData'] = $this->msiteWs->get_page_with_objectsArray(39,$this->data['current_language']);
        
        if($this->input->get('searchDataMenuItems',TRUE) == 1) {
            $output['searchDataMenu'] = $this->foreignWorkers->getClientsWorkers4Search();
        }
        
        
        //$output['contactData'] = $this->msiteWs->get_page_with_objectsArray(16,$this->data['current_language']);
        
        //GET PAGES WITH ID!!!
        //$output['paramPages'] = $this->msiteWs->getParamPages();
        
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function index($param = FALSE) {
        $this->_loaderWS($param);
        
        
        $output['dataSummary'] = $this->foreignWorkers->DataSummary(false);
       
        $test = $output['dataSummary'];
        //$output['checkDb'] = $this->foreignWorkers->is_DoubleInsert($table = 'first_ForeignWorkers');
        
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
//            $this->msite->set_where("status='1'");
//            $this->msite->sort_objects("sort", "DESC");
//            $first_branches1 = $this->msite->get_all_objects('first_branches');
//            $output['gallery'] = $this->msiteWs->objects_to_ArrayNoseoData($first_branches1);
            
            
            $output['data'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        //print_r($this->data['page']); die('asd');
        if(isset($_GET['sebas'])) {
            $output = $test;
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    public function loginUser ($param = FALSE) {
          
        $this->_loaderWS($param);
                
        //https://stackoverflow.com/questions/40903355/how-to-verify-jwt-token-in-codeigniter-3-x-for-every-request-from-client
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('username','password'));
        
        //echo md5('sebas'); die();
        // username= 'SuperAdmin'; $password='SuperAdmin777';
        // username= 'user'; $password='user13';
        // username= 'admin'; $password='sebas';
        
        if( !empty($jsonPosts['username']) && !empty($jsonPosts['password']) ) {
            
                
                $this->db->select('*');
                $this->db->from('first_Users');
                $this->db->where('username', $jsonPosts['username']);
                $this->db->where('passwordMd5', md5($jsonPosts['password']));
                $result= $this->db->get();

                $user = $result->row_array();
                
                
                if(!empty($user)) {
                    
                    $this->db->select('*');
                    $this->db->from('first_userType');
                    $this->db->where('id', $user['userType']);
                    $result= $this->db->get();
                    $credential = $result->row_array();
                    
                    $userCredential = $credential['name']; //superAdmin //admin //user

                    $output['data'] = array(
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'userCredential' => $userCredential,
                        'token' => JWT::encode($user['id'], $this->data['usersCode']),
                        'dateTimeLogin' => date("Y-m-d H:i:s")
                    );

                } else {
                    $output['error'] =  'שם משתמש או סיסמה לא נכון';
                }
                
                
                
        } else {
            //set_status_header(500);
            $output['error'] = 'נא הכניס שם משתמש וסיסמה';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    
    public function logoutUser ($param = FALSE) {
        
        //$this->_loaderWS($param);
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId'));
        
        
        if( !empty($jsonPosts['userId']) ) {
            
                $output = 'logOut';
                
        } else {
            //set_status_header(500);
            $output = 'error NO-DATA';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function checkPage ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
//            $this->msite->set_where("status='1'");
//            $this->msite->sort_objects("sort", "DESC");
//            $first_branches1 = $this->msite->get_all_objects('first_branches');
//            $output['gallery'] = $this->msiteWs->objects_to_ArrayNoseoData($first_branches1);
            
            
            $output['data'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        //print_r($this->data['page']); die('asd');
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function clients ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        
        $output['data'] = 'OK';
        $output['page'] = $this->data['page']->Arr();
        
        if( $param && (empty($this->input->get()) || $this->input->get('sebas')) )  {
            
            
            $output['form'] = $this->foreignWorkers->getformFields($object = 'first_seniors');
            // rowsOption4SelectClient
            $output['rowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['form']);;
            
            $output['contactsSeniors'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_contactsSeniors',$contactSenior = TRUE);
            $output['formContacts'] = $this->foreignWorkers->getformFields($object = 'first_contactsSeniors');
            
            $output['medicalStatus'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_medicalStatus');
            $output['formMedicalStatus'] = $this->foreignWorkers->getformFields($object = 'first_medicalStatus');
            
            $output['hospitalization'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_hospitalization');;
            $output['formHospitalization'] = $this->foreignWorkers->getformFields($object = 'first_hospitalization');
            
            $output['placementData'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_placement');
            
            $output['formPlacement'] = $this->foreignWorkers->getformFields($object = 'first_placement',$excludeFieldsArray = array('toDate'));
            $output['formPlacementrowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['formPlacement']);

            //$output['formPlacementrowsOption4Select'] =  $this->foreignWorkers->getSelectedPlacement($output['formPlacement'], $param);
            
            
            $output['clientRequestsData'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_clientRequests');
            $output['formClientRequests'] = $this->foreignWorkers->getformFields($object = 'first_clientRequests');
            $output['formPClientRequestsRowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['formClientRequests']);
            
            
            $output['first_financingData'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_financing');
            $output['formFinancing'] = $this->foreignWorkers->getformFields($object = 'first_financing');
            $output['formFinancingRowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['formFinancing']);
            
            
            //$output['reportsData'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_reports');
            $output['formReports'] = $this->foreignWorkers->getformFields($object = 'first_reports');
            $output['reportsRowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['formReports']);
            
            
            //GET REPORT HISTORY
            $this->msite->set_where("status='1'");
            $this->msite->set_where("seniorId='$param'");
            $this->msite->sort_objects("dateReport", "DESC");
            $reportHistory = $this->msite->get_all_objects('first_reports');
            
            $output['reportHistory'] = $this->msiteWs->objects_to_ArrayNoseoData($reportHistory);
            
            //GET ALL STATUS FOR SELECT -> CHANGE
            $this->msite->set_where("status='1'");
            $this->msite->sort_objects("sort", "DESC");
            $tasksStatus = $this->msite->get_all_objects('first_taskStatus');
            $output['tasksStatus'] = $this->msiteWs->objects_to_ArrayNoseoData($tasksStatus);
            
            //GET ALL taskTypes FOR SELECT -> CHANGE
            $this->msite->set_where("status='1'");
            $this->msite->sort_objects("sort", "DESC");
            $taskTypes = $this->msite->get_all_objects('first_task_type');
            $output['taskTypes'] = $this->msiteWs->objects_to_ArrayNoseoData($taskTypes);
            
            // DATA SENIOR BY PARAM Id
            $this->msite->set_where("status='1'");
            //$this->msite->set_where("seniorStatus !='מוסתר'");
            $dataParam = $this->msite->get_object('first_seniors', $param);
            $dataParamArray = $this->msiteWs->object_to_Array($dataParam);
            
            
            $output['dataParam'] = $dataParamArray['data'];
            $output['param'] = $param;
            
        }
        
        else if ($this->input->get('noIDNewclient')==1) {
            
            $output['form'] = $this->foreignWorkers->getformFields($object = 'first_seniors');
            $output['rowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['form']);
            
        }
        
        
        if($this->input->get('update', TRUE)==1) { // FOR AUTO ADMIN FORMS
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $jsonPostsUpdate = $this->msiteWs->getPostFromJson(array('inputId','inputName','inputValue','formType'));
                
                if( !empty($jsonPostsUpdate['inputId']) && !empty($jsonPostsUpdate['inputName']) ) {
                    $params = array (
                      $jsonPostsUpdate['inputName'] => $jsonPostsUpdate['inputValue']
                    );
                    
                    $table = 'first_seniors';
                    $this->db->where('id', $jsonPostsUpdate['inputId']);
                    $output['OK_update'] = $this->db->update($table, $params).' | '.$jsonPostsUpdate['formType'] ; 
                } else {
                    $output['OK_update'] = 'FALSE';
                }
                
            } else {
                $output = $checkUserCredentials;
            }
            
            
        }
        
        //FOR MULTIPLE CONTACTS
        else if($this->input->get('update', TRUE)==2) {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $jsonPostsUpdate = $this->msiteWs->getPostFromJson(array('inputId','inputName','inputValue','formType'));
                
                if( !empty($jsonPostsUpdate['inputId']) && !empty($jsonPostsUpdate['inputName']) ) {
                    $params = array (
                      $jsonPostsUpdate['inputName'] => $jsonPostsUpdate['inputValue']
                    );
                    
                    $table = 'first_contactsSeniors';
                    $this->db->where('id', $jsonPostsUpdate['inputId']);
                    $output['OK_update'] = $this->db->update($table, $params).' | '.$jsonPostsUpdate['formType'] ; 
                } else {
                    $output['OK_update'] = 'FALSE';
                }
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        else if( $this->input->get('updateSectionTable', TRUE)==1 ) {
            
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','tableUpdate'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $table = $jsonPosts['tableUpdate'];
                
                if( $table != 'first_hospitalization' &&
                    $table != 'first_medicalStatus' &&
                    $table != 'first_placement' &&
                    $table != 'first_clientRequests' &&
                    $table != 'first_financing'
                ) {
                    die ('error-table');
                }
                
                $jsonPostsUpdate = $this->msiteWs->getPostFromJson(array('inputId','inputName','inputValue','formType'));
                
                if( !empty($jsonPostsUpdate['inputId']) && !empty($jsonPostsUpdate['inputName']) ) {
                    $params = array (
                      $jsonPostsUpdate['inputName'] => $jsonPostsUpdate['inputValue']
                    );
                    
                    
                    $this->db->where('id', $jsonPostsUpdate['inputId']);
                    $output['OK_update'] = $this->db->update($table, $params).' | '.$jsonPostsUpdate['formType'] ; 
                } else {
                    $output['OK_update'] = 'FALSE';
                }
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        else if( $this->input->get('insertClient', TRUE) == 1 ) {
            
            
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $tableInsert ='first_seniors';
                
                    $jsonDataPosts = $this->msiteWs->getPostFromJson(
                            array('name',
                                'surname',
                                'tz',
                                'sex',
                                'seniorStatus',
                                'religion',
                                'birthday',
                                'address',
                                'city',
                                'countryArea',
                                'phone',
                                'phoneSMS',
                                'license_exist',
                                'license_fromDate',
                                'license_toDate'
                                )
                            );
                
                
                    if(!$this->foreignWorkers->is_DoubleInsert($tableInsert)) {
                
                        $data = array(
                            'lang' => NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'name' => $jsonDataPosts['name'],
                            'surname' => $jsonDataPosts['surname'],
                            'seniorStatus' => $jsonDataPosts['seniorStatus'],
                            'religion' => $jsonDataPosts['religion'],
                            'birthday' => !empty($jsonDataPosts['birthday']) ? $jsonDataPosts['birthday'] : date("Y-m-d H:i:s"),
                            'address' => $jsonDataPosts['address'],
                            'city' => $jsonDataPosts['city'],
                            'countryArea' => $jsonDataPosts['countryArea'],
                            'phone' => $jsonDataPosts['phone'],
                            'phoneSMS' => $jsonDataPosts['phoneSMS'],
                            'license_exist' => $jsonDataPosts['license_exist'],
                            'license_fromDate' => !empty($jsonDataPosts['license_fromDate']) ? $jsonDataPosts['license_fromDate'] : date("Y-m-d H:i:s"),
                            'license_toDate' => !empty($jsonDataPosts['license_toDate']) ? $jsonDataPosts['license_toDate'] : date("Y-m-d H:i:s")
                        );
    //
    //
                        $insert = $this->db->insert($tableInsert, $data); 
                        $insert_id = $this->db->insert_id(); 

                        $data_seo = array(
                            'title' => 'לקוחות',
                            'description' => 'מערכת עובדים זרים',
                            'friendly' =>  'לקוח'. '_' . $insert_id,
                            'controller' => $this->router->fetch_class(),
                            'method' => $this->router->fetch_method(),
                            'param' => $insert_id,
                            'robots' => '',
                            'image' => '',
                            'canonical' => base_url().'/'.$this->router->fetch_class() . '/' . $this->router->fetch_method() . '/' . $insert_id,
                        );

                        $seo_id = $this->msite->insert_object(Msite::TABLE_SEO, $data_seo); 
                        $this->msite->update_object($tableInsert, $insert_id, array('seo_id' => $seo_id));

                        $output['insertId'] = $insert_id;
                    } else {
                        $output['insertId'] = $this->msite->get_max($tableInsert, 'id');
                    }
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        
        
        
        
        else if(!empty ($this->input->get('insertSection', TRUE))) {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized' && !empty($param)) {
                
                    
                    
                    $tableInsert = $this->input->get('insertSection', TRUE);
                    
                    //$output['table'] = $tableInsert;
                    
                    $this->msite->set_where("seniorId='$param'");
                    $sectionExist = $this->msite->get_all_objects($tableInsert);
                    
                    if(empty($sectionExist)) {
                        
                        
                        
                        if(!$this->foreignWorkers->is_DoubleInsert($tableInsert)) {
                            $data = array(
                                'lang' => NULL,
                                'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                                'status' => 1,
                                'created_at' => date("Y-m-d H:i:s"),
                                'seniorId' => $param
                            );


                            $insert = $this->db->insert($tableInsert, $data); 
                            $output['insertId'] = $this->db->insert_id(); 
                        } else {
                            $output['insertId'] = $this->msite->get_max($tableInsert, 'id'); 
                        }
                    }
                    
                    else {
                        $output['sectionExist'] = 'sectionExist';
                        $output['insertId'] = 'DONE';
                    }
                    
                //$output['add'] = $this->input->get('insertSection', TRUE);
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        // FOR LINKS AND HOSPITAL HISTORY!!!
        else if(!empty ($this->input->get('changeStatus', TRUE))) {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized' && !empty($param)) {
                
                    $tableInsert = $this->input->get('changeStatus', TRUE);
                    
                   
                    
                    if($tableInsert == 'first_placement') {
                        
                        $this->db->select('id');
                        $this->db->from($tableInsert);
                        $this->db->where('seniorId', $param);
                        $this->db->where('status', 1);
                        $result= $this->db->get();
                        $resulRow = $result->row_array();
                        
                        $params = array (
                                'status' => 0,
                                'toDate' => date("Y-m-d H:i:s"),
                        );
                        
                        $this->db->where('seniorId', $param);
                        $this->db->where('status', 1);
                        
                        $output['OK_update'] = $this->db->update($tableInsert, $params);
                        
                    }
                    
                    else {
                        $params = array (
                                'status' => 0,
                        );
                        $this->db->where('seniorId', $param);
                        $this->db->where('status', 1);
                        $output['OK_update'] = $this->db->update($tableInsert, $params);
                    }
                    
                    
                    
                    if(!empty($output['OK_update'])) {
                        
                        if($tableInsert == 'first_hospitalization') {
                            $customField = 'dateExit';
                        } else if($tableInsert == 'first_placement') {
                            $customField = 'inscriptionDate';
                        }
                        
                        if(!$this->foreignWorkers->is_DoubleInsert($tableInsert)) {
                            $data = array(
                                'lang' => NULL,
                                'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                                'status' => 0,
                                'created_at' => date("Y-m-d H:i:s"),
                                'seniorId' => $param,
                                $customField => date("Y-m-d H:i:s")
                            );


                            $insert = $this->db->insert($tableInsert, $data); 
                            $output['insertId'] = $this->db->insert_id(); 
                        } else {
                            $output['insertId'] = $this->msite->get_max($tableInsert, 'id'); 
                        }
                          
                        $this->db->where('id', $output['insertId']);
                        $output['OK_update'] = $this->db->update($tableInsert, array ('status' => 1));
                        
                    }
                    
                    
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        
        else if($this->input->get('insertContact', TRUE)==1) {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                if(!empty($param)) {
                    
                    $tableInsert = 'first_contactsSeniors';

                    if(!$this->foreignWorkers->is_DoubleInsert($tableInsert)) {
                    
                        $data = array(
                            'lang' => NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'seniorId' => $param
                        );

                        $insert = $this->db->insert($tableInsert, $data); 
                        $output['insertId'] = $this->db->insert_id(); 
                    } else {
                        $output['insertId'] = $this->msite->get_max($tableInsert, 'id');
                    }
                    
                    //$output['OK_insert'] = 'INSERT: '.$this->db->insert_id().' for Id: '.$param;
                } else $output['insert_error'] = 'error NO id'; 
                
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        else if($this->input->get('delette', TRUE)=='contact') {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','rowId'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                if(!empty($jsonPosts['rowId'])) {
                    
                    $tableDelette = 'first_contactsSeniors';
                    
                    $this->db->where('id', $jsonPosts['rowId']);
                    $this->db->delete($tableDelette);
                }
                
                $output['delette'] = "deletteContact ".$jsonPosts['rowId'];
            }
            else {
                $output = $checkUserCredentials;
            }
        }
        
        else if($this->input->get('delette', TRUE)=='hospital') {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','rowId'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $tableDelette = 'first_hospitalization';

                if(!empty($jsonPosts['rowId'])) {
                
                    $this->db->where('seniorid', $jsonPosts['rowId']);
                    $this->db->where('status', 1);
                    $this->db->delete($tableDelette);
                    
                    $output['delette'] = "deletteHospital";
                }
            }
            else {
                $output = $checkUserCredentials;
            }
        }
        
        elseif (!$param){
            
           
            //GET countryAreas for SearchClients
            $this->msite->set_where("status='1'");
            $countryAreas = $this->msite->get_all_objects('first_countryAreas');
            $output['countryAreas'] = $this->msiteWs->objects_to_ArrayNoseoData($countryAreas);
            
            
            if(!empty($this->input->get('q', TRUE)) || !empty($this->input->get('search', TRUE)) ) {
                
                $searchFlag = false;
                
                if(!empty($this->input->get('statusClientSearch', TRUE))) {
                
                    $search = $this->input->get('statusClientSearch', TRUE);
                    $this->msite->set_where("seniorStatus='$search'");
                    $searchFlag = true;
                }
                
                if(!empty($this->input->get('countryAreasSearch', TRUE))) {
                
                    $search = $this->input->get('countryAreasSearch', TRUE);
                    $this->msite->set_where("countryArea='$search'");
                    $searchFlag = true;
                }
                
                if(!empty($this->input->get('licenseSearch', TRUE))) {
                
                    $search = $this->input->get('licenseSearch', TRUE);
                    $curr_date = date("Y-m-d");
                    
                    if($search=='בתוקף') {
                        $this->msite->set_where("license_toDate >= '$curr_date' ");
                    } else {
                        $this->msite->set_where("license_toDate <= '$curr_date' ");
                    }
                    $searchFlag = true;
                    
                }
                
                $q = $this->input->get('q', TRUE);
                
                if(!empty($q)) {
                    $this->msite->set_where_like(['name', 'surname', 'tz', 'address', 'phone', 'phoneSMS', 'city'],
                    $q);
                    
                    $searchFlag = true;
                }
                
                $clients = '';
                
                if($searchFlag) {
                    $this->msite->set_where("status='1'");
                    $this->msite->sort_objects("name", "ASC");
                    $clients = $this->msite->get_all_objects('first_seniors', NULL, array('id','name','surname','phone','phoneSMS','seniorStatus','license_toDate'));
                }
                
                if(!empty($clients)) {
                    $output['clientsList'] = $this->msiteWs->objects_to_ArrayNoseo($clients);
                } else {
                    $output['clientsList'] = array();
                }
                    
                
            } else {
                $output['clientsList'] = array();
            }
            
            
            if(empty($output['clientsList'])) {
                $output['clientsList'] = array();
            }
            
            
            
        }

               
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    
    public function foreignWorkers ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        
        $output['data'] = 'OK';
        $output['page'] = $this->data['page']->Arr();
        
        
        if($param && empty($this->input->get()) ) {
            
                $output['form'] = $this->foreignWorkers->getformFields($object = 'first_ForeignWorkers');
                // rowsOption4SelectClient
                $output['rowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['form']);;
                
                //GET ALL STATUS FOR SELECT -> CHANGE
                $this->msite->set_where("status='1'");
                $this->msite->sort_objects("sort", "DESC");
                $tasksStatus = $this->msite->get_all_objects('first_taskStatus');
                $output['tasksStatus'] = $this->msiteWs->objects_to_ArrayNoseoData($tasksStatus);

                //GET ALL taskTypes FOR SELECT -> CHANGE
                $this->msite->set_where("status='1'");
                $this->msite->sort_objects("sort", "DESC");
                $taskTypes = $this->msite->get_all_objects('first_task_type');
                $output['taskTypes'] = $this->msiteWs->objects_to_ArrayNoseoData($taskTypes);
            
                // DATA WORKER BY PARAM Id
                $this->msite->set_where("status='1'");
                $dataParam = $this->msite->get_object('first_ForeignWorkers', $param);
                $dataParamArray = $this->msiteWs->object_to_Array($dataParam);
                
                //changeOuto ותק שהות
                $dataParamArray['data']['stayZman'] = $this->foreignWorkers->getStayZman($dataParamArray['data']);
                
                // DATA linkedClients BY PARAM Id
                $output['linkedClients'] = $this->foreignWorkers->getLinkedClientsToWorker($param);
                
                //GET ALL FILES
                $this->msite->set_where("status='1'");
                $this->msite->set_where("workerId='$param'");
                $this->msite->set_where("category='4'");
                $this->msite->sort_objects("sort", "DESC");
                $allFiles = $this->msite->get_all_objects('first_files');
                $output['allFilesWorker'] = $this->msiteWs->objects_to_ArrayNoseoData($allFiles);
                
                //GET ALL RECOMMENDS
                $output['recommendsWorker'] = $this->foreignWorkers->getDataforFormsPopulate($param,$table = 'first_recommendsWorkers',$contactSenior = FALSE, $recommendsWorkers = TRUE );
                $output['formRecommendsWorker'] = $this->foreignWorkers->getformFields($object = 'first_recommendsWorkers');
                
                $output['dataParam'] = $dataParamArray['data'];
                $output['param'] = $param;
        }
        
        else if ($this->input->get('noIDNewWorker')==1) {
            $output['form'] = $this->foreignWorkers->getformFields($object = 'first_ForeignWorkers', FALSE, $action = 'newWorker');
            $output['rowsOption4Select'] = $this->msiteWs->getRowTablesforSelect($output['form']);;
        }
        
        
        if($this->input->get('update', TRUE)==1) { // FOR AUTO ADMIN FORMS
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $jsonPostsUpdate = $this->msiteWs->getPostFromJson(array('inputId','inputName','inputValue','formType'));
                
                if( !empty($jsonPostsUpdate['inputId']) && !empty($jsonPostsUpdate['inputName']) ) {
                    $params = array (
                      $jsonPostsUpdate['inputName'] => $jsonPostsUpdate['inputValue']
                    );
                    
                    $table = 'first_ForeignWorkers';
                    $this->db->where('id', $jsonPostsUpdate['inputId']);
                    $output['OK_update'] = $this->db->update($table, $params).' | '.$jsonPostsUpdate['formType'] ; 
                } else {
                    $output['OK_update'] = 'FALSE';
                }
                
            } else {
                $output = $checkUserCredentials;
            }
            
            
        }
        
        //FOR MULTIPLE RECOMMENDS
        else if($this->input->get('update', TRUE)==2) {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $jsonPostsUpdate = $this->msiteWs->getPostFromJson(array('inputId','inputName','inputValue','formType'));
                
                if( !empty($jsonPostsUpdate['inputId']) && !empty($jsonPostsUpdate['inputName']) ) {
                    $params = array (
                      $jsonPostsUpdate['inputName'] => $jsonPostsUpdate['inputValue']
                    );
                    
                    $table = 'first_recommendsWorkers';
                    $this->db->where('id', $jsonPostsUpdate['inputId']);
                    $output['OK_update'] = $this->db->update($table, $params).' | '.$jsonPostsUpdate['formType'] ; 
                } else {
                    $output['OK_update'] = 'FALSE';
                }
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        else if($this->input->get('insertRecommend', TRUE)==1) {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                if(!empty($param)) {
                    
                    $tableInsert = 'first_recommendsWorkers';

                    if(!$this->foreignWorkers->is_DoubleInsert($tableInsert)) {
                    
                        $data = array(
                            'lang' => NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'workerId' => $param
                        );

                        $insert = $this->db->insert($tableInsert, $data); 
                        $output['insertId'] = $this->db->insert_id(); 
                    } else {
                        $output['insertId'] = $this->msite->get_max($tableInsert, 'id');
                    }
                    
                    //$output['OK_insert'] = 'INSERT: '.$this->db->insert_id().' for Id: '.$param;
                } else $output['insert_error'] = 'error NO id'; 
                
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        else if($this->input->get('delette', TRUE)=='recommend') {
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','rowId'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                if(!empty($jsonPosts['rowId'])) {
                    
                    $tableDelette = 'first_recommendsWorkers';
                    
                    $this->db->where('id', $jsonPosts['rowId']);
                    $this->db->delete($tableDelette);
                }
                
                $output['delette'] = "deletteContact ".$jsonPosts['rowId'];
            }
            else {
                $output = $checkUserCredentials;
            }
        }
        
        
        else if($this->input->get('insertWorker', TRUE) == 1 ) {
            
            
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $tableInsert ='first_ForeignWorkers';
                
                if( !$this->foreignWorkers->is_DoubleInsert($tableInsert) ) {

                    
                    $jsonDataPosts = $this->msiteWs->getPostFromJson(
                            array(
                                'dateInscription',
                                'workerStatus',
                                'surname',
                                'name',
                                'sex',
                                'email',
                                'phone1',
                                'phone2',
                                'phoneSMS',
                                'address'
                                )
                            );
                    
                    
                    $data = array(
                        'lang' => NULL,
                        'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                        'status' => 1,
                        'created_at' => date("Y-m-d H:i:s"),
                        'dateInscription' => !empty($jsonDataPosts['dateInscription']) ? $jsonDataPosts['dateInscription'] : date("Y-m-d H:i:s"),
                        'workerStatus' => $jsonDataPosts['workerStatus'],
                        'surname' => $jsonDataPosts['surname'],
                        'name' => $jsonDataPosts['name'],
                        'sex' => $jsonDataPosts['sex'],
                        'email' => $jsonDataPosts['email'],
                        'phone1' => $jsonDataPosts['phone1'],
                        'phone2' => $jsonDataPosts['phone2'],
                        'phoneSMS' => $jsonDataPosts['phoneSMS'],
                        'address'  => $jsonDataPosts['address']
                    );
//
//
                    $insert = $this->db->insert($tableInsert, $data); 
                    $insert_id = $this->db->insert_id(); 

                    $data_seo = array(
                        'title' => 'עובד',
                        'description' => 'מערכת עובדים זרים',
                        'friendly' =>  'עובד'. '_' . $insert_id,
                        'controller' => $this->router->fetch_class(),
                        'method' => $this->router->fetch_method(),
                        'param' => $insert_id,
                        'robots' => '',
                        'image' => '',
                        'canonical' => base_url().'/'.$this->router->fetch_class() . '/' . $this->router->fetch_method() . '/' . $insert_id,
                    );

                    $seo_id = $this->msite->insert_object(Msite::TABLE_SEO, $data_seo); 
                    $this->msite->update_object($tableInsert, $insert_id, array('seo_id' => $seo_id));

                    $output['insertId'] = $insert_id;
                } else {
                    $output['insertId'] = $this->msite->get_max($tableInsert, 'id');
                }
                        
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
               
        elseif (!$param) {
            
            //GET countryAreas for SearchWorkers
            $this->msite->set_where("status='1'");
            $countryAreas = $this->msite->get_all_objects('first_countryAreas');
            $output['countryAreas'] = $this->msiteWs->objects_to_ArrayNoseoData($countryAreas);
            
            if(!empty($this->input->get('q', TRUE)) || !empty($this->input->get('search', TRUE)) ) {
            
                $searchFlag = false;
                
               if(!empty($this->input->get('statusClientSearch', TRUE))) {
                
                    $search = $this->input->get('statusClientSearch', TRUE);
                    $this->msite->set_where("workerStatus='$search'");
                    $searchFlag = true;
                }
                
                if(!empty($this->input->get('countryAreasSearch', TRUE))) {
                
                    $search = $this->input->get('countryAreasSearch', TRUE);
                    $this->msite->set_where("countryArea='$search'");
                    $searchFlag = true;
                }
                
                if(!empty($this->input->get('licenseSearch', TRUE))) {
                
                    $search = $this->input->get('licenseSearch', TRUE);
                    $curr_date = date("Y-m-d");
                    if($search=='בתוקף') {
                        $this->msite->set_where("visa_validity >= '$curr_date' ");
                    } else {
                        $this->msite->set_where("visa_validity <= '$curr_date' ");
                    }
                    $searchFlag = true;
                }
                
                $q = $this->input->get('q', TRUE);
                
                if(!empty($q)) {
                    $this->msite->set_where_like(['name', 'surname', 'passport', 'address', 'phone1', 'phone2', 'phoneSMS', 'CountryofBirth'],
                        $q);
                    $searchFlag = true;
                    
                }
                
                $workers = '';
                
                if($searchFlag) {
                    $this->msite->set_where("status='1'");
                    $this->msite->sort_objects("name", "ASC");
                    $workers = $this->msite->get_all_objects('first_ForeignWorkers', NULL, array('id','name','surname','phone1','phone2','workerStatus','phoneSMS','visa_validity'));
                };
                
                if(!empty($workers)) {
                    $output['foreignWorkersList'] = $this->msiteWs->objects_to_ArrayNoseo($workers);
                } else {
                    $output['foreignWorkersList'] = array();
                }
                
                
            } else {
                $output['foreignWorkersList'] = array();
            }
            
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
        
     }
    
    
    
    
    public function firstTasks ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('all'); //all //superAdmin //admin  //user
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if($this->input->get('newTask', TRUE)==1) {
                
                $tableInsert ='first_tasks';
                $jsonPostsInsert = $this->msiteWs->getPostFromJson(array('upTask','doneTask','typeTask','taskStatus','userId','seniorId','workerId','comments','textMatash'));
                
                if( !empty($jsonPostsInsert['userId']) ) {

                    $data = array(
                        'lang' => NULL,
                        'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                        'status' => 1,
                        'created_at' => date("Y-m-d H:i:s"),
                        'upTask' => $jsonPostsInsert['upTask'],
                        'doneTask' => $jsonPostsInsert['doneTask'],
                        'typeTask' => $jsonPostsInsert['typeTask'],
                        'taskStatus' => $jsonPostsInsert['taskStatus'],
                        'userId' => $jsonPostsInsert['userId'],
                        'seniorId' => $jsonPostsInsert['seniorId'],
                        'comments' => $jsonPostsInsert['comments'],
                        'workerId' => $jsonPostsInsert['workerId']
                    );
                    
                    $insert = $this->db->insert($tableInsert, $data); 
                    $insert_id = $this->db->insert_id();
                    
                    if(!empty($jsonPostsInsert['textMatash'])) {
                        
                        $dataMatash = array(
                            'lang' => NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($tableInsert, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'seniorId' => $jsonPostsInsert['seniorId'],
                            'dateReport' => $jsonPostsInsert['doneTask'],
                            'subjectReport' => $jsonPostsInsert['textMatash']
                        );
                        
                        
                        $insertMatash = $this->db->insert('first_reports', $dataMatash); 
                        //$insert_id = $this->db->insert_id();
                        
                    }
                    
                    

                    $output['newTaskId'] = $insert_id;
                }
                
                $output['action'] = 'newTask';
                
            }
            
            else if($this->input->get('update', TRUE)==1) {
                
                $jsonPosts = $this->msiteWs->getPostFromJson(array('updateStatusId','updateStatusName'));
                
                $idStatus = $this->foreignWorkers->getRowValuesFromNameDB($fromDB = 'first_taskStatus', $fromFieldName = 'name', $id4Search= $jsonPosts['updateStatusName']);
                
                if(!empty($idStatus['id']) && !empty($jsonPosts['updateStatusName'])) {
                    $params = array (
                    'taskStatus' => $idStatus['id']
                );
                
                    $this->db->where('id', $jsonPosts['updateStatusId']);
                    $output['OK_id'] = $this->db->update('first_tasks', $params); 
                } else {
                    $output['error'] = 'errorUpdate';
                }
                
            }
            
            else if($this->input->get('update', TRUE)==2) {
                
                $jsonPosts = $this->msiteWs->getPostFromJson(array('updateCommentId','updateCommentText'));
                
                if(!empty($jsonPosts['updateCommentText']) && !empty($jsonPosts['updateCommentId'])) {
                    $params = array (
                    'comments' => $jsonPosts['updateCommentText']
                );
                    
                $this->db->where('id', $jsonPosts['updateCommentId'][0]);
                
                $output['OK_id'] = $this->db->update('first_tasks', $params); 
                    
                } else {
                    $output['error'] = 'errorUpdate';
                }
            }
            
            else if($this->input->get('delette', TRUE)==1) {
                
                $jsonPosts = $this->msiteWs->getPostFromJson(array('rowId'));
                
                if(!empty($jsonPosts['rowId'])) {
                    $this->db->where('id', $jsonPosts['rowId']);
                    $this->db->delete('first_tasks');
                    
                    $output['del_id'] = 'OK '.$jsonPosts['rowId']; 
                }
                
                else {
                    $output['error'] = 'errorDelette';
                }
            }
            
            else {
                
                //GET ALL TASKS
                $this->foreignWorkers->set_where(ForeignWorkers::TABLE_TASKS . ".status='1'");
                //$this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".sort", "DESC");
                $order = !empty($this->input->get('orderBy', TRUE)) ? $this->input->get('orderBy', TRUE) : '';
                
                $limit = $this->input->get('nolimit') == 1 ? FALSE : 3;
                
                $tasks = $this->foreignWorkers->get_all_tasks($order,$jsonPosts['userId'], $limit);
                $output['data'] = $tasks;
                
                
                //GET ALL STATUS FOR SELECT -> CHANGE
                $this->msite->set_where("status='1'");
                $this->msite->sort_objects("sort", "DESC");
                $tasksStatus = $this->msite->get_all_objects('first_taskStatus');
                $output['tasksStatus'] = $this->msiteWs->objects_to_ArrayNoseoData($tasksStatus);
                
            }
            
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        //print_r($this->data['page']); die('asd');
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    
    public function uploadFiles ($param = FALSE) {
        
        if(!empty($this->input->post('siteCode',true))) {
            $param = 'uploadMethod';
        }
        
        //$output['code'] = 'code: '.$this->input->post('siteCode',true);
        
        $this->_loaderWS($param); // NO SEND SITE CODE!!!!
        
        //GET ALL FILES CATEGORIES
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $files_categories = $this->msite->get_all_objects('first_files_categories');
        $output['files_categories'] = $this->msiteWs->objects_to_ArrayNoseoData($files_categories);
                
        if ($this->input->get('getFiles', TRUE) == 1) {
            
           $pageAutoriced = array('all'); //all //superAdmin //admin  //user
           $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','workerId','seniorId','category'));
           $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

           if($checkUserCredentials != 'unauthorized') {
               
                //GET ALL FILES
                $this->msite->set_where("status='1'");
                
                if(!empty($jsonPosts['workerId'])) {
                    $id = $jsonPosts['workerId'];
                    $this->msite->set_where("workerId='$id'");
                }
                else if(!empty($jsonPosts['seniorId'])) {
                    $id = $jsonPosts['seniorId'];
                    $this->msite->set_where("seniorId='$id'");
                };
                
                if(!empty($jsonPosts['category'])) {
                    $cat = $jsonPosts['category'];
                    $this->msite->set_where("category='$cat'");
                }
                
                $this->msite->sort_objects("sort", "DESC");
                $allFiles = $this->msite->get_all_objects('first_files');
                $output['allFilesWorker'] = $this->msiteWs->objects_to_ArrayNoseoData($allFiles);
               
           }
           
           else {
                $output = $checkUserCredentials;
            }
        }
        
        
        
        else {
            //$pageAutoriced = array('all'); //all //superAdmin //admin  //user
            //$jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','workerId','seniorId','name'));

            $jsonPosts = array (
                'userId' => $this->input->post('userId',true),
                'token' => $this->input->post('token', true),
                'userCredential' => $this->input->post('userCredential',true),
                'workerId' => $this->input->post('workerId',true),
                'seniorId' => $this->input->post('seniorId',true),
                'name' => $this->input->post('name',true),
                'category' => $this->input->post('category',true),
                'siteCode' => $this->input->post('siteCode',true)
            );

            //print_r($jsonPosts);die();

            if($jsonPosts['siteCode'] == md5($this->data['code'])) {

                $config = array();
                $config['upload_path']          = appFILES;
                $config['allowed_types']        = 'jpg|jpeg|pdf|png|tif';
                $config['file_name']            = uniqid();
                $config['max_size']             = 5500;
                //$config['max_width']            = 1024;
                //$config['max_height']           = 768;


                $this->load->library('upload', $config);
                $this->upload->initialize($config, TRUE);

                $field['name'] = 'filename';

                if($this->upload->do_upload($field['name'])) {
                    
                    $filedata = $this->upload->data(); 

                    if($filedata['file_name']) {

                        $table = 'first_files';
                        $inputNameFile = 'fileName';

                        $data = array(
                            'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'workerId' => $jsonPosts['workerId'],
                            'seniorId' => $jsonPosts['seniorId'],
                            'documentName' => $jsonPosts['name'],
                            $inputNameFile => $filedata['file_name'],
                            'category' => !empty($jsonPosts['category']) ? $jsonPosts['category'] : 4
                        );

                        if($insert_id = $this->msite->insert_object($table, $data)) {
                               $output['insert_id'] = $insert_id;
                        }
                            
                    }
                    
                    
                }

                else {

                    $error = 'שגיאה';

                    if(!empty($this->upload->display_errors())) {

                        $errorMessage = str_replace("<p>", "", $this->upload->display_errors() );
                        $errorMessage = str_replace("</p>", "", $errorMessage );

                        if($errorMessage == 'The filetype you are attempting to upload is not allowed.') {
                            $error = 'סוג הקובץ שאתה מנסה להעלות אינו מורשה.';
                        }
                        elseif ($errorMessage == 'The file you are attempting to upload is larger than the permitted size.') {
                            $error = 'הקובץ שאתה מנסה להעלות גדול מהגודל המותר.';
                        }

                        else {
                          $error = $errorMessage;  
                        }

                        //$error = 'שגיאה';
                    }

                    $output['uploadError'] = $error;
                }
            }

            else {
                $output = 'unauthorized';
            }
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }                    
    
    
    
    
    
    public function uploadFilesSMS ($param = FALSE) {
        
        if(!empty($this->input->post('siteCode',true))) {
            $param = 'uploadMethod';
        } else {
            die();
        }
        
        $this->_loaderWS($param);
        
        $config = array();
        $config['upload_path']          = appFILES;
        $config['allowed_types']        = 'jpg|jpeg|pdf|png|tif';
        $config['file_name']            = uniqid();
        $config['max_size']             = 5500;
        //$config['max_width']            = 1024;
        //$config['max_height']           = 768;


        $this->load->library('upload', $config);
        $this->upload->initialize($config, TRUE);

        
        $table = 'first_SMS';
        $inputNameFile = 'fileName';
        $idRows = $this->input->post('idRows');
        $userId = $this->input->post('userId');
        
        $sendPage = $this->input->post('sendPage');
        $phones = $this->foreignWorkers->getSMSPhones($idRows,$sendPage);
        
        
        if(!isset($_FILES['filename']) || empty($_FILES['filename'])) {
            
            $data = array(
                'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
                'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                'status' => 1,
                'created_at' => date("Y-m-d H:i:s"),
                'smsText' => $this->input->post('smsText'),
                'phones' => $phones['phones'], 
                'clientsId' => $idRows,
                'fileName' => '',
                'pageSource' =>  $sendPage,
                'userId' => $userId
                
            );
            
            if($insert_id = $this->msite->insert_object($table, $data)) {
                $output['insert_SMSid'] = $insert_id;
            }
            
        }  
        
        else if($this->upload->do_upload('filename') ) {

            $filedata = $this->upload->data(); 

            if($filedata['file_name']) {

                $longUrl = base_url().FILES.$filedata['file_name'];

                $bitlyUrl = $this->foreignWorkers->getBitlyUrl($longUrl);

                $data = array(
                    'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
                    'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                    'status' => 1,
                    'created_at' => date("Y-m-d H:i:s"),
                    'smsText' => $this->input->post('smsText'),
                    'phones' => $phones['phones'], 
                    'clientsId' => $idRows,
                    'fileName' => $filedata['file_name'],
                    'fileBitly' =>  $bitlyUrl,
                    'pageSource' =>  $sendPage,
                    'userId' => $userId
                );
                
                if($insert_id = $this->msite->insert_object($table, $data)) {
                    $output['insert_SMSid'] = $insert_id;
                }
            }
        }
        
        else {

            $error = 'שגיאה';

            if(!empty($this->upload->display_errors())) {

                $errorMessage = str_replace("<p>", "", $this->upload->display_errors() );
                $errorMessage = str_replace("</p>", "", $errorMessage );

                if($errorMessage == 'The filetype you are attempting to upload is not allowed.') {
                    $error = 'סוג הקובץ שאתה מנסה להעלות אינו מורשה.';
                }
                elseif ($errorMessage == 'The file you are attempting to upload is larger than the permitted size.') {
                    $error = 'הקובץ שאתה מנסה להעלות גדול מהגודל המותר.';
                }

                else {
                  $error = $errorMessage;  
                }

                //$error = 'שגיאה';
            }

            $output['uploadError'] = $error;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }   
    
    
    
    
    
    
    public function documents ($param = FALSE) {  //AllFiles
        
        $this->_loaderWS($param);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $clients = $this->msite->get_all_objects('first_seniors', NULL, array('id','name','surname','phone','seniorStatus'));
        $output['clientsList'] = $this->msiteWs->objects_to_ArrayNoseo($clients);
        
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $workers = $this->msite->get_all_objects('first_ForeignWorkers',NULL,array('id','name','surname','phone1','phone2','workerStatus'));
        $output['foreignWorkersList'] = $this->msiteWs->objects_to_ArrayNoseo($workers);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $workers = $this->msite->get_all_objects('first_files_categories');
        $output['files_categories'] = $this->msiteWs->objects_to_ArrayNoseo($workers);
            
            
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function reports ($param = FALSE) {  //AllFiles
        
        $this->_loaderWS($param);
        
        $output['reports'] = 'reports';
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $clients = $this->msite->get_all_objects('first_seniors', NULL, array('id','name','surname','phone','seniorStatus'));
//        $output['clientsList'] = $this->msiteWs->objects_to_ArrayNoseo($clients);
        
            
            
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function match ($param = FALSE) {  //AllFiles
        
        $this->_loaderWS($param);
        //echo "<pre>";
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $clients = $this->msite->get_all_objects('first_seniors', NULL, array('id','name','surname','seniorStatus','countryArea'));
        $clientsList = $this->msiteWs->objects_to_ArrayNoseo($clients);
        
        $this->db->select(array('name','surname','id','favorite_place','passport_from','countryArea','sex','lang1','lang2','lang3','CountryofBirth'));
        $this->db->from('first_ForeignWorkers');
        $workersListGet = $this->db->get();
        $workersList = $workersListGet->result_array();
        
        
        $matchList = array();
        
        
        foreach ($clientsList as $key => $client) {
            
            
            
            $this->db->select(array('male','female','from','langRequest'));
            $this->db->from('first_clientRequests');
            $this->db->where('seniorId',$client['id']);
            $clientRequests = $this->db->get();
            $clientRequest = $clientRequests->row_array();
            
            //echo "<br/>ClientReq: <br/>";
            //print_r($clientRequest);
            
            $workerMatch = array();
            
            
            foreach ($workersList as $key => $worker) {
                
                
                
                $sex = 0; $language = 0; $from = 0;  $countryArea = 0;
                
                if($clientRequest['male'] == 'כן' && $worker['sex'] == 'זכר') {$sex = 1;}
                if($clientRequest['male'] == 'לא' && $worker['sex'] == 'נקבה') {$sex = 1;}
                if($clientRequest['female'] == 'כן' && $worker['sex'] == 'נקבה') {$sex = 1;}
                if($clientRequest['female'] == 'לא' && $worker['sex'] == 'זכר') {$sex = 1;}
                
                if($clientRequest['from'] == $worker['CountryofBirth'] ) {$from = 1;}
                if($clientRequest['from'] == $worker['passport_from'] ) {$from = 1;}
                
                if(
                    $clientRequest['langRequest'] == $worker['lang1'] ||
                    $clientRequest['langRequest'] == $worker['lang2'] ||
                    $clientRequest['langRequest'] == $worker['lang3'] 
                ) {$language = 1;}
                
                if(
                    $client['countryArea'] == $worker['favorite_place'] ||
                    $client['countryArea'] ==  $worker['countryArea']
                ) {$countryArea = 1;}
                
                
                
                $workerMatch[] = array(
                    'workerName' => $worker['name'].' '.$worker['surname'],
                    'workerId' => $worker['id'],
                    'sexMatch' => $sex,
                    'from' => $from,
                    'langRequest' => $language,
                    'countryArea' => $countryArea
                );
                
            };
            
            
            
            
            foreach ($workerMatch as $workerM) {
                
                $resultMatch = $workerM['sexMatch'] + 
                    $workerM['from'] + 
                    $workerM['langRequest'] +
                    $workerM['countryArea'];
                
                if($resultMatch >= 3) {
                    $workerM['points'] = $resultMatch;
                    $workerBestMatch[] = $workerM;
                };
                
            }
            
            
            
            
            if(!empty($workerBestMatch)) {
                
                $matchList[] = array (
                    'nameClient' =>  $client['name'].' '.$client['surname'],
                    'seniorId' => $client['id'],
                    'workers' => $workerBestMatch
                );
            }
            
            $workerBestMatch = array();
            
        } // DONE FOR EACH;
                
        
//        echo "<pre>";
//        print_r($matchList);die('sebas');
//        echo "</pre>";
        
        foreach ($matchList as $key => $valueList) {
            
            if(isset($valueList['workers'])) {
                
                $clientData = array(
                    'nameClient' => $valueList['nameClient'],
                    'seniorId' => $valueList['seniorId'],
                );

                foreach ($valueList['workers'] as $value) {

                    $matchList4Display[] = array (
                        'points' => $value['points'],
                        'client' => $clientData,
                        'worker' => $value
                    );
                }
            }
            
        }
        
        usort($matchList4Display, function($a, $b) {
            return $b['points'] - $a['points'];
        });
        
        $output['matchList'] = $matchList4Display;
            
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function users ($param = FALSE) {  //AllFiles
        
        $this->_loaderWS($param);
        
        if($this->input->get('newUser', true) ==1 ) {
            
            $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
            $jsonPosts = $this->msiteWs->getPostFromJson(array(
                'userId',
                'token',
                'userCredential',
                'name',
                'username',
                'userType',
                'email',
                'phone',
                'passwordMd5'
            ));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized' ) {

                if(isset($jsonPosts['username'])) {
                    $this->db->select('id');
                    $this->db->from('first_Users');
                    $this->db->where('username', $jsonPosts['username']);
                    $result= $this->db->get();
                    $results = $result->row_array();

                    if($results['id']) {

                        $output['error'] = 'שם משתמש כבר קיים במערכת.';


                    } else {

                        $table = 'first_Users';

                        $data = array(
                            'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'name' => $jsonPosts['name'],
                            'username' => $jsonPosts['username'],
                            'userType' => $jsonPosts['userType'],
                            'email' => $jsonPosts['email'],
                            'phone' => $jsonPosts['phone'],
                            'passwordMd5' => md5($jsonPosts['passwordMd5'])
                        );

                        
                        $insert_id = $this->msite->insert_object($table, $data);

                        if($insert_id) {
                            $output['insertOk'] = 'OK';
                        } else {
                            $output['error'] = 'errorInsert';
                        }

                    }

                } else {$output['error'] = 'errorInsert';};

            } else {
                $output = $checkUserCredentials;
            };
        }
        
        else if($this->input->get('delleteUser', true) == 1 ) {
            
            $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
            $jsonPosts = $this->msiteWs->getPostFromJson(array(
                'userId',
                'token',
                'userCredential',
                'rowId'
            ));
            
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized' ) {

                if(isset($jsonPosts['rowId'])) {
                    
                    
                    $this->db->where('id', $jsonPosts['rowId']);
                    $this->db->delete('first_Users');

                    $output['deletted'] = $jsonPosts['rowId'];

                } else {$output['error'] = 'errorDelette';};

            } else {
                $output = $checkUserCredentials;
            };
        
        }
        
        
        else if($this->input->get('getUserbyId', true) == 1 ) {
            
            $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
            
            $jsonPosts = $this->msiteWs->getPostFromJson(array(
                'userId',
                'token',
                'userCredential',
                'rowId'
            ));
            
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized' ) {

                if(isset($jsonPosts['rowId'])) {
                    
                    $table = 'first_Users';
                    
                    $this->db->select('*');
                    $this->db->from($table);
                    $this->db->where('id', $jsonPosts['rowId']);
                    $result= $this->db->get();
        
                    $output['userData'] = $result->row_array();

                } else {$output['error'] = 'noRowId';};

            } else {
                $output = $checkUserCredentials;
            };
        
        }
        
        
        elseif($this->input->get('updateUser', true) ==1 ) {
            
            $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
            $jsonPosts = $this->msiteWs->getPostFromJson(array(
                'userId',
                'token',
                'userCredential',
                'name',
                'userType',
                'email',
                'phone',
                'passwordMd5',
                'userIdUpdate'
            ));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized' ) {

                if(isset($jsonPosts['userIdUpdate'])) {
                    
                        $table = 'first_Users';

                        $update_id = false;
                        
                        if(!empty($jsonPosts['passwordMd5'])) {
                            $params = array(
                                'name' => $jsonPosts['name'],
                                'userType' => $jsonPosts['userType'],
                                'email' => $jsonPosts['email'],
                                'phone' => $jsonPosts['phone'],
                                'passwordMd5' => md5($jsonPosts['passwordMd5'])
                            );
                        
                            $this->db->where('id', $jsonPosts['userIdUpdate']);
                            $update_id = $this->db->update($table, $params); 
                        }

                        if($update_id) {
                            $output['updatetOk'] = 'OK: '.$update_id;
                        } else {
                            $output['error'] = 'errorUpdate';
                        }

                    } else {$output['error'] = 'errorUpdadeId';};
            } else {
                $output = $checkUserCredentials;
            };
        }
        
        
        else {
            $this->msite->set_where("status='1'");
            $this->msite->sort_objects("sort", "DESC");
            $clients = $this->msite->get_all_objects('first_Users', NULL, array('id','name','userType','username','phone','email'));
            $output['usersList'] = $this->msiteWs->objects_to_ArrayNoseo($clients);

            $this->msite->set_where("status='1'");
            $this->msite->sort_objects("sort", "DESC");
            $userTypes = $this->msite->get_all_objects('first_userType');
            $output['userTypes'] = $this->msiteWs->objects_to_ArrayNoseo($userTypes);
        }
            
            
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function getDbForSearchInput ($param = FALSE) {
        
        $this->_loaderWS($param); // NO SEND SITE CODE!!!!
        
        $pageAutoriced = array('all'); //all //superAdmin //admin  //user
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','workerId','db'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

        if($checkUserCredentials != 'unauthorized') {

            $this->db->select('id,name');
            $this->db->from($jsonPosts['db']);
            $this->db->where('status',1);
            $allDataGet = $this->db->get();

            $output = $allDataGet->result_array();
        }
        
        else {
                $output = 'unauthorized';
            }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    
        
        }
        
        
        
    public function sendSms ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('all'); //all //superAdmin //admin  //user
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','smsTestNumber','insertSMSid'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
            $this->db->select('*');
            $this->db->from('first_SMS');
            $this->db->where('id',$jsonPosts['insertSMSid']);
            $data = $this->db->get();
            $dataDB = $data->row_array();
            
            if(!empty($jsonPosts['smsTestNumber']) && !empty($dataDB)) {
                
                $message = $dataDB['smsText'];
                
                if(!empty($dataDB['fileBitly'])) {
                    $message .= ' - '.'לצפיה בקובץ: '; 
                    $message .= $dataDB['fileBitly'];
                }
                
                $this->foreignWorkers->sendSMS( $jsonPosts['smsTestNumber'], $message, $from = 'Workers', $param = FALSE);
                //$this->foreignWorkers->sendSMS( '054-6464312,052-815-0841', $message, $from = 'Workers', $param = 'multi');
            };
            
            $output['sms'] = 'SENT';
        }
        
        else {
            
            $output = $checkUserCredentials;
        }
        
        //print_r($this->data['page']); die('asd');
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function sendMultipleSms ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('superAdmin'); //all //superAdmin //admin  //user
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','insertSMSid'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
            $this->db->select('*');
            $this->db->from('first_SMS');
            $this->db->where('id',$jsonPosts['insertSMSid']);
            $data = $this->db->get();
            $dataDB = $data->row_array();
            
            if( !empty($dataDB) && !empty($dataDB['phones']) ) {
                
                $message = $dataDB['smsText'];
                
                if(!empty($dataDB['fileBitly'])) {
                    $message .= ' - '.'לצפיה בקובץ: '; 
                    $message .= $dataDB['fileBitly'];
                }
                
                //$this->foreignWorkers->sendSMS( $jsonPosts['smsTestNumber'], $message, $from = 'Workers', $param = FALSE);
                $this->foreignWorkers->sendSMS( $dataDB['phones'], $message, $from = 'Workers', $param = 'multi');
            };
            
            $output['smsDone'] = 'SENT';
        }
        
        else {
            
            $output = $checkUserCredentials;
        }
        
        //print_r($this->data['page']); die('asd');
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    

    public function configPage ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $table = 'first_firmData';
        $this->msite->set_where("id='1'");
        $active = $this->msiteWs->objects_to_ArrayNoseoData($this->msite->get_all_objects($table));
        
        $output['form'] = $active;
        $output['firmData'] = $this->foreignWorkers->getformFields($object = 'first_firmData');
        
        
        if($this->input->get('update', TRUE)==1) { // FOR AUTO ADMIN FORMS
            $pageAutoriced = array('all'); //all //superAdmin //admin  //user
            $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
            $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);

            if($checkUserCredentials != 'unauthorized') {
                
                $jsonPostsUpdate = $this->msiteWs->getPostFromJson(array('inputId','inputName','inputValue','formType'));
                
                if( !empty($jsonPostsUpdate['inputId']) && !empty($jsonPostsUpdate['inputName']) ) {
                    $params = array (
                      $jsonPostsUpdate['inputName'] => $jsonPostsUpdate['inputValue']
                    );
                    
                    $table = 'first_firmData';
                    $this->db->where('id', $jsonPostsUpdate['inputId']);
                    $output['OK_update'] = $this->db->update($table, $params).' | '.$jsonPostsUpdate['formType'] ; 
                } else {
                    $output['OK_update'] = 'FALSE';
                }
                
            } else {
                $output = $checkUserCredentials;
            }
        }
        
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function tasks ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('all'); //all //superAdmin //admin  //user
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            $output['tasks'] = '1';
        }
        
        else {
            
            $output = $checkUserCredentials;
        }
        
        //print_r($this->data['page']); die('asd');
        
        
        //GET ALL STATUS FOR SELECT -> CHANGE
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $tasksStatus = $this->msite->get_all_objects('first_taskStatus');
        $output['tasksStatus'] = $this->msiteWs->objects_to_ArrayNoseoData($tasksStatus);
            
        //GET ALL taskTypes FOR SELECT -> CHANGE
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $taskTypes = $this->msite->get_all_objects('first_task_type');
        $output['taskTypes'] = $this->msiteWs->objects_to_ArrayNoseoData($taskTypes);
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function insertToDb() {
        
//        https://data.gov.il/dataset/countries
        //https://help.zoho.com/portal/en/community/topic/hebrew-characters-imported-from-a-csv-file-are-not-displayed-right
        
            $fileData = 'csv/cityesNew.csv';
        
        if (($handle = fopen(base_url().IMG.$fileData, "r")) !== FALSE) {
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $file[] = $data;
            }
        }
        
        fclose($handle);

        $table = 'first_cities';
        
        foreach ($file as $key => $value) {
            
            echo($value[0]);die('value');
            
            if($key==0) {
                
                $data = array(
                'lang' => NULL,
                'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                'status' => 1,
                'created_at' => date("Y-m-d H:i:s"),
                'name' => $value[0]
            );
//
//
            //$insert = $this->db->insert($table, $data); 
            //$insert_id = $this->db->insert_id(); 
            
            die('END');
            }
        }
        
        echo "<pre>";
        //print_r($workersHeziFromDb);die('sebas');
        print_r($file);die('sebas');
        echo "</pre>";

        
        echo "sebas";
    }
    
    
    
    
}
    

