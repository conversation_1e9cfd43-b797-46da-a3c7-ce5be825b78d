# מערכת ניהול מסמכים וטפסים

## מטרת המערכת
מערכת ניהול המסמכים מאפשרת למתנדבי השירות הלאומי לנהל את כל המסמכים והטפסים הנדרשים במהלך השירות, כולל העלאת מסמכים, חתימה על טפסים, ומעקב אחר סטטוס האישורים.

## סוגי מסמכים

### לפי שלב השירות
1. **טרום שירות** - מסמכים הנדרשים לפני תחילת השירות (צילום ת"ז, תמונת פנים, אישור רפואי, וכו')
2. **מהלך שירות** - מסמכים הנדרשים במהלך השירות (אישורי מחלה, אישור עבודה, אישור לימודים, וכו')
3. **אישורים** - מסמכים שהמערכת מספקת למתנדב (אישור תקופת שירות, דוח תקבולים, טופס 101)

### לפי סוג הפעולה
1. **העלאה** - מסמכים שהמתנדב מעלה למערכת (אישור מחלה, צילום ת"ז, וכו')
2. **חתימה** - טפסים שהמתנדב נדרש לקרוא ולחתום עליהם דיגיטלית (תקנון, הסכם דירה, וכו')
3. **קישור** - הפניה לטפסים או שירותים חיצוניים (דיווח תקלה בדירה, שאלון הכוון)
4. **צפייה** - מסמכים שהמתנדב יכול לצפות בהם (אישורים שהופקו עבורו)

## סטטוסים של מסמכים
- **Missing** - המסמך לא קיים במערכת, ניתן להעלות אותו
- **Pending** - המסמך הועלה וממתין לאישור, ניתן להעלות מסמך חדש
- **Exist** - המסמך אושר על ידי המערכת

## רכיבי המערכת

### FilesPage
הדף הראשי המארגן את תצוגת המסמכים לפי שלבי השירות. מחלק את המסמכים ל-3 אזורים:
- טרום שירות
- מהלך שירות 
- אישורים

### ServiceDocs, DocsTromSherut, IshurimDocs
קומפוננטים המרכזים את המסמכים לפי שלב השירות:
- **ServiceDocs** - מציג מסמכים למהלך השירות
- **DocsTromSherut** - מציג מסמכים לטרום שירות
- **IshurimDocs** - מציג אישורים לצפייה

### FileItem
קומפוננט המציג מסמך בודד במערכת. מטפל בהצגת סטטוס המסמך (קיים/חסר/ממתין) ומאפשר העלאת קבצים. מאפשר גישה לעדכון/העלאת קובץ חדש בהתאם לסטטוס.

### UploadFile
קומפוננט המטפל בתהליך העלאת הקבצים, כולל drag-and-drop ותצוגה מקדימה. תומך בקבצי תמונה ו-PDF.

### SickLeaveForm
קומפוננט ייעודי לטיפול באישורי מחלה, דורש בחירת תאריכים לפני העלאת הקובץ וחישוב מספר ימי המחלה.

### ReadAndSign
קומפוננט לטיפול בטפסים הדורשים חתימה דיגיטלית, מציג את תוכן הטופס ושולח קוד אימות ב-SMS.

### ExternalLink
קומפוננט לטיפול בקישורים חיצוניים, מציג כפתור שפותח קישור חיצוני.

### OpenPDFfiles
קומפוננט להצגת מסמכים לצפייה (PDF).

## מאפיינים מרכזיים

### העלאת קבצים
1. תמיכה בסוגי קבצים: jpg, jpeg, png, pdf
2. אפשרות לגרירה והשמטה (drag-and-drop)
3. תצוגה מקדימה של הקובץ שנבחר
4. אפשרות לבטל בחירה ולבחור קובץ אחר
5. חסימת האפשרות להעלות קובץ חדש רק עבור צילום ת"ז (FormID 1) כאשר סטטוס המסמך הוא "Exist". לכל שאר סוגי המסמכים, ניתן להעלות מסמך חדש גם כאשר הסטטוס הוא "Exist"

### אישורי מחלה
1. בחירת תאריך התחלה ותאריך סיום
2. חישוב אוטומטי של מספר ימי המחלה
3. אימות תקינות התאריכים (תאריך התחלה לפני תאריך סיום)
4. העלאת קובץ אישור המחלה לאחר בחירת התאריכים

### חתימה דיגיטלית
1. הצגת תוכן הטופס לקריאה
2. אישור קריאה וחתימה
3. שליחת קוד אימות ב-SMS
4. הזנת הקוד לאישור החתימה

### רענון נתונים
1. רענון רשימת המסמכים לאחר פעולות (העלאה, חתימה)
2. שמירת היסטוריית פעולות באמצעות תאריכי עדכון
3. הצגת סטטוס עדכני של המסמכים

## הגבלות ואבטחה
1. אימות נתוני משתמש (SessionKey ו-IDNumber) בכל פעולה מול ה-API
2. חסימת העלאה רק עבור צילום ת"ז (FormID 1) כאשר סטטוס המסמך הוא "Exist". לכל שאר סוגי המסמכים, ניתן להעלות גם כאשר הסטטוס הוא "Exist"
3. אימות סוג הקובץ המועלה
4. אימות תאריכים באישורי מחלה
5. אימות באמצעות SMS בתהליכי חתימה

## טבלת מסמכים
```
מזהה | שם מסמך               | סוג פעולה | שלב שירות   | הערות
-----------------------------------------------------------------
10    | אישור מחלה            | העלאה     | מהלך שרות   |
18    | אישור עבודה           | העלאה     | מהלך שרות   |
19    | אישור לימודים         | העלאה     | מהלך שרות   |
16    | בקשה להגשה לוועדה רפואית | העלאה | לא להציג    |
17    | סיכום רפואי לוועדה רפואית | העלאה | לא להציג    |
6     | תעודת פטור מהצבא      | העלאה     | טרום שרות   |
1     | צילום ת"ז + ספח       | העלאה     | טרום שרות   | לא ניתן להעלות כאשר סטטוס "Exist"
11    | אישור ניהול חשבון בנק | העלאה     | טרום שרות   |
5     | אישור רפואי           | העלאה     | טרום שרות   |
4     | תמונת פנים            | העלאה     | טרום שרות   |
14    | אישור אפוטרופוס       | העלאה     | טרום שרות   |
99    | אחר                   | העלאה     | טרום שרות   |
12    | הרשאת חיוב כללית      | חתימה     | טרום שרות   | אין סוג מסמך - מדובר בחתימה
13    | הצהרת פטור מגיוס       | חתימה     | טרום שרות   | אין סוג מסמך - מדובר בחתימה
2     | תקנון                 | חתימה     | טרום שרות   | אין סוג מסמך - מדובר בחתימה
3     | הסכם דירה             | חתימה     | טרום שרות   | אין סוג מסמך - מדובר בחתימה
15    | שאלון הכוון            | קישור     | טרום שרות   | אין סוג מסמך מדובר בקישור
71    | דיווח תקלה בדירה      | קישור     | מהלך שרות   | אין סוג מסמך מדובר בקישור
101   | טופס 101              | צפייה     | אישורים      |
102   | אישור תקופת שירות     | צפייה     | אישורים      |
104   | דוח תקבולים           | צפייה     | אישורים      |
```

## תזרים טיפול באישור מחלה
1. המתנדב לוחץ על כפתור "העלאת קובץ" באישור מחלה
2. נפתח מודל לבחירת תאריכי התחלה וסיום
3. המערכת מחשבת את מספר ימי המחלה ומציגה למתנדב
4. המתנדב בוחר קובץ (תמונה או PDF) של אישור המחלה
5. מוצגת תצוגה מקדימה של הקובץ
6. המתנדב שולח את הקובץ
7. המערכת שולחת את הקובץ לשרת עם פרטי התאריכים ומספר הימים
8. הסטטוס משתנה ל-"Pending" עד לאישור הבקשה

## תזרים טיפול בחתימה דיגיטלית
1. המתנדב לוחץ על כפתור החתימה בטופס הרלוונטי
2. נפתח מודל עם תוכן הטופס לקריאה
3. המתנדב מאשר קריאה וחתימה
4. המערכת שולחת קוד אימות ב-SMS
5. נפתח מודל להזנת הקוד
6. המתנדב מזין את הקוד
7. המערכת מאמתת את הקוד ומשלימה את החתימה
8. הסטטוס משתנה ל-"Exist"
