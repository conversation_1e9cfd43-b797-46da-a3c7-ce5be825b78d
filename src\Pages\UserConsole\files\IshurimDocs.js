/**
 * IshurimDocs - קומפוננט המציג אישורים קיימים במערכת
 *
 * מאפשר צפייה במסמכים ואישורים שונים (אישור תקופת שירות, דוח תקבולים וכו')
 * אוסף את האישורים מה-API ומציג אותם באמצעות OpenPDFfiles
 *
 * Props:
 * - infoUser: מידע על המשתמש
 */
import React, { useState, useEffect } from "react";
import { getSafeUserData } from "../../../context/AuthContext";
import {
  Grid,
  Typography,
  Paper,
  CircularProgress,
  Box,
} from "@mui/material";
import { filesTexts } from "./fileFunctions";
import { getFromApiSherutLeumi } from "./fileFunctions";
import OpenPDFfiles from "./OpenPDFfiles";
import "./documentStyles.css";

export default function IshurimDocs(props) {
  const { infoUser } = props;
  const serviceStatus = infoUser?.SeviceStatus;

  const [loading, setLoading] = useState(false);
  const [responseData, setResponseData] = useState(null);
  const [formsData, setFormsData] = useState(null);
  const [formsLoading, setFormsLoading] = useState(false);
  const [documentsByCategory, setDocumentsByCategory] = useState({});

  // Get user data from localStorage
  const userJ = getSafeUserData();

  // Fetch documents list from API
  useEffect(() => {
    if (!loading && !responseData && userJ) {
      const sendObj = {
        IDNumber: userJ.IDNO,
        SessionKey: userJ.SessionKey,
      };
      getFromApiSherutLeumi(
        "/api/v2/volunteer/documents/list",
        sendObj,
        setLoading,
        setResponseData
      );
    }
  }, [loading, responseData, userJ]);

  // Fetch available forms from API
  useEffect(() => {
    if (!formsLoading && !formsData && userJ) {
      const sendObj = {
        IDNumber: userJ.IDNO,
        SessionKey: userJ.SessionKey,
      };
      getFromApiSherutLeumi(
        "/api/v2/volunteer/forms/list",
        sendObj,
        setFormsLoading,
        setFormsData
      );
    }
  }, [formsLoading, formsData, userJ]);

  // Organize documents by category
  useEffect(() => {
    if (responseData?.data?.DocumentsList) {
      const documents = responseData.data.DocumentsList;

      // Create categories based on document types
      const categories = {
        certificates: { title: "אישורי שירות", docs: [] },
        payments: { title: "דוחות תקבולים", docs: [] },
        forms: { title: "טפסים", docs: [] },
        other: { title: "מסמכים אחרים", docs: [] },
      };

      // Categorize each document
      documents.forEach((doc) => {
        switch (doc.TypeDocument) {
          case "101":
            categories.forms.docs.push(doc);
            break;
          case "102":
            categories.certificates.docs.push(doc);
            break;
          case "103":
            categories.certificates.docs.push(doc);
            break;
          case "104":
            categories.payments.docs.push(doc);
            break;
          default:
            categories.other.docs.push(doc);
        }
      });

      // Filter out empty categories
      const filteredCategories = Object.fromEntries(
        Object.entries(categories).filter(
          ([_, category]) => category.docs.length > 0
        )
      );

      setDocumentsByCategory(filteredCategories);
    }
  }, [responseData]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={3}>
        <CircularProgress />
        <Typography variant="body1" style={{ marginRight: 10 }}>
          טוען מסמכים...
        </Typography>
      </Box>
    );
  }

  const hasDocuments = responseData?.data?.DocumentsList?.length > 0;

  return (
    <Paper className="p-6 mt-4 rounded-lg">
      <Typography
        variant="h5"
        component="h2"
        gutterBottom
        align="right"
        className="mb-6 pb-2 border-b"
      >
        אישורים ומסמכים
      </Typography>

      {!hasDocuments ? (
        <Box textAlign="center" p={3}>
          <Typography variant="body1">אין אישורים להצגה</Typography>
        </Box>
      ) : (
        <div className="documents-container">
          {Object.entries(documentsByCategory).map(
            ([categoryKey, category]) => (
              <div key={categoryKey} className="mb-8 w-full">
                <Typography
                  variant="h6"
                  component="h3"
                  gutterBottom
                  align="right"
                  className="mb-4"
                >
                  {category.title}
                </Typography>

                <Grid
                  container
                  spacing={3}
                  sx={{
                    width: "100%",
                    margin: 0,
                    direction: "rtl",
                    display: "flex",
                    flexWrap: "wrap",
                    justifyContent: "flex-start",
                    "& .MuiGrid-item": {
                      width: "100%",
                      display: "flex",
                      padding: "6px",
                      "& > *": {
                        width: "100%",
                        height: "100%",
                      },
                    },
                  }}
                >
                  {category.docs.map((document, index) => (
                    <Grid item xs={12} sm={12} md={8} lg={6} key={index}>
                      <OpenPDFfiles data={document} />
                    </Grid>
                  ))}
                </Grid>
              </div>
            )
          )}
        </div>
      )}
    </Paper>
  );
}

/* 
פירוט קודים של מסמכים  הזמינים לרכזת "להורדה" דרך האפליקציה (קריאת שרת getDocument הסטטוס של המתנדבת נמשך מקריאת info)
 
1. ID 101  > טופס 101 (יוצג רק למתנדבים/ות בסטטוס SeviceStatus: "InServiceFirstYear" או InServiceSecondYear)

2. ID 102 >אישור תקופת שירות (יוצג רק למתנדבים/ות בסטטוס SeviceStatus: "AfterService וגם isEligibleForCertificate=True)

3. ID 103 > אישור הצבה (יוצג רק למתנדבים/ות בסטטוס SeviceStatus: "preService)
4. ID 104 > דוח תקבולים (דמי כיס) (יוצג רק לרק למתנדבים/ות בסטטוס SeviceStatus: "InServiceFirstYear" או InServiceSecondYear או AfterSrvice)
5. ID 118 > טופס בקשת אישור עבודה (יוצג רק למתנדבים בסטטוס SeviceStatus: "InServiceFirstYear" או InServiceSecondYear או SeviceStatus: "preService)
6. ID 119 > טופס בקשת אישור לימודים (יוצג רק למתנדבים בסטטוס SeviceStatus: "InServiceFirstYear" או InServiceSecondYear או SeviceStatus: "preService)
*/
