

<div class="panel-group" id="projects" role="tablist" aria-multiselectable="true">
    <div class="panel">
        <div class="row">
            <div class="col-md-4">
                <a href="<?php echo base_url('admin/store/category'); ?>" class="btn btn-default btn-block">קטגוריה</a>
            </div>
            <div class="col-md-4">
                <a href="<?php echo base_url('admin/store/extra'); ?>" class="btn btn-default btn-block">תוספת</a>
            </div>
            <div class="col-md-4">
                <a href="<?php echo base_url('admin/store/field'); ?>" class="btn btn-default btn-block">שדה</a>
            </div>
        </div>
    </div>
    <?php if(isset($categories) && !empty($categories))foreach($categories as $category) { ?>
    <div class="panel panel-default">
        <div class="panel-heading" role="tab" id="headingprojects<?php echo $category['id']; ?>">
            <h4 class="panel-title">
                <a role="button" data-toggle="collapse" data-parent="#projects" href="#collapseprojects<?php echo $category['id']; ?>" aria-expanded="true" aria-controls="collapseprojects<?php echo $category['id']; ?>">
                    <?php echo $category['en_name']; ?>
                </a>
                <div class="btn-group pull-left">
                    <a href="<?php echo base_url('admin/store/products/' . $category['id']); ?>" class="btn btn-xs btn-primary">מוצרים</a>
                    <a href="<?php echo base_url('admin/store/category/' . $category['id']); ?>" class="btn btn-xs btn-success">ערוך</a>
                </div>
                
            </h4>
        </div>
        
    </div>
    <?php } ?>
</div>