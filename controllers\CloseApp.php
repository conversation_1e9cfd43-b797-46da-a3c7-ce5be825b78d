<?php defined('BASEPATH') OR exit('No direct script access allowed');

class CloseApp extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = md5('seb-webProject!sherut-leumiCloseApp$%+');
        
        //die($this->data['code']);
        
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    
    //https://sherut-leumi.wdev.co.il/api/CloseApp/saveJson
    
    public function saveJson() {
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('code','jsonData','idno','base64Pic'));
        
        $isError = $this->checkCode($jsonPosts);
        if($isError) {return $this->output->set_status_header(200)->set_content_type('application/json')->set_output(json_encode($isError));}
        
        if( empty($jsonPosts['idno']) ) {
            
            $response = array(
                'success' => false,
                'error' => 'No idno'
            );
            
            return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
            
        }
        
        $insertId = $this->saveJsonDb($jsonPosts);
        
        if(!empty($insertId)) {
            
            $response = array(
                'success' => true,
                'insertId' => $insertId,
                'values' => $jsonPosts
            );
            
            
        } else {
            
            
            $response = array(
                'success' => false,
                'values' => $jsonPosts
            );
            
            
        }
        
        return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        
        
    }
    
    //https://sherut-leumi.wdev.co.il/api/CloseApp/getJson
    public function getJson() {
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('code','idno','getLastPic'));
        
        $isError = $this->checkCode($jsonPosts);
        if($isError) {return $this->output->set_status_header(200)->set_content_type('application/json')->set_output(json_encode($isError));}
        
        if( empty($jsonPosts['idno']) ) {
            
            $response = array(
                'success' => false,
                'error' => 'No idno'
            );
            
            return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
            
        }
        
        $dataJson = $this->getJsonDb($jsonPosts);
        
        if(!empty($dataJson)) {
            
            $response = array(
                'error' => false,
                'success' => true,
                'data' => $dataJson
            );
            
            
        } else {
            
            
            $response = array(
                'error' => true,
                'success' => false,
                'values' => $jsonPosts
            );
            
            
        }
        
        return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode($response));
        
        
    }
    
    private function checkCode($jsonPosts) {
        
        if( empty($jsonPosts['code']) || $this->data['code'] != $jsonPosts['code']) {
            
            return array(
                'error' => 'codeError',
                'success' => false,
            );
            
        }
        
        return false;
        
        
    }
    
    private function getJsonDb($jsonPosts) {
        
        $this->db->select('*');
        $this->db->from('closeAppSaves');
        $this->db->where('idno', $jsonPosts['idno']);
        
        if( isset($jsonPosts['getLastPic']) && $jsonPosts['getLastPic']) {
            
            $this->db->where('base64Pic	!=', null);
            
        }
        
        $this->db->order_by('id',"DESC");

        $result= $this->db->get();
        $data = $result->row_array();
        
        return $data;
        
    }
    
    
    private function saveJsonDb($jsonPosts) {
        
        $data = array(
            
            'jsonData' => json_encode($jsonPosts['jsonData']),
            'idno' => $jsonPosts['idno'],
            'base64Pic' => !empty($jsonPosts['base64Pic']) ? $jsonPosts['base64Pic'] : null
        );

        $insert = $this->db->insert('closeAppSaves', $data); 
        $insert_id = $this->db->insert_id();
        
        if( !empty($insert_id) && !empty($jsonPosts['base64Pic']) && !empty($jsonPosts['idno']) ) {
            
            $this->db->where('idno', $jsonPosts['idno']);
            $this->db->where('id !=', $insert_id);
            $this->db->delete('closeAppSaves');
            
        } 
        
                
        return $insert_id;
        
        
    }
    
    
}