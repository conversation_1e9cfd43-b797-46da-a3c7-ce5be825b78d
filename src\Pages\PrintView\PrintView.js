import React, { useState, useEffect, useRef } from "react";
import { getSafeUserData } from "../../context/AuthContext";
import { getFromApiSherutLeumi } from "../UserConsole/files/fileFunctions";
import Spinner from "react-bootstrap/Spinner";

export default function PrintView() {
  const [loading, setLoading] = useState(false);
  const [responseData, setResponseData] = useState(null);
  const [printDialogShowed, setPrintDialogShowed] = useState(false);
  const showHtmlRef = useRef(null);

  useEffect(() => {
    const userJ = getSafeUserData();

    if (!loading && !responseData) {
      const sendObj = {
        idno: userJ.IDNO,
        SessionKey: userJ.SessionKey,
        FormID: 101,
      };

      getFromApiSherutLeumi(
        "/api/v2/volunteer/data/getDocument",
        sendObj,
        setLoading,
        setResponseData
      );
    }

    if (responseData?.data?.Html && !loading && !printDialogShowed) {
      // Process the HTML to add the ID number next to the מס תז field
      const enhancedHtml = processForm101Html(
        responseData.data.Html,
        userJ.IDNO
      );
      responseData.data.Html = enhancedHtml;

      setPrintDialogShowed(true);
      setTimeout(() => {
        window.print();
      }, 200);
    }
  }, [loading, responseData, printDialogShowed]);

  // Additional useEffect to directly manipulate DOM after render if needed
  useEffect(() => {
    if (showHtmlRef.current && responseData?.data?.Html) {
      const userJ = getSafeUserData();
      enhanceIdFieldsInDOM(showHtmlRef.current, userJ.IDNO);
    }
  }, [responseData]);

  // Dedicated function to enhance ID fields in the rendered DOM
  const enhanceIdFieldsInDOM = (container, idNo) => {
    console.log("Enhancing ID fields with ID:", idNo);

    // Add a debug element to help us identify if the function is running
    const debugElement = document.createElement("div");
    debugElement.style.cssText = "display: none;";
    debugElement.setAttribute("data-debug", "ID field enhancement attempted");
    container.appendChild(debugElement);

    // First method: direct search for specific ID fields using class/ID hints
    const potentialIdFieldSelectors = [
      '[id*="tz"]',
      '[id*="idno"]',
      '[id*="id_number"]',
      '[class*="tz"]',
      '[class*="idno"]',
      '[class*="id_number"]',
      'label:contains("מס תז")',
      'span:contains("מס תז")',
      'div:contains("מס תז")',
      'label:contains("מספר זהות")',
      'span:contains("מספר זהות")',
      'div:contains("מספר זהות")',
    ];

    // Force an ID field if none is found
    let idFieldFound = false;

    // Custom method to find elements containing text
    const findElementsWithText = (root, searchText) => {
      const elements = [];
      const allElements = root.querySelectorAll("*");

      allElements.forEach((el) => {
        if (el.textContent && el.textContent.includes(searchText)) {
          elements.push(el);
          console.log(
            "Found element with text:",
            el.textContent.trim(),
            el.tagName
          );
        }
      });

      return elements;
    };

    // Find elements with relevant text
    let idFields = [];
    ["מס תז", "מספר זהות", "ת.ז.", "תעודת זהות"].forEach((text) => {
      const found = findElementsWithText(container, text);
      idFields = [...idFields, ...found];
    });

    console.log("Found ID fields:", idFields.length);

    // Manually force ID field insertion at a suitable place if none found
    if (idFields.length === 0) {
      // Find a good location to insert ID field - look for a header or other prominent element
      const headers = container.querySelectorAll("h1, h2, h3");
      if (headers.length > 0) {
        console.log("No ID fields found, inserting after header");
        const header = headers[0];
        const wrapper = document.createElement("div");
        wrapper.className = "field-group";
        wrapper.style.cssText =
          "margin-top: 15px; text-align: right; font-weight: bold;";

        const label = document.createElement("span");
        label.className = "field-label";
        label.textContent = "מספר תעודת זהות";

        const idDisplay = document.createElement("span");
        idDisplay.className = "idNumberDisplay";
        idDisplay.textContent = idNo;

        wrapper.appendChild(label);
        wrapper.appendChild(document.createTextNode(": "));
        wrapper.appendChild(idDisplay);

        if (header.nextSibling) {
          header.parentNode.insertBefore(wrapper, header.nextSibling);
        } else {
          header.parentNode.appendChild(wrapper);
        }

        idFieldFound = true;
      }
    } else {
      // Apply enhancement to each field
      idFields.forEach((field) => {
        // Skip if already enhanced
        if (field.querySelector(".idNumberDisplay")) return;

        console.log("Enhancing field:", field.textContent.trim());

        // Create a wrapper if it doesn't exist
        if (!field.classList.contains("field-group")) {
          // Clone the field
          const wrapper = document.createElement("div");
          wrapper.className = "field-group";
          wrapper.style.cssText =
            "margin: 5px 0; display: flex; align-items: center;";

          // Set label text
          const label = document.createElement("span");
          label.className = "field-label";
          label.textContent = field.textContent.trim();

          // Create ID display
          const idDisplay = document.createElement("span");
          idDisplay.className = "idNumberDisplay";
          idDisplay.textContent = idNo;

          // Replace field with wrapped structure
          wrapper.appendChild(label);
          wrapper.appendChild(document.createTextNode(": "));
          wrapper.appendChild(idDisplay);

          // Replace the original field
          if (field.parentNode) {
            field.parentNode.replaceChild(wrapper, field);
            idFieldFound = true;
          }
        }
      });
    }

    // If still no ID field found, add one at the top of the document
    if (!idFieldFound) {
      console.log("No suitable ID fields found, adding at top");
      const wrapper = document.createElement("div");
      wrapper.className = "field-group id-field-added";
      wrapper.style.cssText =
        "margin: 20px 0; padding: 10px; background-color: #f8f9fa; text-align: right; border-radius: 5px;";

      const label = document.createElement("span");
      label.className = "field-label";
      label.textContent = "מספר תעודת זהות";

      const idDisplay = document.createElement("span");
      idDisplay.className = "idNumberDisplay";
      idDisplay.textContent = idNo;

      wrapper.appendChild(label);
      wrapper.appendChild(document.createTextNode(": "));
      wrapper.appendChild(idDisplay);

      container.insertBefore(wrapper, container.firstChild);
    }
  };

  // Function to enhance the Form 101 HTML with ID number and improved styling
  const processForm101Html = (html, idNo) => {
    if (!html || !idNo) return html;

    // Create a temporary div to manipulate the HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = html;

    // Find all potential ID number fields by looking for מס תז or similar texts
    // Using a more specific selector to find exact matches
    const idFields = Array.from(tempDiv.querySelectorAll("*")).filter((el) => {
      const text = el.textContent && el.textContent.trim();
      return (
        text &&
        (text === "מס תז" ||
          text === "מספר תעודת זהות" ||
          text === "מספר זהות" ||
          text === "ת.ז." ||
          text === "תעודת זהות" ||
          text.includes("מס תז") ||
          text.includes("מספר זהות") ||
          text.includes("ת.ז."))
      );
    });

    console.log("Found ID fields:", idFields.length);

    // For each potential field, add the ID number and improve styling
    idFields.forEach((field) => {
      // Create an ID display element
      const idDisplay = document.createElement("span");
      idDisplay.textContent = idNo;
      idDisplay.style.cssText = `
        font-weight: bold;
        color: #1991d0;
        margin-right: 8px;
        padding: 2px 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
        display: inline-block;
        font-size: 15px;
      `;

      // Improve the field label styling
      field.style.cssText = `
        display: flex;
        align-items: center;
        margin: 5px 0;
        font-weight: bold;
      `;

      // Clear the field's existing content except for the label text
      const labelText = field.textContent.trim();
      field.innerHTML = "";
      field.textContent = labelText + ": ";

      // Append the ID number
      field.appendChild(idDisplay);
    });

    return tempDiv.innerHTML;
  };

  return (
    <div className="printView">
      {loading && !responseData && (
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      )}

      {responseData?.data?.Html ? (
        <div
          ref={showHtmlRef}
          className="showHtml"
          dangerouslySetInnerHTML={{ __html: responseData.data.Html }}
        />
      ) : (
        responseData && (
          <h1 style={{ color: "red", textAlign: "center", padding: "20px 0" }}>
            שגיאה
          </h1>
        )
      )}
    </div>
  );
}
