/**
 * documentProcessors.js - מעבדי מסמכים ספציפיים
 *
 * מכיל פונקציות לעיבוד וטיפול בסוגים שונים של מסמכים:
 * - טופס 101
 * - אישור תקופת שירות (102)
 * - דוח תקבולים (104)
 */

import dayjs from "dayjs";
import "dayjs/locale/he";
import hebrewDate from "hebrew-date";
dayjs.locale("he");

/**
 * מטפל בטופס 101 - מכין את המסמך לתצוגה בהתאם לסטנדרטים הנדרשים
 * הערה: מועדף להשתמש בתבנית האחידה החדשה
 * @param {HTMLElement} tempDiv - אלמנט HTML זמני המכיל את המסמך
 * @param {string} logo - הלוגו להוספה למסמך
 * @returns {void} - מעדכן את אלמנט ה-HTML שהתקבל
 */
export const processForm101 = (tempDiv, logo) => {
  console.log("טיפול בטופס 101 - סידור לוגו ותאריך");

  // --- מחק את כל אלמנטי בס"ד וטפל בהם מחדש ---
  const besdElements = Array.from(tempDiv.querySelectorAll("*")).filter(
    (el) => el.textContent && el.textContent.trim() === 'בס"ד'
  );

  console.log("נמצאו", besdElements.length, 'אלמנטים של בס"ד');

  // מחק את כל אלמנטי בס"ד
  besdElements.forEach((element) => {
    if (element.parentNode) {
      element.parentNode.removeChild(element);
    }
  });

  // --- נקה את כל התאריכים, מחק בס"ד שמופיע בתוכם ---
  // איתור תאריכים
  const dateElements = Array.from(tempDiv.querySelectorAll("*")).filter(
    (el) => {
      const text = el.textContent?.trim() || "";
      return (
        (text.includes("/") && text.length < 30) ||
        (text.includes("תשפ") && text.length < 35) ||
        text.includes("באייר") ||
        text.match(/\d{1,2}\/\d{1,2}\/\d{4}/) ||
        text.includes("תאריך")
      );
    }
  );

  // ניקוי תאריכים מבס"ד
  let foundDateContent = "";
  dateElements.forEach((element) => {
    // נקה את התוכן מבס"ד
    let content = element.textContent.trim();

    // הסר בס"ד מהתאריך אם קיים
    if (content.includes('בס"ד')) {
      content = content.replace(/בס"ד/g, "").trim();
      element.textContent = content;
    }

    // שמור את התוכן של התאריך הראשון שמצאנו
    if (!foundDateContent && content.match(/\d{1,2}\/\d{1,2}\/\d{4}/)) {
      foundDateContent = content;
    }

    // מחק את אלמנט התאריך המקורי
    if (element.parentNode) {
      element.parentNode.removeChild(element);
    }
  });

  // אם לא מצאנו תוכן תאריך, השתמש בתאריך הנוכחי
  if (!foundDateContent) {
    const currentDate = new Date();
    const day = currentDate.getDate().toString().padStart(2, "0");
    const month = (currentDate.getMonth() + 1).toString().padStart(2, "0");
    const year = currentDate.getFullYear();
    foundDateContent = `${day}/${month}/${year}`;
  }

  // --- מחק את כל הלוגואים הקיימים ---
  const allLogos = tempDiv.querySelectorAll(
    'img[src*="logo"], img[alt*="logo"]'
  );
  allLogos.forEach((logoElement) => {
    if (logoElement.parentNode) {
      logoElement.parentNode.removeChild(logoElement);
    }
  });

  // --- צור מחדש את כל האלמנטים במבנה הרצוי: לוגו ראשון, אחריו בס"ד ותאריך ---

  // 1. קודם נוסיף את הלוגו בראש המסמך במרכז
  const logoDiv = document.createElement("div");
  logoDiv.style.cssText = `
    text-align: center !important;
    width: 100%;
    margin: 10px auto 15px auto;
  `;
  logoDiv.innerHTML = `<img src="${logo}" alt="לוגו השירות הלאומי" 
                         style="width: 120px; height: auto; margin: 0 auto; display: block;" />`;

  // נוסיף את הלוגו בתחילת המסמך
  tempDiv.insertBefore(logoDiv, tempDiv.firstChild);

  // 2. צור מיכל לבס"ד ותאריך מיושרים לימין
  const headerRightDiv = document.createElement("div");
  headerRightDiv.style.cssText = `
    display: flex;
    flex-direction: column;
    text-align: right;
    width: 100%;
    margin-bottom: 15px;
  `;

  // יצירת אלמנט בס"ד מוצמד לימין
  const besdDiv = document.createElement("div");
  besdDiv.style.cssText = `
    text-align: right !important; 
    margin-right: 20px; 
    width: 100%; 
    font-size: 12pt; 
    font-weight: normal;
  `;
  besdDiv.innerHTML = 'בס"ד';
  headerRightDiv.appendChild(besdDiv);

  // יצירת אלמנט תאריך מוצמד לימין מתחת לבס"ד
  const dateDiv = document.createElement("div");
  dateDiv.style.cssText = `
    text-align: right !important;
    margin-right: 20px;
    width: 100%;
    font-size: 11pt;
    margin-top: 3px;
  `;
  dateDiv.innerHTML = foundDateContent;
  headerRightDiv.appendChild(dateDiv);

  // נוסיף את המיכל אחרי הלוגו
  tempDiv.insertBefore(headerRightDiv, logoDiv.nextSibling);
};

/**
 * מטפל בטבלה באישור תקופת שירות - מוודא שהטבלה מסודרת כראוי
 * הערה: מועדף להשתמש בתבנית האחידה החדשה
 * @param {HTMLElement} tempDiv - אלמנט HTML זמני המכיל את המסמך
 * @returns {void} - מעדכן את אלמנט ה-HTML שהתקבל
 */
export const processServicePeriod = (tempDiv) => {
  console.log("מעבד טבלת תקופת שירות - מחליף בין עמודות מתאריך ועד תאריך");

  // נמצא את כל הטבלאות במסמך
  const tables = tempDiv.querySelectorAll("table");

  // אם אין טבלאות, ננסה למצוא אלמנטים אחרים שעשויים להכיל את התאריכים
  if (tables.length === 0) {
    processNonTableDateElements(tempDiv);
    return;
  }

  // נעבור על כל טבלה במסמך
  tables.forEach((table, tableIndex) => {
    console.log(`מעבד טבלה מספר ${tableIndex + 1}`);

    // איתור שורות הטבלה
    const rows = table.querySelectorAll("tr");
    if (rows.length === 0) return;

    // ניסיון #1: חיפוש על פי כותרות מדויקות
    if (swapColumnsBasedOnHeaders(table, rows)) {
      console.log(`טבלה ${tableIndex + 1}: הוחלפו עמודות על פי כותרות`);
      return;
    }

    // ניסיון #2: חיפוש על פי תוכן תאים - זיהוי תאריכים
    if (swapColumnsBasedOnDateContent(table, rows)) {
      console.log(`טבלה ${tableIndex + 1}: הוחלפו עמודות על פי תוכן תאריכים`);
      return;
    }

    // ניסיון #3: החלפה מאולצת של העמודות הראשונות
    if (rows.length > 1 && rows[0].querySelectorAll("th, td").length >= 2) {
      console.log(
        `טבלה ${tableIndex + 1}: ביצוע החלפה מאולצת של שתי העמודות הראשונות`
      );
      forceSwapFirstTwoColumns(table);
    }
  });

  // נטפל בטקסט חופשי במסמך למקרה שהתאריכים לא בטבלה
  swapDatesInFreeText(tempDiv);
};

/**
 * מחפש ומחליף בין עמודות על פי כותרות
 * @param {HTMLElement} table - טבלה לעיבוד
 * @param {NodeList} rows - שורות הטבלה
 * @returns {boolean} - האם ההחלפה בוצעה
 */
function swapColumnsBasedOnHeaders(table, rows) {
  // מערכים של מילות מפתח שיכולות להופיע בכותרות
  const fromDateKeywords = [
    "מתאריך",
    "מ-תאריך",
    "תאריך התחלה",
    "תחילת שירות",
    "תאריך תחילה",
  ];
  const toDateKeywords = [
    "עד תאריך",
    "עד-תאריך",
    "תאריך סיום",
    "סיום שירות",
    "תאריך סוף",
  ];

  // ניקח את השורה הראשונה כשורת כותרות
  const headerRow = rows[0];
  const headerCells = headerRow.querySelectorAll("th, td");

  let fromDateIndex = -1;
  let toDateIndex = -1;

  // חיפוש עמודות המתאימות לכותרות
  for (let i = 0; i < headerCells.length; i++) {
    const cellText = headerCells[i].textContent.trim();

    // בדיקה אם התא מכיל מילת מפתח של תאריך התחלה
    if (
      fromDateIndex === -1 &&
      fromDateKeywords.some((keyword) => cellText.includes(keyword))
    ) {
      fromDateIndex = i;
    }

    // בדיקה אם התא מכיל מילת מפתח של תאריך סיום
    if (
      toDateIndex === -1 &&
      toDateKeywords.some((keyword) => cellText.includes(keyword))
    ) {
      toDateIndex = i;
    }
  }

  // אם מצאנו את שתי העמודות
  if (fromDateIndex !== -1 && toDateIndex !== -1) {
    // הפעולה תלויה באיזה מצב אנו רוצים:
    // - אם רוצים שמתאריך יופיע לפני עד-תאריך
    if (fromDateIndex > toDateIndex) {
      swapTableColumns(table, fromDateIndex, toDateIndex);
      return true;
    }
    // - אם רוצים שעד-תאריך יופיע לפני מתאריך (ההפך מהמצב הקודם)
    else if (fromDateIndex < toDateIndex) {
      swapTableColumns(table, fromDateIndex, toDateIndex);
      return true;
    }
  }

  return false;
}

/**
 * מחפש ומחליף בין עמודות על פי תוכן תאריכים
 * @param {HTMLElement} table - טבלה לעיבוד
 * @param {NodeList} rows - שורות הטבלה
 * @returns {boolean} - האם ההחלפה בוצעה
 */
function swapColumnsBasedOnDateContent(table, rows) {
  if (rows.length <= 1) return false;

  // תבנית לזיהוי תאריכים בפורמט DD/MM/YYYY או D/M/YYYY
  const datePattern = /\b(\d{1,2})\/(\d{1,2})\/(\d{4})\b/;

  // מצא עמודות שמכילות תאריכים
  let dateColumns = [];

  // בדוק בכל השורות פרט לשורת הכותרת
  for (let i = 1; i < Math.min(rows.length, 5); i++) {
    const cells = rows[i].querySelectorAll("td, th");

    for (let j = 0; j < cells.length; j++) {
      const cellText = cells[j].textContent.trim();

      if (datePattern.test(cellText) && !dateColumns.includes(j)) {
        dateColumns.push(j);
      }
    }
  }

  // אם מצאנו בדיוק שתי עמודות תאריך
  if (dateColumns.length === 2) {
    // נניח שהעמודה הראשונה במערך היא מתאריך והשנייה היא עד-תאריך
    // או להיפך, תלוי באיזה סדר רוצים
    swapTableColumns(table, dateColumns[0], dateColumns[1]);
    return true;
  }

  return false;
}

/**
 * מחליף בין שתי עמודות בטבלה
 * @param {HTMLElement} table - הטבלה לעדכון
 * @param {number} col1 - אינדקס העמודה הראשונה
 * @param {number} col2 - אינדקס העמודה השנייה
 */
function swapTableColumns(table, col1, col2) {
  // מסדר את האינדקסים בסדר עולה כדי להקל על הקוד
  const minCol = Math.min(col1, col2);
  const maxCol = Math.max(col1, col2);

  const rows = table.querySelectorAll("tr");

  // נעבור על כל שורה ונחליף בין התאים
  rows.forEach((row) => {
    const cells = row.querySelectorAll("th, td");

    if (cells.length > maxCol) {
      // שמירת התוכן והמאפיינים של התאים
      const tempHTML = cells[minCol].innerHTML;
      const tempClassName = cells[minCol].className;
      const tempStyle = cells[minCol].getAttribute("style");

      // החלפת התוכן והמאפיינים
      cells[minCol].innerHTML = cells[maxCol].innerHTML;
      cells[minCol].className = cells[maxCol].className;
      cells[minCol].setAttribute(
        "style",
        cells[maxCol].getAttribute("style") || ""
      );

      cells[maxCol].innerHTML = tempHTML;
      cells[maxCol].className = tempClassName;
      cells[maxCol].setAttribute("style", tempStyle || "");
    }
  });
}

/**
 * מאלץ החלפה בין שתי העמודות הראשונות
 * @param {HTMLElement} table - הטבלה לעדכון
 */
function forceSwapFirstTwoColumns(table) {
  const rows = table.querySelectorAll("tr");

  rows.forEach((row) => {
    const cells = row.querySelectorAll("th, td");

    if (cells.length >= 2) {
      // החלפת התוכן של שתי העמודות הראשונות
      const tempHTML = cells[0].innerHTML;
      cells[0].innerHTML = cells[1].innerHTML;
      cells[1].innerHTML = tempHTML;
    }
  });
}

/**
 * מטפל באלמנטים שאינם בטבלה אך מכילים תאריכים
 * @param {HTMLElement} tempDiv - אלמנט HTML להתאמה
 */
function processNonTableDateElements(tempDiv) {
  console.log("מחפש תאריכים מחוץ לטבלאות");

  // חיפוש אלמנטים שעשויים להכיל תאריכי התחלה וסיום
  const startDateElements = Array.from(tempDiv.querySelectorAll("*")).filter(
    (el) => {
      const text = el.textContent.trim();
      return (
        (text.includes("מתאריך") ||
          text.includes("תאריך התחלה") ||
          text.includes("תחילת שירות")) &&
        /\d{1,2}\/\d{1,2}\/\d{4}/.test(text)
      );
    }
  );

  const endDateElements = Array.from(tempDiv.querySelectorAll("*")).filter(
    (el) => {
      const text = el.textContent.trim();
      return (
        (text.includes("עד תאריך") ||
          text.includes("תאריך סיום") ||
          text.includes("סיום שירות")) &&
        /\d{1,2}\/\d{1,2}\/\d{4}/.test(text)
      );
    }
  );

  // אם מצאנו את שני סוגי האלמנטים
  if (startDateElements.length > 0 && endDateElements.length > 0) {
    console.log("נמצאו אלמנטי תאריך התחלה וסיום - מחליף סדר");

    // נחליף בין המיקומים של זוג האלמנטים הראשון
    const startEl = startDateElements[0];
    const endEl = endDateElements[0];

    if (startEl.parentNode === endEl.parentNode) {
      const parent = startEl.parentNode;

      // בדוק את הסדר הנוכחי
      const startIndex = Array.from(parent.childNodes).indexOf(startEl);
      const endIndex = Array.from(parent.childNodes).indexOf(endEl);

      if (startIndex !== -1 && endIndex !== -1) {
        // נעתיק את ה-HTML והמאפיינים
        const startHTML = startEl.outerHTML;
        const endHTML = endEl.outerHTML;

        // מחליף את האלמנטים
        startEl.outerHTML = endHTML;
        endEl.outerHTML = startHTML;
      }
    }
  }
}

/**
 * מחליף בין תאריכים בטקסט חופשי
 * @param {HTMLElement} tempDiv - אלמנט HTML להתאמה
 */
function swapDatesInFreeText(tempDiv) {
  console.log("מחפש תאריכים בטקסט חופשי להחלפה");

  // מערכים של מילות מפתח לזיהוי תאריכי התחלה וסיום
  const startDatePhrases = ["מתאריך", "מ-תאריך", "תאריך התחלה", "תחילת שירות"];
  const endDatePhrases = ["עד תאריך", "עד-תאריך", "תאריך סיום", "סיום שירות"];

  // תבנית לזיהוי תאריכים
  const datePattern = /\b(\d{1,2})\/(\d{1,2})\/(\d{4})\b/;

  // נמצא את כל אלמנטי הטקסט שעשויים להכיל תאריכים
  const textElements = Array.from(
    tempDiv.querySelectorAll("p, div, span, td, th, li")
  ).filter((el) => datePattern.test(el.textContent.trim()));

  // מידע על התאריכים שנמצאו
  let startDateInfo = null;
  let endDateInfo = null;

  // חיפוש אלמנטים המכילים תאריכי התחלה וסיום
  for (const el of textElements) {
    const text = el.textContent.trim();

    // בדיקה אם זה תאריך התחלה
    if (
      !startDateInfo &&
      startDatePhrases.some((phrase) => text.includes(phrase))
    ) {
      const match = text.match(datePattern);
      if (match) {
        startDateInfo = { element: el, fullMatch: match[0] };
      }
    }

    // בדיקה אם זה תאריך סיום
    if (
      !endDateInfo &&
      endDatePhrases.some((phrase) => text.includes(phrase))
    ) {
      const match = text.match(datePattern);
      if (match) {
        endDateInfo = { element: el, fullMatch: match[0] };
      }
    }

    // אם מצאנו את שני סוגי התאריכים, נפסיק לחפש
    if (startDateInfo && endDateInfo) break;
  }

  // אם מצאנו את שני סוגי התאריכים, נחליף ביניהם
  if (startDateInfo && endDateInfo) {
    console.log("נמצאו תאריכי התחלה וסיום בטקסט - מחליף תאריכים");

    // נשמור את התאריכים המקוריים
    const startDate = startDateInfo.fullMatch;
    const endDate = endDateInfo.fullMatch;

    // נחליף את התאריכים
    startDateInfo.element.innerHTML = startDateInfo.element.innerHTML.replace(
      startDate,
      endDate
    );
    endDateInfo.element.innerHTML = endDateInfo.element.innerHTML.replace(
      endDate,
      startDate
    );
  }
}

/**
 * מטפל בדוח תקבולים - מוודא שהתאריכים מוצגים בפורמט נכון
 * הערה: מועדף להשתמש בתבנית האחידה החדשה
 * @param {HTMLElement} tempDiv - אלמנט HTML זמני המכיל את המסמך
 * @returns {void} - מעדכן את אלמנט ה-HTML שהתקבל
 */
export const processReceiptReport = (tempDiv) => {
  console.log("טיפול ספציפי בדוח תקבולים - עדכון תאריכים");

  // החלפה ברמת ה-HTML המלא
  let fullHtml = tempDiv.innerHTML;

  // פונקציה שמחליפה כל הופעה של תאריך בפורמט MM/DD/YYYY ל-DD/MM/YYYY
  function convertDates(html) {
    return html.replace(
      /(\d{1,2})\/(\d{1,2})\/(\d{4})/g,
      function (match, p1, p2, p3) {
        // וודא שזה באמת בפורמט MM/DD/YYYY
        if (
          parseInt(p1) >= 1 &&
          parseInt(p1) <= 12 &&
          parseInt(p2) >= 1 &&
          parseInt(p2) <= 31
        ) {
          // בדוק אם p1 גדול מ-12, אז זה בוודאות כבר בפורמט DD/MM
          if (parseInt(p1) > 12) {
            return match; // כבר בפורמט הנכון
          }
          // המר לפורמט DD/MM/YYYY
          return p2.padStart(2, "0") + "/" + p1.padStart(2, "0") + "/" + p3;
        }
        return match;
      }
    );
  }

  // החלף את כל התאריכים ב-HTML
  fullHtml = convertDates(fullHtml);

  // תאריך לועזי מעודכן - נוודא שהוא מעודכן באמת
  const currentDate = new Date();
  const formattedCurrentDate =
    currentDate.getDate().toString().padStart(2, "0") +
    "/" +
    (currentDate.getMonth() + 1).toString().padStart(2, "0") +
    "/" +
    currentDate.getFullYear();

  console.log("התאריך הלועזי הנוכחי:", formattedCurrentDate);

  // טיפול בתאריכים - שלב 1: טיפול בתאריכים יחד עם טקסט מלווה
  const allParagraphs = tempDiv.querySelectorAll("p, div, span, td, th");
  allParagraphs.forEach((paragraph) => {
    // רק תפוס אלמנטים שמכילים תאריכים אבל לא שדות ת.ז.
    if (
      /\d{1,2}\/\d{1,2}\/\d{4}/.test(paragraph.textContent) &&
      !paragraph.textContent.includes("ת.ז") &&
      !paragraph.textContent.includes("זהות")
    ) {
      // החלפת תאריכים בתוך טקסט
      paragraph.innerHTML = paragraph.innerHTML.replace(
        /(\d{1,2})\/(\d{1,2})\/(\d{4})/g,
        formattedCurrentDate
      );
    }
  });

  // טיפול בתאריכים - שלב 2: חיפוש תאריכים בכל ה-HTML
  const dateRegex = /\b(\d{1,2})\/(\d{1,2})\/(\d{4})\b/g;

  // עדכן את ה-HTML עם הטיפול בפסקאות
  fullHtml = tempDiv.innerHTML;

  // מחליף את כל התאריכים ב-HTML בתאריך העדכני
  fullHtml = fullHtml.replace(dateRegex, formattedCurrentDate);

  // עדכן את ה-HTML
  tempDiv.innerHTML = fullHtml;

  // בדיקה סופית שבאמת עדכנו את כל התאריכים
  setTimeout(() => {
    const remainingDates = [];
    const dateNodes = tempDiv.querySelectorAll("*");
    dateNodes.forEach((node) => {
      if (
        /\d{1,2}\/\d{1,2}\/\d{4}/.test(node.textContent) &&
        !node.textContent.includes(formattedCurrentDate) &&
        !node.textContent.includes("ת.ז") &&
        !node.textContent.includes("זהות")
      ) {
        remainingDates.push(node.textContent);
        // עדכון ישירות
        node.textContent = node.textContent.replace(
          /\d{1,2}\/\d{1,2}\/\d{4}/g,
          formattedCurrentDate
        );
      }
    });

    if (remainingDates.length > 0) {
      console.log("נמצאו תאריכים שלא עודכנו:", remainingDates);
    }
  }, 10);
};

/**
 * עוזר לעיבוד תאריכים - מחליף מחרוזות תאריך במבנה חודש/יום/שנה למבנה יום/חודש/שנה
 * @param {string} html - מחרוזת HTML המכילה תאריכים
 * @returns {string} - מחרוזת HTML עם תאריכים מתוקנים
 */
function convertDates(html) {
  // ... existing code ...
}

/**
 * מתקן תאריכים בכל אלמנט הTempDiv
 * פונקציה זו עדיין בשימוש פעיל בתבנית החדשה
 * @param {HTMLElement} tempDiv - אלמנט HTML להתאמת תאריכים בו
 * @returns {void} - מעדכן את אלמנט ה-HTML שהתקבל
 */
export const fixDatesInDocument = (tempDiv) => {
  function fixDateInString(text) {
    if (!text) return text;

    // זיהוי האם המחרוזת מכילה "ת.ז." או "ת.ז" או מספר זהות
    const isIdNumber =
      text.includes("ת.ז.") ||
      text.includes("ת.ז") ||
      text.includes("מספר זהות") ||
      text.includes('ת"ז') ||
      /[^\d](\d{5,9})[^\d]/.test(text);

    // אם זה ת.ז., אל תבצע שינויים
    if (isIdNumber) {
      return text;
    }

    // פורמט MM/DD/YY לפורמט DD/MM/YYYY
    text = text.replace(
      /(\d{1,2})\/(\d{1,2})\/(\d{2})(?!\d)/g,
      (match, month, day, year) => {
        // בדיקה שזה באמת תאריך (חודש לא יכול להיות גדול מ-12)
        if (parseInt(month) > 12) {
          // אם חודש גדול מ-12, אז כנראה זה כבר בפורמט DD/MM
          return match;
        }

        const formattedMonth = month.padStart(2, "0");
        const formattedDay = day.padStart(2, "0");
        return `${formattedDay}/${formattedMonth}/20${year}`;
      }
    );

    // פורמט YYYYMMDD לפורמט DD/MM/YYYY
    text = text.replace(
      /\b(\d{4})(\d{2})(\d{2})\b/g,
      (match, year, month, day) => {
        // וידוא שזה תאריך תקף (חודש בין 1-12, יום בין 1-31)
        if (
          parseInt(month) > 12 ||
          parseInt(month) < 1 ||
          parseInt(day) > 31 ||
          parseInt(day) < 1
        ) {
          return match;
        }
        return `${day}/${month}/${year}`;
      }
    );

    // פורמט MM/DD/YYYY לפורמט DD/MM/YYYY
    text = text.replace(
      /(\d{1,2})\/(\d{1,2})\/(\d{4})/g,
      (match, part1, part2, year) => {
        // בדיקה שזה באמת תאריך בפורמט MM/DD/YYYY
        if (
          parseInt(part1) <= 12 &&
          parseInt(part1) > 0 &&
          parseInt(part2) <= 31 &&
          parseInt(part2) > 0
        ) {
          const month = part1.padStart(2, "0");
          const day = part2.padStart(2, "0");
          return `${day}/${month}/${year}`;
        }
        return match; // ייתכן שזה כבר בפורמט DD/MM/YYYY
      }
    );

    // הוסף אפס בהתחלה לתאריכים חסרים
    text = text.replace(
      /\b(\d)\/(\d{1,2})\/(\d{4})\b/g,
      (match, day, month, year) => {
        return `0${day}/${month.padStart(2, "0")}/${year}`;
      }
    );

    text = text.replace(
      /\b(\d{2})\/(\d)\/(\d{4})\b/g,
      (match, day, month, year) => {
        return `${day}/0${month}/${year}`;
      }
    );

    return text;
  }

  // טיפול בכל אלמנטי טקסט
  const textNodes = [];
  const walker = document.createTreeWalker(
    tempDiv,
    NodeFilter.SHOW_TEXT,
    null,
    false
  );

  let node;
  while ((node = walker.nextNode())) {
    // הוסף בדיקה שהטקסט לא כולל ת.ז. או מספר זהות
    if (
      !node.nodeValue.includes("ת.ז.") &&
      !node.nodeValue.includes("ת.ז") &&
      !node.nodeValue.includes("מספר זהות") &&
      !node.nodeValue.includes('ת"ז') &&
      (node.nodeValue.match(/\d+\/\d+\/\d+/) ||
        node.nodeValue.match(/\b\d{8}\b/))
    ) {
      textNodes.push(node);
    }
  }

  textNodes.forEach((node) => {
    node.nodeValue = fixDateInString(node.nodeValue);
  });

  // טיפול באלמנטים עם שדות ת.ז.
  const idElements = Array.from(
    tempDiv.querySelectorAll(
      '[data-id], [data-field="id"], .idNumber, #idNumber'
    )
  );
  idElements.forEach((el) => {
    // סמן שלא לבצע תיקון
    el.setAttribute("data-skip-date-fix", "true");
  });

  // טיפול בתכונות של אלמנטים
  const elementsWithAttrs = tempDiv.querySelectorAll(
    "*:not([data-skip-date-fix='true'])"
  );
  elementsWithAttrs.forEach((el) => {
    Array.from(el.attributes).forEach((attr) => {
      if (attr.value.match(/\d+\/\d+\/\d+/) || attr.value.match(/\b\d{8}\b/)) {
        // בדיקה שלא מדובר בת.ז.
        if (
          !attr.name.toLowerCase().includes("id") &&
          !attr.value.includes("ת.ז.") &&
          !attr.value.includes("ת.ז") &&
          !attr.value.includes("מספר זהות")
        ) {
          el.setAttribute(attr.name, fixDateInString(attr.value));
        }
      }
    });
  });
};

export const getCurrentHebrewDate = () => {
  const today = new Date();
  const hd = hebrewDate(today);
  // דוגמה: י"א באייר תשפ"ד
  return `${hd.hd} ב${hd.hm} ${hd.hy}`;
};
