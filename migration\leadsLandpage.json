{"table": "leadsLandpage", "controller": "welcome", "method": "", "explain": "leadsLandpage", "title": "FirstName", "description": "IDNO", "seo_title": "", "seo_description": "", "image": "", "fields": {"IDNO": {"sort": "200", "width": "col-md-4", "name": "IDNO", "type": "short", "explain": "IDNO", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "FirstName": {"sort": "190", "width": "col-md-4", "name": "FirstName", "type": "short", "explain": "FirstName", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "LastName": {"sort": "180", "width": "col-md-4", "name": "LastName", "type": "short", "explain": "LastName", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Mobile": {"sort": "170", "width": "col-md-4", "name": "Mobile", "type": "short", "explain": "Mobile", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Email": {"sort": "160", "width": "col-md-4", "name": "Email", "type": "short", "explain": "Email", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "text": {"sort": "150", "width": "col-md-4", "name": "text", "type": "long", "explain": "text", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "divur": {"sort": "140", "width": "col-md-4", "name": "divur", "type": "short", "explain": "divur", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "utm_source": {"sort": "130", "width": "col-md-4", "name": "utm_source", "type": "short", "explain": "utm_source", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "utm_medium": {"sort": "120", "width": "col-md-4", "name": "utm_medium", "type": "short", "explain": "utm_medium", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "PrvSchool": {"sort": "115", "width": "col-md-4", "name": "PrvSchool", "type": "short", "explain": "PrvSchool", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "response": {"sort": "110", "width": "col-md-4", "name": "response", "type": "long", "explain": "response", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}}}