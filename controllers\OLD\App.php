<?php defined('BASEPATH') OR exit('No direct script access allowed');

class App extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+OtzarHaaretz';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+OtzarHaaretz-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
        
        $this->load->model('msiteWs');
        $this->load->model('OtzarHaretz');
        
        $controller = 'app';
        
        $this->data['current_language'] = 'he';
        $this->data['controller'] = $this->router->fetch_class();
        $this->data['method'] = $this->router->fetch_method();
        $this->data['param'] = $param;
        $this->data['settings'] = $this->msiteWs->get_settings();
        $this->data['pages'] = $this->msite->get_all_pages($parent_id = 0, $lang = NULL, $controller);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages'][$controller.'_index'];
        
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id(),$this->data['current_language']);
        
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }

//        
        
        
    }
    
    public function SiteDataItems ($param = FALSE) {
        
        //echo "sebas";
        
        $this->_loaderWS($param);
        
        $output['pages'] =  $this->msiteWs->get_pagesWs($this->data['pages']);
        $output['settings'] = $this->data['settings'];
        
        $output['SystemData'] = $this->msiteWs->get_page_with_objectsArray(39,$this->data['current_language']);
        
        if($this->input->get('searchDataMenuItems',TRUE) == 1) {
            $output['searchDataMenu'] = $this->OtzarHaretz->getSuppliers4Search();
        }
        
        
        //$output['contactData'] = $this->msiteWs->get_page_with_objectsArray(16,$this->data['current_language']);
        
        //GET PAGES WITH ID!!!
        //$output['paramPages'] = $this->msiteWs->getParamPages();
        
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function index($param = FALSE) {
        $this->_loaderWS($param);
        
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        $output['data'] = 'OK';
         
        if($checkUserCredentials != 'unauthorized') {
            
            
            if( $jsonPosts['userCredential'] == 'superAdmin' ) {
                
                $this->load->model('OtzarHaretz');
                
                $output['numbers'] = $this->OtzarHaretz->GetSystemHomeData();
                
                
            }
            
//            $this->msite->set_where("status='1'");
//            $this->msite->sort_objects("sort", "DESC");
//            $first_branches1 = $this->msite->get_all_objects('first_branches');
//            $output['gallery'] = $this->msiteWs->objects_to_ArrayNoseoData($first_branches1);
            
            
            $output['data'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
//    SYSTEM PAGES!!
    
    public function clients($param = FALSE) {
        $this->_loaderWS($param);
        
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        $output['data'] = 'OK';
         
        if($checkUserCredentials != 'unauthorized') {
            
            
//            $this->msite->set_where("status='1'");
//            $this->msite->sort_objects("sort", "DESC");
//            $first_branches1 = $this->msite->get_all_objects('first_branches');
//            $output['gallery'] = $this->msiteWs->objects_to_ArrayNoseoData($first_branches1);
            
            
            $output['clients'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function suppliers($param = FALSE) {
        $this->_loaderWS($param);
        
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        $output['data'] = 'OK';
         
        if($checkUserCredentials != 'unauthorized') {
            
            
            $output['suppliers'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function reports ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        $output['data'] = 'OK';
         
        if($checkUserCredentials != 'unauthorized') {
            
            $output['reports'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function subscription ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
         
        if($checkUserCredentials != 'unauthorized') {
            
            $output['subscription'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function checkPage ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
//            $this->msite->set_where("status='1'");
//            $this->msite->sort_objects("sort", "DESC");
//            $first_branches1 = $this->msite->get_all_objects('first_branches');
//            $output['gallery'] = $this->msiteWs->objects_to_ArrayNoseoData($first_branches1);
            
            
            $output['data'] = 'OK';
            $output['page'] = $this->data['page']->Arr();
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        //print_r($this->data['page']); die('asd');
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    
    
    public function insertToDb() {
        
        die();
        
//        https://data.gov.il/dataset/countries
        //https://help.zoho.com/portal/en/community/topic/hebrew-characters-imported-from-a-csv-file-are-not-displayed-right
        
        // choose "CSV (Comma Delimited) (*.csv)
        
        $fileData = 'csv/suppliers4Import4.csv';
        
        if (($handle = fopen(base_url().IMG.$fileData, "r")) !== FALSE) {
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $file[] = $data;
            }
        }
        
        fclose($handle);

        $table = 'suppliers';
        
//        echo "<pre>";
//                print_r($file);
//        echo "</pre>";
//        die();
        
        foreach ($file as $key => $value) {
            
            if($key>0) {
                
                if(!empty($value[5])) {

//                        echo "<pre>";
//                        print_r($value);
//                        echo "</pre>";die('value');

                        $data = array(
                            'lang' => NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            
                            'name' => $value[0].' - '.$value[1],
                            'phone' => str_replace("-", "",$value[4]),
                            'address' => $value[1],
                            'ownerName' => $value[2],
                            
                            'username' => $value[3],
                            'passwordMd5' => md5($value[3]),
                            
                            'tz' => $value[3],
                            'sign' => $value[5],
                            'signed' => $value[6],
                            'agent' => $value[7]
                        );
        //
        //
                    $insert = $this->db->insert($table, $data); 
                    $insert_id = $this->db->insert_id(); 

                    //$insert_id = 1;

                    if(!empty($insert_id)) {

                        $cashier = array(
                            'lang' => NULL,
                            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'name' => '1',
                            'code' => '123',
                            'supplierId' => $insert_id
                        );

                        $insert = $this->db->insert('cashiersSuppliers', $cashier);
                    }


//                    echo "<pre>";
//                        print_r($data);
//                        print_r($cashier);
//                    echo "</pre>";
//
//                    die('END');
                }
            
            }
        }
        
       

        
        echo "sebas";
    }
    
    
    
    
}
    

