.register-form {
  padding: 2rem 0;
  direction: rtl;
}

.register-form .topPic {
  width: 100%;
  margin-bottom: 2rem;
}

.register-form .loader {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.register-form .loader.active {
  display: block;
}

.register-form header {
  margin-bottom: 3rem;
}

.register-form header h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.register-form header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.register-form .form-input-wrapper {
  margin-bottom: 1rem;
}

.register-form .form-control,
.register-form .form-select {
  height: 3rem;
  border-radius: 0.5rem;
  border: 1px solid #dcdcdc;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  transition: border-color 0.15s ease-in-out;
}

.register-form .form-control:focus,
.register-form .form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.register-form .form-control.is-invalid,
.register-form .form-select.is-invalid {
  border-color: #dc3545;
}

.register-form .form-control.is-invalid:focus,
.register-form .form-select.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.register-form .gender-selection {
  text-align: center;
}

.register-form .gender-selection label {
  display: block;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: #2c3e50;
}

.register-form .gender-selection label.error {
  color: #dc3545;
}

.register-form .gender-selection .btn {
  padding: 0.5rem 2rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.register-form .gender-selection .btn img {
  margin-left: 0.5rem;
}

.register-form .terms {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
}

.register-form .terms .form-check {
  margin-bottom: 0.5rem;
}

.register-form .terms .form-check:last-child {
  margin-bottom: 0;
}

.register-form .terms a {
  color: #0d6efd;
  text-decoration: none;
}

.register-form .terms a:hover {
  text-decoration: underline;
}

.register-form .submit-btn {
  padding: 0.75rem 3rem;
  font-size: 1.1rem;
  background-color: #0d6efd;
  border: none;
  transition: all 0.2s ease-in-out;
}

.register-form .submit-btn:hover {
  background-color: #0b5ed7;
  transform: translateY(-1px);
}

.register-form .submit-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.register-form .login-link {
  margin-top: 1rem;
}

.register-form .login-link a {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

.register-form .login-link a:hover {
  color: #0d6efd;
}

.register-form .login-link strong {
  color: #0d6efd;
}

@media (max-width: 768px) {
  .register-form {
    padding: 1rem;
  }

  .register-form .gender-selection .btn {
    padding: 0.5rem 1rem;
  }

  .register-form .submit-btn {
    width: 100%;
    padding: 0.75rem;
  }
}
