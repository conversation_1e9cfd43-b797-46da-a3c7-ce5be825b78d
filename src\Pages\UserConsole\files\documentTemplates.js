/**
 * Document templates with unified header and consistent formatting
 *
 * כל מסמך כולל:
 * - לוגו האגודה בצד ימין למעלה
 */

// טמפלייט בסיסי אחיד לכל המסמכים
const baseDocumentTemplate = (content, title) => `
  <div class="document-container">
    <div class="document-header">
      <div class="header-right">
        <div class="logo-container">
          <img src="{{LOGO_PLACEHOLDER}}" alt="לוגו האגודה להתנדבות" class="header-logo" />
        </div>
      </div>
      <div class="header-center">
        <h1 class="document-title">${title}</h1>
      </div>
    </div>
    <div class="document-content">
      ${content}
    </div>
  </div>
`;

// מסנן תוכן HTML מדברים מיותרים
const cleanContent = (htmlContent) => {
  if (!htmlContent) return "";

  // מסיר כפילויות של תוכן
  let cleanedContent = htmlContent
    .replace(/<img[^>]*logo[^>]*>/gi, "") // הסרת לוגואים
    .replace(/<[^>]*>בס"ד<\/[^>]*>/gi, "") // הסרת בס"ד
    .replace(/<h1[^>]*>(טופס 101|אישור תקופת שירות|דוח תקבולים)<\/h1>/gi, ""); // הסרת כותרות מיותרות

  return cleanedContent;
};

// טופס 101 עם התבנית האחידה
export const form101Template = (data) => {
  const contentHtml = cleanContent(data.formContent);

  const content = `
    <div class="form-details">
      ${
        contentHtml
          ? contentHtml
          : `
      <div class="date-section">
        <span>תאריך תחילת שירות: </span>
        <span class="date-field">${data.startDate}</span>
      </div>
      <div class="date-section">
        <span>תאריך סיום שירות: </span>
        <span class="date-field">${data.endDate}</span>
      </div>
      `
      }
    </div>
  `;

  return baseDocumentTemplate(content, "טופס 101");
};

// אישור תקופת שירות עם התבנית האחידה
export const servicePeriodTemplate = (data) => {
  const contentHtml = cleanContent(data.certificateContent);

  const content = `
    <div class="certificate-details">
      ${
        contentHtml
          ? contentHtml
          : `
      <div class="service-dates">
        <div>
          <span>תאריך תחילת שירות: </span>
          <span class="service-date">${data.startDate}</span>
        </div>
        <div>
          <span>תאריך סיום שירות: </span>
          <span class="service-date">${data.endDate}</span>
        </div>
      </div>
      `
      }
    </div>
  `;

  return baseDocumentTemplate(content, "אישור תקופת שירות");
};

// דוח תקבולים עם התבנית האחידה
export const receiptReportTemplate = (data) => {
  const contentHtml = cleanContent(data.reportContent);

  const content = `
    <div class="report-details">
      ${
        contentHtml
          ? contentHtml
          : `
      <div class="report-period">
        <span>תקופת הדוח: </span>
        <span class="period-date">${data.periodStart}</span>
        <span>עד</span>
        <span class="period-date">${data.periodEnd}</span>
      </div>
      `
      }
    </div>
  `;

  return baseDocumentTemplate(content, "דוח תקבולים");
};

// טופס חדש כללי - ניתן להשתמש בו עבור כל סוג מסמך חדש
export const genericDocumentTemplate = (data) => {
  const content = cleanContent(data.content) || "";
  return baseDocumentTemplate(content, data.title || "מסמך");
};
