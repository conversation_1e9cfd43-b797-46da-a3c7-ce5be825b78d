
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MekSherut extends CI_Controller {
    public function __construct() {
        parent::__construct();
        $this->load->model('sherutLeumi');
    }

    public function getMekSherutNEW() {
        $mekSherutData = $this->sherutLeumi->apiClientGetMekSherutNEW();
        $this->output
            ->set_status_header(200)
            ->set_content_type('application/json')
            ->set_output(json_encode($mekSherutData));
    }

    public function populateMSherutNEW() {
        $result = $this->sherutLeumi->populateMSherutNEW();
        if ($result) {
            echo "Data populated successfully.";
        } else {
            echo "No data to populate.";
        }
    }
}
?>
