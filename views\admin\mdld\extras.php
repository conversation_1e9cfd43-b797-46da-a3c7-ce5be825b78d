<style>
.panel-body {
    padding: 5px 15px;
}
.row {
    margin-right: -5px;
    margin-left: -5px;
}
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
    
    padding-left: 5px;
    padding-right: 5px;
}
</style>
<div class="row">
    <?php if(isset($extras) && $extras) foreach ($extras as $row) { ?>
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-body">
                <h4><?php echo $row->Arg('en_name'); ?> / <?php echo $row->Arg('th_name'); ?></h4>
                
                <?php echo form_open_multipart('admin/object/product_extra/put/', array("class" => "ajax")); ?>
                <div class="row">
                    <?php if($row->Arg('type') === 'COLOR') { ?>
                    <div class="col-md-2">
                        <div class="form-group">
                            <input type="text" class="form-control colorpickerextra" name="color" value="" placeholder="צבע"/>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="en_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="en_value" data-ajax--text="en_value" data-placeholder="<?php echo $row->Arg('en_name'); ?>" data-allow-clear="true">
                                <option selected></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="th_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="th_value" data-ajax--text="th_value" data-placeholder="<?php echo $row->Arg('th_name'); ?>" data-allow-clear="true">
                                <option selected></option>
                            </select>
                        </div>
                    </div>
                    <?php } else { ?>
                    <div class="col-md-4">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="en_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="en_value" data-ajax--text="en_value" data-placeholder="<?php echo $row->Arg('en_name'); ?>" data-allow-clear="true">
                                <option selected></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="th_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="th_value" data-ajax--text="th_value" data-placeholder="<?php echo $row->Arg('th_name'); ?>" data-allow-clear="true">
                                <option selected></option>
                            </select>
                        </div>
                    </div>
                    <?php } ?>
                    <div class="col-md-2">
                        <input type="number" class="form-control" name="price" step="0.01" min="0" value="" placeholder="מחיר"/>
                    </div>
                    <div class="col-md-2">
                        <input type="hidden" name="product_id" value="<?php echo $product_id; ?>"/>
                        <input type="hidden" name="extra_id" value="<?php echo $row->Id(); ?>"/>
                        <button type="submit" class="btn btn-primary">הוסף</button>
                    </div>
                </div>
                <?php echo form_close(); ?>
                
                <?php $pvals = $row->Arr('pvals'); ?>
                
                <?php if(!empty($pvals)) { foreach($pvals as $pval) { ?>
                <?php echo form_open_multipart('admin/object/product_extra/update/' . $pval->Id() . '/', array("class" => "ajax")); ?>
                <div class="row">
                    <?php if($row->Arg('type') === 'COLOR') { ?>
                    <div class="col-md-2">
                        <div class="form-group">
                            <input type="text" class="form-control colorpickerextra" name="color" value="<?php echo $pval->Arg('color'); ?>" placeholder="צבע" />
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="en_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="en_value" data-ajax--text="en_value" data-placeholder="<?php echo $row->Arg('en_name'); ?>" data-allow-clear="true">
                                <option value="<?php echo $pval->Arg('en_value'); ?>" selected><?php echo $pval->Arg('en_value'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="th_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="th_value" data-ajax--text="th_value" data-placeholder="<?php echo $row->Arg('th_name'); ?>" data-allow-clear="true">
                                <option value="<?php echo $pval->Arg('th_value'); ?>" selected><?php echo $pval->Arg('th_value'); ?></option>
                            </select>
                        </div>
                    </div>
                    <?php } else { ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="en_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="en_value" data-ajax--text="en_value" data-placeholder="<?php echo $row->Arg('en_name'); ?>" data-allow-clear="true">
                                <option value="<?php echo $pval->Arg('en_value'); ?>" selected><?php echo $pval->Arg('en_value'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <select class="form-control select2ajax" style="width: 100%;" name="th_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_extra'); ?>" data-ajax--cache="true" data-ajax--id="th_value" data-ajax--text="th_value" data-placeholder="<?php echo $row->Arg('th_name'); ?>" data-allow-clear="true">
                                <option value="<?php echo $pval->Arg('th_value'); ?>" selected><?php echo $pval->Arg('th_value'); ?></option>
                            </select>
                        </div>
                    </div>
                    <?php } ?>
                    <div class="col-md-2">
                        <input type="number" class="form-control" name="price" step="0.01" min="0" value="<?php echo $pval->Arg('price'); ?>" placeholder="מחיר"/>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <div class="input-group">
                                <div class="input-group-btn">
                                    <button type="button" class="btn pulse-hover <?php echo $pval->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/object/product_extra/status/' . $pval->Id()); ?>">
                                        <?php echo $pval->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                                    </button> 
                                </div>
                                <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $pval->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/object/product_extra/sort/' . $pval->Id() . '/'); ?>">
                            </div>
                        </div>
                        
                    </div>
                    <div class="col-md-2">
                        <input type="hidden" name="product_id" value="<?php echo $product_id; ?>"/>
                        <input type="hidden" name="extra_id" value="<?php echo $row->Id(); ?>"/>
                        <div class="btn-group" role="group">
                            <button type="submit" class="btn btn-success">עדכן</button>
                            <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/object/product_extra/destroy/' . $pval->Id() . '/' . getQS()); ?>">
                                מחק
                            </button> 
                        </div>
                        
                    </div>
                </div>
                <?php echo form_close(); ?>
                <?php }} ?>
                
            </div>
        </div>
    </div>
    <?php } ?>
</div>