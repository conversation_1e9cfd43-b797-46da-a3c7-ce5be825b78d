<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Register extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        $this->load->library('logger');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('token'));
           if( $postCode['token'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }

    private function custom_log($message, $level = 'debug') {
        // נתיב לקובץ לוג מותאם אישית
        $log_file = APPPATH . 'logs/register_debug_' . date('Y-m-d') . '.log';
        error_log('[' . date('Y-m-d H:i:s') . ' | ' . strtoupper($level) . '] ' . $message . PHP_EOL, 3, $log_file);
        
        // כתוב גם ללוג הרגיל של CodeIgniter לגיבוי
        $this->logger->write_log($level, $message);
    }

    public function newRegister($jPData = FALSE) {
        $this->_loaderWS();
        $output['funcName'] = $this->router->fetch_method();
        
        // Log the entry to the function
        $this->custom_log('===== START: newRegister function called =====');
        $this->custom_log('Request headers: ' . json_encode(getallheaders(), JSON_UNESCAPED_UNICODE));
        
        $pageAutoriced = array('register');
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if( !$checkPageAuth && !$this->input->get('sebas')  ) { 
            $this->custom_log('Authentication failed: Unauthorized access', 'error');
            return $this->output->set_status_header(403); 
        }
        
        $getPosts = array(
            'FirstName',
            'LastName',
            'IDNO',
            'MobileStart',
            'Mobile', 
            'BirthDate',
            'CityCode',
            'Email',
            'PrvSchool',
            'sex',
            'Category', // Ensure 'Category' is included in the list of fields to retrieve
            'YearYad',
            'Password'
        );
        
        // Log the raw request body
        $raw_input = file_get_contents('php://input');
        $this->custom_log('RAW REQUEST BODY: ' . $raw_input);
        
        // Try to analyze and log the raw JSON for better visibility
        try {
            $raw_json = json_decode($raw_input, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->custom_log('JSON REQUEST DECODED: ' . json_encode($raw_json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            }
        } catch (Exception $e) {
            $this->custom_log('Failed to parse raw JSON input: ' . $e->getMessage(), 'error');
        }
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        
        // Detailed logging of raw input
        $this->custom_log('RAW INPUT FIELDS: ' . json_encode($pagePosts, JSON_UNESCAPED_UNICODE));
        
        // Log specific important fields
        $this->custom_log('IMPORTANT FIELDS CHECK:');
        $this->custom_log('- FirstName: ' . (isset($pagePosts['FirstName']) ? $pagePosts['FirstName'] : 'NOT SET'));
        $this->custom_log('- LastName: ' . (isset($pagePosts['LastName']) ? $pagePosts['LastName'] : 'NOT SET'));
        $this->custom_log('- IDNO: ' . (isset($pagePosts['IDNO']) ? $pagePosts['IDNO'] : 'NOT SET'));
        $this->custom_log('- BirthDate: ' . (isset($pagePosts['BirthDate']) ? $pagePosts['BirthDate'] : 'NOT SET'));
        $this->custom_log('- Mobile: ' . (isset($pagePosts['Mobile']) ? $pagePosts['Mobile'] : 'NOT SET'));
        $this->custom_log('- MobileStart: ' . (isset($pagePosts['MobileStart']) ? $pagePosts['MobileStart'] : 'NOT SET'));
        $this->custom_log('- Category: ' . (isset($pagePosts['Category']) ? $pagePosts['Category'] : 'NOT SET'));
        $this->custom_log('- sex: ' . (isset($pagePosts['sex']) ? $pagePosts['sex'] : 'NOT SET'));
        $this->custom_log('- YearYad: ' . (isset($pagePosts['YearYad']) ? $pagePosts['YearYad'] : 'NOT SET'));
        
        // Check for missing required fields
        $requiredFields = ['FirstName', 'LastName', 'IDNO', 'Mobile', 'BirthDate', 'CityCode', 'Email', 'sex', 'Category'];
        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (empty($pagePosts[$field])) {
                $missingFields[] = $field;
            }
        }
        
        if (!empty($missingFields)) {
            $this->custom_log('MISSING REQUIRED FIELDS: ' . implode(', ', $missingFields), 'error');
        }
        
        // Prepare data for the new server format
        $formattedPosts = [
            'firstName' => $pagePosts['FirstName'],
            'lastName' => $pagePosts['LastName'], 
            'sex' => $pagePosts['sex'] == 'man' ? 2 : 1,
            'category' => null, // Will be set after validation
            'imageUrl' => '',
            'idno' => $pagePosts['IDNO'],
            'password' => $pagePosts['Password'],
            'mobile' => $pagePosts['MobileStart'] . '-' . $pagePosts['Mobile'],
            'birthDate' => null, // Will be set after validation
            'cityCode' => $pagePosts['CityCode'],
            'email' => $pagePosts['Email'],
            'prvSchool' => $pagePosts['PrvSchool'],
            'yearYad' => $pagePosts['YearYad']
        ];
        
        // Validate and format category
        try {
            if (isset($pagePosts['Category'])) {
                if (is_numeric($pagePosts['Category'])) {
                    $formattedPosts['category'] = intval($pagePosts['Category']);
                } else {
                    throw new Exception('Invalid category format: ' . $pagePosts['Category']);
                }
            } else {
                throw new Exception('Category is required but not provided');
            }
            $this->custom_log('Category validated and formatted: ' . $formattedPosts['category']);
        } catch (Exception $e) {
            $this->custom_log('Category validation error: ' . $e->getMessage(), 'error');
            return $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'errorMessage' => 'Invalid category format'
                ]));
        }

        // Validate and format birth date
        try {
            if (isset($pagePosts['BirthDate']) && !empty($pagePosts['BirthDate'])) {
                $date = DateTime::createFromFormat('Y-m-d', $pagePosts['BirthDate']);
                if ($date === false) {
                    throw new Exception('Invalid date format. Expected Y-m-d, got: ' . $pagePosts['BirthDate']);
                }
                $formattedPosts['birthDate'] = $date->format('Ymd');
            } else {
                throw new Exception('Birth date is required but not provided');
            }
            $this->custom_log('Birth date validated and formatted: ' . $formattedPosts['birthDate']);
        } catch (Exception $e) {
            $this->custom_log('Birth date validation error: ' . $e->getMessage(), 'error');
            return $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'errorMessage' => 'Invalid birth date format'
                ]));
        }

        // Additional validation for mobile number
        if (!isset($pagePosts['MobileStart']) || !isset($pagePosts['Mobile'])) {
            $this->custom_log('Mobile number validation error: Missing mobile parts', 'error');
            return $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'error' => true,
                    'errorMessage' => 'Missing mobile number information'
                ]));
        }

        $this->custom_log('FORMATTED POSTS (BEFORE API CALL): ' . json_encode($formattedPosts, JSON_UNESCAPED_UNICODE));
        
        // Make sure Mobile is formatted correctly (without hyphens)
        if (isset($formattedPosts['mobile']) && strpos($formattedPosts['mobile'], '-') !== false) {
            $this->custom_log('Mobile contains hyphen: ' . $formattedPosts['mobile']);
        }
        
        // Validate IDNO is 9 digits
        if (isset($formattedPosts['idno'])) {
            $cleanIdno = preg_replace('/\D/', '', $formattedPosts['idno']);
            if (strlen($cleanIdno) !== 9) {
                $this->custom_log('Warning: IDNO is not 9 digits: ' . $formattedPosts['idno'] . ' (cleaned: ' . $cleanIdno . ')', 'warning');
            }
        }
        
        $this->custom_log('FINAL FORMATTED POSTS (AFTER TYPE CONVERSION): ' . json_encode($formattedPosts, JSON_UNESCAPED_UNICODE));
        
        $output['fields'] = $formattedPosts;
        
        // Log the API endpoint being called
        $this->custom_log('CALLING API ENDPOINT: v2/volunteer/registerSend');
        
        // Attempt to send request and capture full response
        $this->custom_log('SENDING DATA TO API: ' . json_encode($formattedPosts, JSON_UNESCAPED_UNICODE));
        
        try {
            // Add timeout and error handling for API call
            $response = $this->sherutLeumi->apiClientGetRegister('v2/volunteer/registerSend', $formattedPosts);
            
            if (!$response) {
                throw new Exception('Empty response from API');
            }
            
            if (!is_array($response)) {
                throw new Exception('Invalid response format from API: ' . print_r($response, true));
            }
            
            $this->custom_log('API CALL COMPLETED SUCCESSFULLY');
            $this->custom_log('API Response: ' . json_encode($response, JSON_UNESCAPED_UNICODE));
            
        } catch (Exception $e) {
            $this->custom_log('API CALL EXCEPTION: ' . $e->getMessage(), 'error');
            $this->custom_log('Request data that caused error: ' . json_encode($formattedPosts, JSON_UNESCAPED_UNICODE), 'error');
            
            // Set a generic error response
            $response = [
                'Result' => 'Error',
                'ErrorMessage' => 'שגיאת מערכת. אנא נסו שוב מאוחר יותר.',
                'IsBusinessError' => false,
                'TechnicalError' => $e->getMessage()
            ];
        }
        
        // Extensive logging of full response
        $this->custom_log('FULL API RESPONSE: ' . json_encode($response, JSON_UNESCAPED_UNICODE));
        
        // Detailed error analysis
        if (isset($response['Result']) && $response['Result'] === 'Error') {
            $this->custom_log('API returned an error response', 'error');
            
            if (isset($response['ErrorMessage'])) {
                $this->custom_log('Error message: ' . $response['ErrorMessage'], 'error');
                
                // Check for specific error messages to help diagnose the problem
                if (strpos($response['ErrorMessage'], 'בקשה לא תקינה') !== false) {
                    $this->custom_log('INVALID REQUEST ERROR DETECTED - This usually means a field format is incorrect', 'error');
                    
                    // Log each field to help diagnose format issues
                    foreach ($formattedPosts as $key => $value) {
                        $this->custom_log("Field '$key' => " . (is_array($value) ? json_encode($value) : $value));
                    }
                }
                
                if (strpos($response['ErrorMessage'], 'משתמש קיים') !== false) {
                    $this->custom_log('EXISTING USER ERROR DETECTED', 'warning');
                }
            }
            
            if (isset($response['IsBusinessError'])) {
                $this->custom_log('Is business error: ' . ($response['IsBusinessError'] ? 'Yes' : 'No'));
            }
            
            // Check for validation errors
            if (isset($response['validationErrors']) && !empty($response['validationErrors'])) {
                $this->custom_log('Validation errors found: ' . json_encode($response['validationErrors'], JSON_UNESCAPED_UNICODE), 'error');
            }
        }
        
        // Extract password and mobile from the response
        $retrievedPassword = isset($response['volunteer']['Password']) ? $response['volunteer']['Password'] : null;
        $retrievedMobile = isset($response['volunteer']['Cellular']) ? $response['volunteer']['Cellular'] : null;

        // Log password and mobile retrieval
        $this->custom_log('Retrieved Password: ' . ($retrievedPassword ? '[FOUND]' : 'NULL'));
        $this->custom_log('Retrieved Mobile: ' . ($retrievedMobile ? $retrievedMobile : 'NULL'));

        // Parse error message for additional data if user exists
        if (isset($response['ErrorMessage']) && strpos($response['ErrorMessage'], 'משתמש קיים') !== false) {
            $this->custom_log('User exists error detected, attempting to extract nested data');
            preg_match('/\{.*\}/', $response['ErrorMessage'], $matches);
            if (!empty($matches)) {
                $this->custom_log('Regex match found in error message: ' . $matches[0]);
                $nestedData = json_decode($matches[0], true);
                if ($nestedData) {
                    $this->custom_log('Found nested user data: ' . json_encode($nestedData, JSON_UNESCAPED_UNICODE));
                    // Override retrieved data with nested data if available
                    $retrievedPassword = isset($nestedData['Password']) ? $nestedData['Password'] : $retrievedPassword;
                    
                    // For existing users, use the mobile number they provided in registration
                    if (!$retrievedMobile) {
                        $retrievedMobile = $formattedPosts['mobile'];
                    }
                    
                    $this->custom_log('Updated Password: ' . ($retrievedPassword ? '[FOUND]' : 'NULL'));
                    $this->custom_log('Updated Mobile: ' . ($retrievedMobile ? $retrievedMobile : 'NULL'));
                } else {
                    $this->custom_log('Failed to parse nested JSON data', 'error');
                }
            } else {
                $this->custom_log('No regex match found in error message', 'warning');
            }
        }

        // Treat as success if we have a password (for existing users we'll use their provided mobile)
        if ($retrievedPassword) {
            $this->custom_log('Password found, treating as success');
            
            // Use the mobile number from registration if we don't have one from the response
            $mobileToUse = $retrievedMobile ? $retrievedMobile : $formattedPosts['mobile'];
            $this->custom_log('Mobile number to use for SMS: ' . $mobileToUse);
            
            // Send SMS with password
            $message = "סיסמתך לאתר האגודה להתנדבות: {$retrievedPassword}. סיסמא זו תשמש אותך גם באפליקציה בהמשך התהליך";
            $this->custom_log('Sending SMS with message: ' . $message);
            $sms = $this->sherutLeumi->sendSMS($mobileToUse, $message);
            $output['smsSent'] = $sms ? "SMS נשלח בהצלחה" : "שגיאה בשליחת SMS";
            $this->custom_log('SMS send result: ' . $output['smsSent']);

            // Mark as success and set existing user flag
            $output['error'] = false;
            $output['isExistingUser'] = true;
            
            // Override the response Result to Success for existing users
            if (isset($response['Result']) && $response['Result'] === 'Error') {
                $this->custom_log('Overriding error result to success for existing user');
                $response['Result'] = 'Success';
                $response['ErrorMessage'] = null; // Clear error message since we're treating this as success
            }
        } else {
            // Only treat as error if we don't have password
            $output['error'] = isset($response['ErrorMessage']);
            $this->custom_log('Error status set to: ' . ($output['error'] ? 'true' : 'false'));
            
            if ($output['error']) {
                $output['errorMessage'] = $this->sherutLeumi->getErrorMessage($response['ErrorMessage']);
                $this->custom_log('Error message: ' . $output['errorMessage'], 'error');
            }
        }
        
        // Store the retrieved values in output
        $output['retrievedPassword'] = $retrievedPassword;
        $output['retrievedMobile'] = $retrievedMobile;
        $output['responseClient'] = $response;
        
        $this->data = $output;
        $header = $output['error'] ? 400 : 200;
        
        $this->custom_log('Response HTTP status: ' . $header);
        $this->custom_log('Response body: ' . json_encode($this->data, JSON_UNESCAPED_UNICODE));
        $this->custom_log('===== END: newRegister function complete =====');
        
        return $this->output
            ->set_status_header($header)
            ->set_content_type('application/json')
            ->set_output(json_encode($this->data));
    }

    public function hebrewYears() {
        
        $currentYear = date("Y");
        
        if( (int)date("m") < 9) {
            $currentYear = $currentYear -1;
        }
        
        $years = array(
            
            'current' => $this->sherutLeumi->showHebrewYear(array('10','30',$currentYear)),
            'nextYear' => $this->sherutLeumi->showHebrewYear(array('10','30',$currentYear+1)),
            'year2' => $this->sherutLeumi->showHebrewYear(array('10','30',$currentYear+2)),
            
        );
        
        
        //echo "<pre>";
        //print_r($years);
        
        $output['years'] = $years;
        
        $this->data = $output;
        
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }






    public function login($jPData = FALSE) {
        
        //https://sherut-leumi.wdev.co.il/api/register/newRegister?sebas=1
        //https://sherut-leumi.wdev.co.il/api/assets/files/david/registerSend.json
            
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('register');
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if( !$checkPageAuth && !$this->input->get('sebas')  ) { return $this->output ->set_status_header(403); }
        
        
        $getPosts = array(
            'IDNO', //324504620
            'Password' //44686786
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);

        $output['fields'] = $pagePosts;
        $output['responseClient'] = $this->sherutLeumi->ApiClient($url = 'v2/volunteer/login', $pagePosts);
        $output['error'] = $this->sherutLeumi->checkError($output['responseClient']);
        
        //$output['error'] = false;
        
        if(!$output['error']) {
            
            $Sayarot = $this->sherutLeumi->getSayarotUser(
                array(
                    'IDNO' => $pagePosts['IDNO'],
                    'SessionKey' => $output['responseClient']['SessionKey']
                )
            );
            
            
            $userData = array(
                'IDNO' => $pagePosts['IDNO'],
                'Category' => $output['responseClient']['Category'],
                'FirstName' => $output['responseClient']['FirstName'],
                'LastName' => $output['responseClient']['LastName'],
                'Sex' => $output['responseClient']['Sex'],
                'InService' => isset($output['responseClient']['InService']) ? $output['responseClient']['InService'] : false,
                'AttendanceReportOnline' => isset($output['responseClient']['AttendanceReportOnline']) ? $output['responseClient']['AttendanceReportOnline'] : false,
                'SessionKey' => $output['responseClient']['SessionKey'],
                'ImageUrl' => $output['responseClient']['ImageUrl'],
                'Sayarot' => $Sayarot
            );
            
            $output['ok'] = $userData;
            
        }
        
        
        $this->data = $output;
        
        if($output['error']) {$header = '400'; };
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function LoginQueryUrl($jPData = FALSE) {
        
        //https://sherut-leumi.wdev.co.il/api/register/newRegister?sebas=1
        //https://sherut-leumi.wdev.co.il/api/assets/files/david/registerSend.json
            
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('register');
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if( !$checkPageAuth && !$this->input->get('sebas')  ) { return $this->output ->set_status_header(403); }
        
        
        $getPosts = array(
            'idno', //324504620
            'sessionkey' //44686786
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        $is_error_SessionKey = $this->sherutLeumi->checkSessionKey($pagePosts['idno'], $pagePosts['sessionkey']);
        
        if(!$is_error_SessionKey) {
            
            $userDataApi = $this->sherutLeumi->ApiClient($url = 'v2/volunteer/info/get', array('SessionKey' =>$pagePosts['sessionkey'], 'idno' =>$pagePosts['idno']) );
            
            //$output['userDataApi'] = $userDataApi;
            
            if( $userDataApi && isset($userDataApi['Result']) && $userDataApi['Result'] == 'Success' ) {
                
                $Sayarot = $this->sherutLeumi->getSayarotUser(
                    array(
                        'IDNO' => $pagePosts['idno'],
                        'SessionKey' => $pagePosts['sessionkey']
                    )
                );
                
                $userData = array(
                    'IDNO' => $pagePosts['idno'],
                    'SessionKey' => $pagePosts['sessionkey'],

                    'Category' => $userDataApi['category'],
                    'FirstName' => $userDataApi['firstname'],
                    'LastName' =>   $userDataApi['lastname'],
                    'Sex' => $userDataApi['sex'],
                    'ImageUrl' => $userDataApi['image'],

                    'Sayarot' => $Sayarot
                );

                $output['ok'] = $userData;
            
            } else {
                $output['error'] = 'errorSesionKey';
            }
            
            
        } else {
            $output['error'] = 'errorSesionKey';
        }
        
        
        $this->data = $output;
        
        //if($output['error']) {$header = '400'; };
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function checkActiveSession ($jPData = FALSE) {
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('all');
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if( !$checkPageAuth && !$this->input->get('sebas')  ) { return $this->output ->set_status_header(403); }
        
        
        $getPagePosts = array(
            'idno', //324504620
            'SessionKey' //44686786
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPagePosts);
        
        $output['error'] = $this->sherutLeumi->checkSessionKey($pagePosts['idno'], $pagePosts['SessionKey']);
        
        $this->data = $output;
        //if($output['error']) {$header = '500'; };
        
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    
    public function forgotPassword($jPData = FALSE) {
        
        //https://sherut-leumi.wdev.co.il/api/register/newRegister?sebas=1
        //https://sherut-leumi.wdev.co.il/api/assets/files/david/registerSend.json
            
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('register');
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if( !$checkPageAuth && !$this->input->get('sebas')  ) { return $this->output ->set_status_header(403); }
        
        
        $getPosts = array(
            'IDNO', //324504620
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);

        $output['fields'] = $pagePosts;
        $output['responseClient'] = $this->sherutLeumi->ApiClient($url = 'v2/volunteer/remindPass', $pagePosts);
        $output['error'] = $this->sherutLeumi->checkError($output['responseClient'], 'forgotPassword');
        
        //$output['error'] = false;
        
        if(!$output['error']) {
            
            $output['ok'] = $output['responseClient']['SMSResult'];
            
        }
        
        
        $this->data = $output;
        
        if($output['error']) {$header = '400'; };
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function sendMyMail($jPData = FALSE) {
        
        //https://sherut-leumi.wdev.co.il/api/register/sendMyMail
        
        //die('hello');
            
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('all');
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if( !$checkPageAuth && !$this->input->get('sebas')  ) { return $this->output ->set_status_header(403); }
        
        
        $getPosts = array(
            'idNumber', //324504620
            'fullName', //324504620
            'email', //324504620
            'subject', //324504620
            'phoneNumber', //324504620
            'content', //324504620

            
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        $this->sendEmail($pagePosts);
        //$mail = $this->sendEmail($to,$titleMail);

        $output['test'] = 'test';
        $output['pagePosts'] = $pagePosts;
                
        //$output['error'] = false;
        
        $this->data = $output;
        
//        if($output['error']) {$header = '400'; };
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    
    private function sendEmail($params) {
        $this->load->library("email");
        $config = Array(
            'protocol' => 'smtp',
            'smtp_host' => 'in-v3.mailjet.com',
            'smtp_port' => 587,
            'smtp_user' => 'fe313beca5ed02d82af856c9a94e0e6b',
            'smtp_pass' => '53edd86632c33b0ef4ceaae3eeb1dfe5',
            'mailtype'  => 'html', 
            'charset'   => 'UTF-8'
        );
        $this->email->initialize($config);
        $this->email->from('<EMAIL>', 'Nurses');
        $this->email->to('<EMAIL>');
          $paramsData = array(
                'idNumber' => isset($params['idNumber']) ? $params['idNumber'] : "טרם נקבע",
                'fullName' => isset($params['fullName']) ? $params['fullName'] : "טרם נקבע",
                'email' => isset($params['email']) ? $params['email'] : "טרם נקבע",
                'subject' => isset($params['subject']) ? $params['subject'] : "טרם נקבע",
                'phoneNumber' => isset($params['phoneNumber']) ? $params['phoneNumber'] : "טרם נקבע",
                'content' => isset($params['content']) ? $params['content'] : "טרם נקבע",
            );
        $text = '<p>שם מלא: ' . $paramsData['fullName'] . '</p> <br/> '
                . '<p>תעודת זהות: ' . $paramsData['idNumber'] . ' </p><br/>'
                . '<p>כותבת איימיל ' . $paramsData['email'] .'</p><br/>'
                . '<p>מספר טלפון: '.$paramsData['phoneNumber'].'</p></br>'
                . '<p>נושא הפנייה: ' . $paramsData['subject'] .  '</p><br/>'
                . '<p>תוכן הפניה: ' . $paramsData['content'] . '</p><br/>';
         $this->email->from('<EMAIL>', 'Wave Project ');
        //  <EMAIL>
        $this->email->to('<EMAIL>');
        $this->email->subject('הודעה חדשה מאפליקציית מתנדב');
        $this->email->message($text);
        $this->email->send();
        $response['success'] = $this->email->send();
        return $response;
    }
    
    private function bodyMail($titleMail,$text) {
        //BUILD EMAIL BODY

        //table lead style
        $td_style_1="background-color: rgb(235, 235, 235); width: 100%; text-align: center;text-direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 14px;";  

        $td_style_2="background-color: rgb(213, 213, 213); width: 100%; text-align: center;text-direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 16px;";


        //{$params['name']}

        $body = "
        <div style='text-align: center;text-direction:rtl; font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;'>
        <br/><br/>
        <a href='https://waveproject.co.il/'>
        <img src='http://leads.wdev.co.il/commonAssetsWave/mails/mail_upNathan.png' border='0'><br/><br/>
        </a>

        <table align='center' dir='LTR'; border='0' cellpadding='5' cellspacing='2' style='width: 100%;max-width: 347px;'>
        <tbody>";

        $body .= "
                <tr>
                    <td style='{$td_style_1}'>
                        <br/>
                        <p style='color:black;font-size: 20px;color:#47266a;'>משמרות במערכת</p>
                        <p style='color:black;font-size: 14px;' >{$titleMail}</p>
                        <br/>
                    </td>
                </tr>";
                        
        $body .= "
                <tr>
                    <td style='{$td_style_2}'>
                        <br/>
                        {$text}
                        <br/><br/>
                    </td>
                </tr>";
                    


        $body .= "
            </tbody></table><br/><br/>

            </div>";
                    
        return $body;
    }
    
}