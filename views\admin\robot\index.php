<div class="panel panel-default">
    <!-- Default panel contents -->
    <div class="panel-heading">
        שאלונים (ROBOT)
        <small>
            <?php showInfo($total, $limit, $page); ?>
        </small>
        <div style="clear: both;"></div>
    </div>
    <!-- Table -->
    <table class="table table-hover">
        <thead>
            <tr>
                <th><?php showOrder('created_at', 'תאריך פתיחה'); ?></th>
                <th><?php showOrder('name', 'שם'); ?></th>
                <th><?php showOrder('date', 'מועד'); ?></th>
                <th><?php showOrder('debts', 'פרטים'); ?></th>
                <th><?php showOrder('status', 'סטטוס'); ?></th>
                <th>מחק שאלון</th>
            </tr>
        </thead>
        <tbody>
            <?php if(isset($objects) && !empty($objects)) { foreach($objects as $row) { ?>
                <tr id="row<?php echo $row->Id(); ?>" role="row">
                    <td>
                        <?php //echo $row->Arg('id').'<br/>'; ?>
                        <?php echo $row->Datetime('created_at'); ?>
                    </td>
                    
                    <td>
                        <strong>שם: </strong><?php echo $row->Arg('name'); ?><br/>
                        <strong>טלפון: </strong><?php echo $row->Arg('phone'); ?><br/>
                        <strong>מייל: </strong><?php echo $row->Arg('email'); ?>                        
                    </td>
                    <td>
                        <strong>סניף: </strong><?php echo $row->Arg('branch'); ?><br/>
                        <strong>יום רצוי: </strong><?php echo $row->Arg('date'); ?><br/>
                        <strong>שעה: </strong><?php echo $row->Arg('time'); ?>
                    </td>
                    <td>
                        <strong>חובות: </strong><?php echo $row->Arg('debts'); ?><br/>
                        <strong>תיקים: </strong><?php echo $row->Arg('opened'); ?><br/>
                        <strong>הליך: </strong><?php echo $row->Arg('process'); ?>
                    </td>
                    <td>
                        <?php 
                            if($row->Arg('status') == 'פנייה חדשה') {$class = 'info';}
                            else if($row->Arg('status') == 'לא רלוונטי') {$class = 'danger';}
                            else if($row->Arg('status') == 'לקוח חושב על זה') {$class = 'warning';}
                            else if($row->Arg('status') == 'רלוונטי') {$class = 'success';}
                            else $class = 'default';
                        ?>
                        <?php echo form_open(current_url() . '?change_status_id=' .  $row->Id(), array('onChange' => 'submit()') ); ?>
                            <select class="form-control btn-<?php echo $class;?>" name="status">
                                <option selected="" value="<?php echo $row->Arg('status'); ?>"><?php echo $row->Arg('status'); ?></option>
                                <option value="רלוונטי">רלוונטי</option>
                                <option value="לא רלוונטי">לא רלוונטי</option>
                                <option value="לקוח חושב על זה">לקוח חושב על זה</option>
                                <option value="פנייה חדשה">פנייה חדשה</option>
                            </select>
                        <?php echo form_close(); ?>
                    </td>
                    <td>
                        <a class="btn btn-xs btn-danger" onclick="return confirm('בטוח שרוצה למחוק את כל השאלון?')" href="<?php echo current_url().'?dellete='.$row->Arg('id'); ?>" target="_self">מחק שאלון</a>
                    </td>
                </tr>
            <?php }} ?>
        </tbody>
    </table>
    <div class="row">
        <div class="col-md-12 text-center">
            <?php showPages($total, $limit, $page); ?>
        </div>
    </div>

</div>




