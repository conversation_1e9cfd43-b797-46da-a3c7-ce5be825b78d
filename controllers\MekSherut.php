<?php defined('BASEPATH') OR exit('No direct script access allowed');

class MekSherut extends CI_Controller {
    
    private $data;
    private $folderView;
    private $pagePosts;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
        if($param === 'uploadMethod') {
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        elseif($this->input->get('sebas')==1) {
            $output = array();
            $output['ok'] = 'GETSebas_Loader';
        }
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('token'));
           if( $postCode['token'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
    }
        
    public function getMekSherutSearchParams($jPData = FALSE) {
        $this->_loaderWS();
        $output['funcName'] = $this->router->fetch_method();
        $pageAutoriced = array('mekSherut'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced, $jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas')) { 
            return $this->output->set_status_header(403); 
        }
    
        try {
            // Get all unique cities
            $this->db->distinct();
            $this->db->select('City_Value, City_Key, City_Zone');
            $this->db->from('mekSherutTemp');
            $this->db->where('City_Value IS NOT NULL');
            $this->db->order_by('City_Value', 'ASC');
            $citiesResult = $this->db->get();
            $allCities = array();
            foreach ($citiesResult->result_array() as $city) {
                if (!empty($city['City_Value'])) {
                    $allCities[] = array(
                        'label' => $city['City_Value'],
                        'value' => $city['City_Value'],
                        'zoneCode' => isset($city['City_Zone']) ? $city['City_Zone'] : ''
                    );
                }
            }
    
            // Get all unique zones
            $this->db->distinct();
            $this->db->select('City_Zone, City_ZoneName');
            $this->db->from('mekSherutTemp');
            $this->db->where('City_Zone IS NOT NULL');
            $this->db->order_by('City_ZoneName', 'ASC');
            $zonesResult = $this->db->get();
            $allZones = array();
            foreach ($zonesResult->result_array() as $zone) {
                if (!empty($zone['City_Zone']) || !empty($zone['City_ZoneName'])) {
                    $allZones[] = array(
                        'label' => $zone['City_ZoneName'],
                        'value' => $zone['City_Zone']
                    );
                }
            }
    
            // Get all unique years
            $this->db->distinct();
            $this->db->select('YEAR');
            $this->db->from('mekSherutTemp');
            $this->db->where('YEAR IS NOT NULL');
            $this->db->order_by('YEAR', 'DESC');
            $yearsResult = $this->db->get();
            $allYears = array();
            foreach ($yearsResult->result_array() as $year) {
                if (!empty($year['YEAR'])) {
                    $allYears[] = array(
                        'label' => $year['YEAR'],
                        'value' => $year['YEAR']
                    );
                }
            }
    
            // Get all unique categories (using Thum2 instead of Thum)
            $this->db->distinct();
            $this->db->select('Thum2_Key, Thum2_Value');
            $this->db->from('mekSherutTemp');
            $this->db->where('Thum2_Value IS NOT NULL');
            $this->db->order_by('Thum2_Value', 'ASC');
            $categoriesResult = $this->db->get();
            
            $allCategories = array();
            foreach ($categoriesResult->result_array() as $category) {
                if (!empty($category['Thum2_Value']) && !empty($category['Thum2_Key'])) {
                    $allCategories[] = array(
                        'label' => $category['Thum2_Value'],
                        'value' => $category['Thum2_Key']
                    );
                }
            }
            
            // Sort by label
            usort($allCategories, function($a, $b) {
                return strcmp($a['label'], $b['label']);
            });
    
            // Get all unique tracks (Maslol)
            $this->db->distinct();
            $this->db->select('Maslol');
            $this->db->from('mekSherutTemp');
            $this->db->where('Maslol IS NOT NULL');
            $maslolResult = $this->db->get();
            $allMaslulTemp = array();
            foreach ($maslolResult->result_array() as $maslol) {
                if (strpos($maslol['Maslol'], ',') !== false) {
                    $arrayMaslul = explode(",", $maslol['Maslol']);
                    foreach ($arrayMaslul as $singleMaslol) {
                        $allMaslulTemp[] = trim($singleMaslol);
                    }
                } else {
                    $allMaslulTemp[] = trim($maslol['Maslol']);
                }
            }
    
            $allMaslul = array();
            if (!empty($allMaslulTemp)) {
                $uniqueMaslul = array_unique($allMaslulTemp);
                foreach ($uniqueMaslul as $maslol) {
                    if (!empty($maslol)) {
                        $allMaslul[] = array(
                            'label' => $maslol,
                            'value' => $maslol
                        );
                    }
                }
                usort($allMaslul, function($a, $b) {
                    return strcmp($a['label'], $b['label']);
                });
            }
    
            $currentYear = date("Y") + 1;        
            if ((int)date("m") < 9) {
                $currentYear = $currentYear - 1;
            }
    
            $output['cities'] = $allCities;
            $output['zones'] = $allZones;
            $output['years'] = $allYears;
            $output['categories'] = $allCategories;
            
            $output['tracks'] = $allMaslul;
            $output['currentYear'] = $this->sherutLeumi->showHebrewYear(array('10','30',$currentYear));
    
        } catch (Exception $e) {
            log_message('error', 'getMekSherutSearchParams error: ' . $e->getMessage());
            return $this->output
                    ->set_status_header(500)
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['error' => true, 'message' => 'An error occurred while fetching search parameters']));
        }
    
        $this->data = $output;
        return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    /**
     * Helper function to handle a single search term with exact and partial matches
     * 
     * @param string $term The search term to handle
     * @return void
     */
    private function handleSearchTerm($term) {
        $term = trim($term);
        if (empty($term)) return;

        $escapedTerm = $this->db->escape_like_str($term);
        
        // Add the conditions without affecting the FROM clause
        $this->db->group_start()
            // Exact matches first
            ->group_start()
                ->where('MOSADNA', $term)
                ->or_where('TAFKID', $term)
                ->or_where('City_Value', $term)
            ->group_end()
            // Then partial matches
            ->or_group_start()
                ->like('MOSADNA', $escapedTerm)
                ->or_like('MAS_NOTES', $escapedTerm)
                ->or_like('TAFKID', $escapedTerm)
                ->or_like('YEAR', $escapedTerm)
                ->or_like('City_Value', $escapedTerm)
                ->or_like('City_ZoneName', $escapedTerm)
                ->or_like('Thum_Value', $escapedTerm)
                ->or_like('Thum2_Value', $escapedTerm)
            ->group_end()
        ->group_end();
    }

    /**
     * Helper function to build and execute search query
     * 
     * @param string $type Type of query to build ('main' or 'count')
     * @return CI_DB_result|bool Query result object or FALSE on failure
     * @throws Exception When query execution fails
     */
    private function buildSearchQuery($type = 'main') {
        try {
            // Start building the SQL query
            if ($type === 'count') {
                $sql = "SELECT COUNT(*) as count ";
            } else {
                $sql = "SELECT id, MOSADNA, MAS_NOTES, TAFKID, YEAR, Maslol,
                        City_Value, City_ZoneName, Rak_Value, Rak_Phone,
                        Thum_Key, Thum_Value, Thum2_Key, Thum2_Value,
                        PICTURE1, PICTURE2, PICTURE3, PICTURE4, grain,
                        TEKENB, TEKENH, TEKENP, ContractNo,
                        City_Key, Rak_Key, is2Maslol,
                        created_at, updated_at, status, sort ";
            }
            
            $sql .= "FROM mekSherutTemp WHERE `Maslol` != '' ";
            
            $binds = array(); // Array to store bind parameters
            
            // Apply filters
            if (!empty($this->pagePosts['year'])) {
                $sql .= "AND YEAR = ? ";
                $binds[] = $this->pagePosts['year'];
            }

            if (!empty($this->pagePosts['City_Value'])) {
                $sql .= "AND City_Value = ? ";
                $binds[] = $this->pagePosts['City_Value'];
            }

            // Enhanced zone filtering with mapping and logging
            if (!empty($this->pagePosts['City_Zone'])) {
                log_message('debug', '[Filter] Processing zone filter with value: ' . json_encode($this->pagePosts['City_Zone']));
                
                // First, let's check what values exist in the database
                $checkZonesQuery = "SELECT DISTINCT City_Zone, COUNT(*) as count FROM mekSherutTemp WHERE City_Zone IS NOT NULL GROUP BY City_Zone";
                $zonesResult = $this->db->query($checkZonesQuery);
                if ($zonesResult) {
                    $availableZones = [];
                    foreach ($zonesResult->result_array() as $row) {
                        $availableZones[$row['City_Zone']] = $row['count'];
                    }
                    log_message('debug', '[Filter] Available zones in DB: ' . json_encode($availableZones, JSON_UNESCAPED_UNICODE));
                }
                
                // Define zone mapping with both string and numeric keys for flexibility
                $zoneMapping = [
                    'צפון' => '0',
                    'מרכז' => '21',
                    'ירושלים' => '27',
                    'דרום' => '1',
                    // Add numeric keys for direct matches
                    '0' => '0',
                    '21' => '21',
                    '27' => '27',
                    '1' => '1'
                ];
                
                if (is_array($this->pagePosts['City_Zone'])) {
                    $validZones = array_filter($this->pagePosts['City_Zone']); // Remove empty values
                    if (!empty($validZones)) {
                        // Map each zone value
                        $mappedZones = array_map(function($zone) use ($zoneMapping) {
                            $trimmedZone = trim($zone);
                            // Convert to string to ensure consistent comparison
                            $trimmedZone = (string)$trimmedZone;
                            $mappedValue = array_key_exists($trimmedZone, $zoneMapping) ? $zoneMapping[$trimmedZone] : $trimmedZone;
                            log_message('debug', sprintf('[Filter] Mapping zone: %s (type: %s) -> %s (type: %s)', 
                                $trimmedZone, gettype($trimmedZone), $mappedValue, gettype($mappedValue)));
                            return $mappedValue;
                        }, $validZones);
                        
                        $zonePlaceholders = implode(',', array_fill(0, count($mappedZones), '?'));
                        $sql .= "AND CAST(City_Zone AS CHAR) IN ($zonePlaceholders) ";
                        $binds = array_merge($binds, $mappedZones);
                        log_message('debug', '[Filter] Applied IN clause for multiple zones: ' . implode(', ', $mappedZones));
                    }
                } else {
                    $zoneValue = trim($this->pagePosts['City_Zone']);
                    if (!empty($zoneValue)) {
                        // Convert to string for consistent comparison
                        $zoneValue = (string)$zoneValue;
                        // Map single zone value
                        $mappedZone = array_key_exists($zoneValue, $zoneMapping) ? $zoneMapping[$zoneValue] : $zoneValue;
                        log_message('debug', sprintf('[Filter] Mapping single zone: %s (type: %s) -> %s (type: %s)', 
                            $zoneValue, gettype($zoneValue), $mappedZone, gettype($mappedZone)));
                        
                        $sql .= "AND CAST(City_Zone AS CHAR) = ? ";
                        $binds[] = $mappedZone;
                        log_message('debug', '[Filter] Applied exact match for zone: ' . $mappedZone);
                        
                        // Verify if this zone exists in DB
                        if (isset($availableZones)) {
                            if (isset($availableZones[$mappedZone])) {
                                log_message('debug', sprintf('[Filter] Zone %s exists in DB with %d records', 
                                    $mappedZone, $availableZones[$mappedZone]));
                            } else {
                                log_message('warning', sprintf('[Filter] Zone %s not found in DB available zones', $mappedZone));
                            }
                        }
                    }
                }
            } else {
                log_message('debug', '[Filter] No zone filter applied - City_Zone parameter is empty');
            }

            // Free text search with synonyms
            if (!empty($this->pagePosts['freeSearch'])) {
                try {
                    $mainTerm = trim($this->pagePosts['freeSearch']);
                    $synonyms = $this->sherutLeumi->getSynonyms($mainTerm);
                    
                    $sql .= "AND ( ";
                    
                    // Main term search conditions
                    $sql .= "(
                        MOSADNA = ? OR TAFKID = ? OR City_Value = ?
                        OR MOSADNA LIKE ? OR MAS_NOTES LIKE ? OR TAFKID LIKE ? 
                        OR YEAR LIKE ? OR City_Value LIKE ? OR City_ZoneName LIKE ?
                        OR Thum_Value LIKE ? OR Thum2_Value LIKE ?
                    )";
                    
                    // Add bind parameters for exact matches
                    $binds[] = $mainTerm;
                    $binds[] = $mainTerm;
                    $binds[] = $mainTerm;
                    
                    // Add bind parameters for LIKE matches
                    $likeStr = '%' . $this->db->escape_like_str($mainTerm) . '%';
                    for ($i = 0; $i < 8; $i++) {
                        $binds[] = $likeStr;
                    }
                    
                    // Add synonym conditions if any exist
                    if (!empty($synonyms) && is_array($synonyms)) {
                        foreach ($synonyms as $synonym) {
                            if (!empty($synonym)) {
                                $sql .= " OR (
                                    MOSADNA = ? OR TAFKID = ? OR City_Value = ?
                                    OR MOSADNA LIKE ? OR MAS_NOTES LIKE ? OR TAFKID LIKE ? 
                                    OR YEAR LIKE ? OR City_Value LIKE ? OR City_ZoneName LIKE ?
                                    OR Thum_Value LIKE ? OR Thum2_Value LIKE ?
                                )";
                                
                                // Add bind parameters for exact matches
                                $binds[] = $synonym;
                                $binds[] = $synonym;
                                $binds[] = $synonym;
                                
                                // Add bind parameters for LIKE matches
                                $likeSynonym = '%' . $this->db->escape_like_str($synonym) . '%';
                                for ($i = 0; $i < 8; $i++) {
                                    $binds[] = $likeSynonym;
                                }
                            }
                        }
                    }
                    
                    $sql .= ") ";
                    
                } catch (Exception $e) {
                    log_message('error', 'Free text search error: ' . $e->getMessage());
                }
            }

            // Apply Thum filter
            if (!empty($this->pagePosts['Thum_Key']) && is_array($this->pagePosts['Thum_Key'])) {
                $sql .= "AND ( ";
                $thumConditions = array();
                
                foreach ($this->pagePosts['Thum_Key'] as $thumKey) {
                    if (!empty($thumKey)) {
                        $thumConditions[] = "(Thum_Key = ? OR Thum2_Key = ?)";
                        $binds[] = trim($thumKey);
                        $binds[] = trim($thumKey);
                    }
                }
                
                if (!empty($thumConditions)) {
                    $sql .= implode(" OR ", $thumConditions);
                }
                
                $sql .= ") ";
            }

            // Apply Maslol filter
            if (!empty($this->pagePosts['Maslol'])) {
                $maslol = trim($this->pagePosts['Maslol']);
                if (!empty($maslol)) {
                    $sql .= "AND Maslol LIKE ? ";
                    $binds[] = '%' . $this->db->escape_like_str($maslol) . '%';
                }
            }

            // Add ORDER BY and LIMIT for main query
            if ($type !== 'count') {
                $sql .= "ORDER BY City_Value ASC, MOSADNA ASC ";
                
                $limit = isset($this->pagePosts['limit']) ? (int)$this->pagePosts['limit'] : 10;
                $limit = max(1, $limit);
                $page = isset($this->pagePosts['page']) ? max(0, (int)$this->pagePosts['page']) : 0;
                $offset = $page * $limit;
                
                $sql .= "LIMIT ? OFFSET ? ";
                $binds[] = $limit;
                $binds[] = $offset;
            }

            // Log the query for debugging
            log_message('debug', sprintf(
                '[Generated Query] Type: %s, SQL: %s, Binds: %s',
                $type,
                $sql,
                json_encode($binds, JSON_UNESCAPED_UNICODE)
            ));

            // Execute query with bindings
            $query = $this->db->query($sql, $binds);
            if ($query === FALSE) {
                $error = $this->db->error();
                throw new Exception('Query execution failed: ' . $error['message']);
            }

            return $query;
            
        } catch (Exception $e) {
            log_message('error', 'buildSearchQuery error: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get filtered and paginated list of service locations (Mek Sherut)
     * 
     * @param bool $jPData Whether data is coming from JSON POST (not used currently)
     * @return CI_Output JSON response containing filtered service locations
     *
     * The function performs the following:
     * 1. Authenticates the request
     * 2. Checks if data needs to be populated
     * 3. Gets search parameters from JSON POST
     * 4. Builds and executes query with filters for:
     *    - Free text search (with synonyms)
     *    - Year
     *    - City
     *    - City Zone  
     *    - Department (Thum)
     *    - Track (Maslol)
     *    - Favorites
     *    - Specific ID
     * 5. Applies pagination
     * 6. Returns formatted results
     */
    /**
     * Get filtered and paginated list of service locations (Mek Sherut)
     * 
     * @param bool $jPData Whether data is coming from JSON POST (not used currently)
     * @return CI_Output JSON response containing filtered service locations
     */
    public function getMekSherutNEW($jPData = FALSE) {
        $this->_loaderWS();
        $output = array('funcName' => $this->router->fetch_method());

        try {
            // Authentication check with detailed logging
            $jsonPosts = $this->msiteWs->getPostFromJson(array('auth', 'token', 'credential'));
            log_message('debug', '[Auth] Checking authentication for mekSherut');
            
            if (!$this->msiteWs->checkPageAuth(array('mekSherut'), $jsonPosts['auth']) && !$this->input->get('sebas')) {
                log_message('warning', '[Auth] Unauthorized access attempt to mekSherut');
                return $this->output->set_status_header(403);
            }

            // Force populate if needed
            try {
                $this->sherutLeumi->checkPopulateMekSherutNEW();
            } catch (Exception $e) {
                log_message('error', '[Population] Failed to populate data: ' . $e->getMessage());
                // Continue execution as this is not a critical error
            }

            // Get and validate search parameters
            $requiredParams = array(
                'freeSearch', 'year', 'City_Value', 'City_Zone', 'Thum_Key', 'Maslol',
                'favoritesPlaces', 'showfavoritesPlaces', 'sayarIdQuery', 'limit', 'page', 'orderBy'
            );
            
            $this->pagePosts = $this->msiteWs->getPostFromJson($requiredParams);
            
            // Log search request
            log_message('debug', sprintf(
                '[Search Request] Parameters: %s',
                json_encode(array_intersect_key($this->pagePosts, array_flip($requiredParams)), JSON_UNESCAPED_UNICODE)
            ));

            // Execute count query with error handling
            try {
                $count_result = $this->buildSearchQuery('count');
                if ($count_result === FALSE) {
                    throw new Exception('Count query failed: ' . $this->db->error()['message']);
                }
                $total_results = $count_result->row()->count;
                $output['allmekSherut'] = $total_results;
                
                log_message('debug', sprintf('[Results] Total count: %d', $total_results));
            } catch (Exception $e) {
                log_message('error', '[Count Query] ' . $e->getMessage());
                throw $e;
            }

            // Execute main query with error handling
            try {
                $result = $this->buildSearchQuery('main');
                if ($result === FALSE) {
                    throw new Exception('Main query failed: ' . $this->db->error()['message']);
                }
            } catch (Exception $e) {
                log_message('error', '[Main Query] ' . $e->getMessage());
                throw $e;
            }

            // Format results with error handling for each item
            $items = array();
            foreach ($result->result_array() as $value) {
                try {
                    $items[] = array(
                        'id' => $value['id'],
                        'MOSADNA' => $value['MOSADNA'],
                        'MAS_NOTES' => $value['MAS_NOTES'],
                        'TAFKID' => $value['TAFKID'],
                        'YEAR' => $value['YEAR'],
                        'Maslol' => $value['Maslol'],
                        'City_Value' => $value['City_Value'],
                        'City_ZoneName' => $value['City_ZoneName'],
                        'Rak_Value' => $value['Rak_Value'],
                        'Rak_Phone' => $value['Rak_Phone'],
                        'sayarotRakazCount' => $this->sherutLeumi->checkSayarotRakazCount($value['Rak_Key']),
                        'Thum' => array(
                            'key' => $value['Thum_Key'],
                            'value' => $value['Thum_Value'],
                            'key2' => $value['Thum2_Key'],
                            'value2' => $value['Thum2_Value']
                        ),
                        'pictures' => $this->sherutLeumi->makePictures(
                            $value['MOSADNA'],
                            $value['PICTURE1'],
                            $value['PICTURE2'],
                            $value['PICTURE3'],
                            $value['PICTURE4']
                        ),
                        'grain' => $value['grain'],
                        'TEKENB' => $value['TEKENB'],
                        'TEKENH' => $value['TEKENH'],
                        'TEKENP' => $value['TEKENP'],
                        'ContractNo' => $value['ContractNo'],
                        'City_Key' => $value['City_Key'],
                        'Rak_Key' => $value['Rak_Key'],
                        'is2Maslol' => $value['is2Maslol'],
                        'created_at' => $value['created_at'],
                        'updated_at' => $value['updated_at'],
                        'status' => $value['status'],
                        'sort' => $value['sort']
                    );
                } catch (Exception $e) {
                    // Log error but continue processing other items
                    $id = isset($value['id']) ? $value['id'] : 'unknown';
                    log_message('error', sprintf(
                        '[Result Formatting] Error processing item %s: %s',
                        $id,
                        $e->getMessage()
                    ));
                }
            }

            $output['items'] = $items;
            
            // Calculate pagination with validation
            $limit = isset($this->pagePosts['limit']) ? (int)$this->pagePosts['limit'] : 10;
            $limit = max(1, $limit); // Ensure minimum of 1
            $output['totalPages'] = ceil($total_results / $limit);
            
            log_message('debug', sprintf(
                '[Response] Items: %d, Total Pages: %d',
                count($items),
                $output['totalPages']
            ));

            return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode($output));

        } catch (Exception $e) {
            $error_message = 'getMekSherutNEW error: ' . $e->getMessage();
            log_message('error', $error_message);
            
            // Include stack trace in development
            if (ENVIRONMENT === 'development') {
                log_message('debug', 'Stack trace: ' . $e->getTraceAsString());
            }
            
            $debug = null;
            if (ENVIRONMENT === 'development') {
                $debug = array(
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                );
            }
            
            return $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'error' => true,
                    'message' => 'An error occurred while fetching data',
                    'debug' => $debug
                )));
        }
    }


    /**
     * Import synonyms from a CSV file into the database
     * This is a maintenance function for populating the synonyms table
     * 
     * @param bool $param Not used, kept for backward compatibility
     * @return CI_Output JSON response with import results
     */
    public function insertSynonyms($param = false) {
        // Check authentication
        $this->_loaderWS('uploadMethod');
        
        try {
            // Validate environment
            if (ENVIRONMENT !== 'development') {
                throw new Exception('This function can only be run in development environment');
            }

            $fileData = 'csv/table.csv';
            $filePath = base_url() . IMG . $fileData;
            
            // Validate file exists and is readable
            if (!is_readable($filePath)) {
                throw new Exception('CSV file not found or not readable: ' . $fileData);
            }

            log_message('debug', '[Synonyms Import] Starting import from: ' . $fileData);
            
            $importCount = 0;
            $errorCount = 0;
            $errors = array();
            
            if (($handle = fopen($filePath, "r")) !== FALSE) {
                // Start transaction
                $this->db->trans_start();
                
                while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    try {
                        // Validate row has enough columns
                        if (count($row) < 5) {
                            throw new Exception('Invalid row format: ' . implode(',', $row));
                        }
                        
                        $data = array(
                            'lang' => NULL,
                            'sort' => '',
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'word1' => trim($row[1]),
                            'word2' => trim($row[2]),
                            'word3' => trim($row[3]),
                            'word4' => trim($row[4])
                        );
                        
                        // Validate at least one word is not empty
                        if (empty($data['word1']) && empty($data['word2']) && 
                            empty($data['word3']) && empty($data['word4'])) {
                            throw new Exception('Row contains no valid words');
                        }
                        
                        // Insert record
                        if (!$this->db->insert('synonyms', $data)) {
                            throw new Exception($this->db->error()['message']);
                        }
                        
                        $importCount++;
                        
                    } catch (Exception $e) {
                        $errorCount++;
                        $errors[] = $e->getMessage();
                        log_message('error', '[Synonyms Import] Row error: ' . $e->getMessage());
                        continue; // Skip to next row on error
                    }
                }
                
                fclose($handle);
                
                // Commit transaction if no errors, otherwise rollback
                if ($errorCount === 0) {
                    $this->db->trans_commit();
                    log_message('debug', '[Synonyms Import] Successfully imported ' . $importCount . ' records');
                } else {
                    $this->db->trans_rollback();
                    throw new Exception('Import failed with ' . $errorCount . ' errors');
                }
                
            } else {
                throw new Exception('Failed to open CSV file');
            }
            
            return $this->output
                ->set_status_header(200)
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'success' => true,
                    'message' => 'Import completed successfully',
                    'imported' => $importCount,
                    'errors' => $errorCount,
                    'errorDetails' => ENVIRONMENT === 'development' ? $errors : null
                )));
                
        } catch (Exception $e) {
            log_message('error', '[Synonyms Import] ' . $e->getMessage());
            
            return $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'error' => true,
                    'message' => 'Import failed: ' . $e->getMessage(),
                    'debug' => ENVIRONMENT === 'development' ? array(
                        'trace' => $e->getTraceAsString()
                    ) : null
                )));
        }
    }
        
    
    
       
}
