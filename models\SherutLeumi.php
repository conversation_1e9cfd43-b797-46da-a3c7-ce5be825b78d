<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SherutLeumi extends CI_Model {
    
    public function __construct() {
        parent::__construct();
        
    }
    
 
    public function apiClient($url,$data = FALSE) {
        
        
        if(!$data) {
            
            return 'noData';
            
        } else {
            
            $this->load->model('msiteWs');
            $checkDev = $this->msiteWs->getPostFromJson(array('isRotemDev'));
            
            $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
            
            if( isset($checkDev['isRotemDev']) && $checkDev['isRotemDev'] ) {
                
                $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
                
            }

            $url= $baseUrl.$url;
            $data_string = json_encode($data);

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));

            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);  //300
            curl_setopt($curl, CURLOPT_TIMEOUT, 20); //156 timeout in seconds
            
            $result = curl_exec($curl);
            curl_close($curl);

            $response = json_decode($result, true);
            //$array1 = json_decode(json_encode($response), True);
            
            
            return $response;
            
        }
        
    }
    
       
    
    public function apiClientGetSayarot($url = false, $data = false) {
        
        
        if(empty($url)) {
            
            return 'noData';
            
        } else {
            
            $this->load->model('msiteWs');
            $checkDev = $this->msiteWs->getPostFromJson(array('isRotemDev'));
            
            
            $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
            
            if( isset($checkDev['isRotemDev']) && $checkDev['isRotemDev'] ) {
                
                //die('isRotemDev');
                
                $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
                
            }

            $url= 'https://vu-apiws-autosc.azurewebsites.net/api/v2/Data/getAllSayarot';
            
            $data_string = '{}';

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
            
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20); 
            curl_setopt($curl, CURLOPT_TIMEOUT, 20); //timeout in seconds

            $result = curl_exec($curl);
            curl_close($curl);

            $response = json_decode($result, true);  
            
            //$array1 = json_decode(json_encode($response), True);
            
            if( isset($response['Sayarot']) && !empty($response['Sayarot']) ) {
            
//                $results = $response['Sayarot'];
//                
//                foreach ($results as $key => $row)
//                {
//                    $value[$key] = (int)$row['RegStart'];  //sort by RegStart
//                }
//
//                array_multisort($value, SORT_ASC, $results);
//            
//                return $results;

                return $response['Sayarot'];
            }
            
            else return array();
        }
        
    }
    
    
    public function apiClientGetMekSherut($url = false, $data = false) {
        
        
        if(empty($url)) {
            
            return 'noData';
            
        } else {
            

            $this->load->model('msiteWs');
            $checkDev = $this->msiteWs->getPostFromJson(array('isRotemDev'));
            
            
            $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/v2/';
            

            $url= 'https://vu-apiws-autosc.azurewebsites.net/api/v2/data/MekomotSherut';
            
            $data_string = '{}';
            
            
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));

            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);  //300
            curl_setopt($curl, CURLOPT_TIMEOUT, 20); //156 timeout in seconds
            
            $result = curl_exec($curl);
            curl_close($curl);

            $response = json_decode($result, true);
            //$array1 = json_decode(json_encode($response), True);
            
            if( isset($response['MekomotSherut']) && !empty($response['MekomotSherut']) ) {
            
                
                return $response['MekomotSherut'];
            }
            
            else return array();
        }
        
    }
    
   public function apiClientGetMekSherutNEW() {
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://vu-apiws-autosc.azurewebsites.net/api/v2/Data/mekomotsherut',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    $responseData = json_decode($response, true);

    if (isset($responseData['items']) && !empty($responseData['items'])) {
        return $responseData;
    }

    return array();
}

    
    public function checkPopulate($param=false) {
        $this->db->select('id,created_at');
        $this->db->from('sayarotTEMP');
        $this->db->order_by('id', 'DESC');
        return false;
        $result= $this->db->get();
        
        $sayeret = $result->row_array();
        
        if(!empty($sayeret)) {
            $start_date = new DateTime($sayeret['created_at']);
            $since_start = $start_date->diff(new DateTime());
            
            // Calculate difference in minutes
            $minutesDifference = ($since_start->d * 1440) + ($since_start->h * 60) + $since_start->i;
        } else {
            $minutesDifference = 9999999;  //Always populate
        }
        
        if($minutesDifference > 360) {  // Check every 6 hours
            $populate = $this->populateSayarot();
            return $populate;
        }
        
        return false;
    }
    
    
    public function populateSayarot() {
    
    $allSayarot = $this->apiClientGetSayarot($url = 'v2/data/sayarot');

    $insertBatch = array();
    
    if(!empty($allSayarot)) {
        
        $this->db->empty_table('sayarotTEMP');
        
        foreach ($allSayarot as $sayeret) {
            
            // No need to check HIDEFROMLIST since it's not in the API response
            
            $phone = !empty($sayeret['Rak']['Mobile']) ? $sayeret['Rak']['Mobile'] : (!empty($sayeret['Rak']['Phone']) ? $sayeret['Rak']['Phone'] : '');
            $placesLeft = (int)$sayeret['LimMtn'] - (int)$sayeret['Mtnstate']; 
            $placesLeft = ($placesLeft > 0 && $placesLeft < 902) ? $placesLeft : 0;
            if((int)$sayeret['LimMtn'] == 903) { $placesLeft = 25; }
            $randPic = rand(1,16); // Pictures name from 1 to 16

            $insertBatch[] = array(
                'id' => $sayeret['ID'],
                'Name' => $sayeret['Name'],
                'RegStart' => $sayeret['RegStart'],
                'Mtnstate' => $sayeret['Mtnstate'],
                'SyrDay' => $sayeret['SyrDay'],
                'ArriveAt' => $sayeret['ArriveAt'],
                'ArriveTo' => $sayeret['ArriveTo'],
                'Notes' => $sayeret['Notes'],
                'Info' => $sayeret['Info'],
                'ArriveNotes' => $sayeret['ArriveNotes'],
                'LimMtn' => $sayeret['LimMtn'],
                'Mosadot' => json_encode($sayeret['Mosadot']),
                'City_Key' => $sayeret['City']['Key'],
                'City_Value' => $sayeret['City']['Value'],
                'Rak_Key' => $sayeret['Rak']['Key'],
                'Rak_Value' => $sayeret['Rak']['Value'],
                'Rak_Phone' => $phone,
                'HomeOnly' => $sayeret['HomeOnly'],
                'placesLeft' => $placesLeft,
                'randPic' => $randPic
            );
        }
        
        if(!empty($insertBatch)) {
            $insert = $this->db->insert_batch('sayarotTEMP', $insertBatch);
            return $insert;
        }
    }
    
    return false;
}
    
    
    public function checkPopulateMekSherut($param=false) {
        
        $this->db->select('id,created_at');
        $this->db->from('mekSherutTemp');
        $this->db->order_by('id', 'DESC');
        $result= $this->db->get();
        
        $mSherut = $result->row_array();
        
       if(!empty($mSherut)) {
                 //    if(true) {

            $now = date("Y-m-d H:i:s");
            $date = new DateTime($mSherut['created_at']);

            //https://stackoverflow.com/questions/365191/how-to-get-time-difference-in-minutes-in-php

            //$start_date = new DateTime('2007-09-01 04:10:58');
            $start_date = new DateTime($mSherut['created_at']);
            $since_start = $start_date->diff(new DateTime());

            $minutesDifference = ($since_start->d * 1440) + ($since_start->h * 60) + $since_start->i;
            
            //die('sebas: '.$minutesDifference);
        
        } else {
            $minutesDifference = 9999999;  //Always populate
        }
        
        
        
        
        if($minutesDifference > (30)) { // POPULE AFTER 6 HOUR ONLY
            
            $populate = $this->populateMSherutNEW();
            return $populate;
            //echo "<br/><br/>POPULATE";
            
        }
        
        
        //echo "minutesDifference: ".$minutesDifference;
        return false;
        
    }
    
     public function checkPopulateMekSherutNEW($param=false) {
        
        $this->db->select('id,created_at');
        $this->db->from('mekSherutTemp');
        $this->db->order_by('id', 'DESC');
        $result= $this->db->get();
        
        $mSherut = $result->row_array();
        
       if(!empty($mSherut)) {
                 //    if(true) {

            $now = date("Y-m-d H:i:s");
            $date = new DateTime($mSherut['created_at']);

            //https://stackoverflow.com/questions/365191/how-to-get-time-difference-in-minutes-in-php

            //$start_date = new DateTime('2007-09-01 04:10:58');
            $start_date = new DateTime($mSherut['created_at']);
            $since_start = $start_date->diff(new DateTime());

            $minutesDifference = ($since_start->d * 1440) + ($since_start->h * 60) + $since_start->i;
            
            //die('sebas: '.$minutesDifference);
        
        } else {
            $minutesDifference = 9999999;  //Always populate
        }
        
        
        
        
        if($minutesDifference > (30)) { // POPULE AFTER 6 HOUR ONLY
            
            $populate = $this->populateMSherutNEW();
            return $populate;
            //echo "<br/><br/>POPULATE";
            
        }
        
        
        //echo "minutesDifference: ".$minutesDifference;
        return false;
        
    }
    
    public function populateMSherut() {
        $allMekSherut = $this->apiClientGetMekSherut($url = 'v2/data/MekomotSherut');
        $insertBatch = array();
        
        if(!empty($allMekSherut)) {
            $this->db->empty_table('mekSherutTemp');
            
            foreach ($allMekSherut as $mSherut) {
                $values = array(
                    'id' => $mSherut['ID'],
                    'MOSADNA' => $mSherut['MOSADNA'],
                    'TEKENB' => (int)$mSherut['AvailB'],
                    'TEKENH' => (int)$mSherut['AvailH'],
                    'TEKENP' => (int)$mSherut['AvailP'],
                    'MAS_NOTES' => $mSherut['MAS_NOTES'],
                    'TAFKID' => $mSherut['Tafkid'],
                    'PICTURE1' => $mSherut['PICTURE1'],
                    'PICTURE2' => $mSherut['PICTURE2'],
                    'PICTURE3' => $mSherut['PICTURE3'],
                    'PICTURE4' => $mSherut['PICTURE4'],
                    'YEAR' => $mSherut['YEAR'],
                    'Maslol' => null,
                    'is2Maslol' => null,
                    'Thum_Key' => $mSherut['Thum']['TchomMos_key'],
                    'Thum_Value' => $mSherut['Thum']['TchomMos_value'],
                    'Thum2_Key' => !empty($mSherut['Thum']['Tchom2_key']) ? $mSherut['Thum']['Tchom2_key'] : null,
                    'Thum2_Value' => !empty($mSherut['Thum']['Tchom2_value']) ? $mSherut['Thum']['Tchom2_value'] : null,
                    'Grain' => $mSherut['Grain'] ? '1' : '',
                    'City_Key' => isset($mSherut['City']) && isset($mSherut['City']['Key']) ? $mSherut['City']['Key'] : '',
                    'City_Value' => isset($mSherut['City']['Value']) ? $mSherut['City']['Value'] : '',
                    'City_Zone' => $mSherut['City']['Zone'],
                    'City_ZoneName' => $mSherut['City']['ZoneName'],
                    'Rak_Key' => $mSherut['Rak']['Key'],
                    'Rak_Value' => $mSherut['Rak']['Value'],
                    'Rak_Phone' => $mSherut['Rak']['Mobile'],
                    'ContractNo' => $mSherut['ContractNo']
                );

                if($mSherut['Maslol'] == 'ממלכתי דתי') {
                    $values['Maslol'] = 'דתי';
                } else if($mSherut['Maslol'] == 'של"מ') {
                    $values['Maslol'] = 'חילוני';
                } else if($mSherut['Maslol'] == 'של"מ,ממלכתי דתי' || $mSherut['Maslol'] == 'ממלכתי דתי, של\'\'מ') {
                    $values['Maslol'] = 'דתי';
                    $values['is2Maslol'] = '1';
                }

                $insertBatch[] = $values;
            }
            
            return $this->db->insert_batch('mekSherutTemp', $insertBatch);
        }
        
        return false;
    }
    
    public function populateMSherutNEW() {
        // מושכים את כל הרשומות מה-API
        $allMekSherut = $this->apiClientGetMekSherutNEW();

        // מערך שישמור את כל הרשומות שנרצה להכניס לטבלה
        $insertBatch = array();

        // נבדוק שהמבנה תקין ויש לנו "items"
        if (!empty($allMekSherut) && isset($allMekSherut['items']) && is_array($allMekSherut['items'])) {
            $allMekSherut = $allMekSherut['items'];
            
            try {
                // קודם מרוקנים את הטבלה הזמנית
                $this->db->empty_table('mekSherutTemp');
            } catch (Exception $e) {
                log_message('error', 'Failed to empty mekSherutTemp table: ' . $e->getMessage());
                return false;
            }

            // נבצע מעבר על כל רשומה שהגיעה
            foreach ($allMekSherut as $mSherut) {
                // וידוא שדות חובה
                if (!isset($mSherut['ID']) || !isset($mSherut['MOSADNA'])) {
                    log_message('error', 'Missing required fields in mSherut record: ' . json_encode($mSherut));
                    continue;
                }

                // מיפוי מסלול לפי דרישות חדשות:
                $maslolMapped = null;
                $is2Maslol = '0';

                if (isset($mSherut['Maslol'])) {
                    $maslolValue = trim($mSherut['Maslol']);
                    
                    // בדיקה אם יש שילוב של ממלכתי דתי ושל"מ בכל סדר וצורה
                    if (
                        strpos($maslolValue, 'ממלכתי דתי') !== false && 
                        (
                            strpos($maslolValue, 'של"מ') !== false || 
                            strpos($maslolValue, 'של״מ') !== false || 
                            strpos($maslolValue, "של''מ") !== false ||
                            strpos($maslolValue, 'שלמ') !== false
                        )
                    ) {
                        $maslolMapped = 'דתי';
                        $is2Maslol = '1';
                    }
                    // בדיקת מסלול דתי בלבד
                    elseif ($maslolValue === 'ממלכתי דתי' || $maslolValue === 'דתי') {
                        $maslolMapped = 'דתי';
                    }
                    // בדיקת מסלול של"מ בלבד - כולל וריאציות שונות
                    elseif (
                        $maslolValue === 'של"מ' || 
                        $maslolValue === 'של״מ' || 
                        $maslolValue === "של''מ" || 
                        $maslolValue === 'שלמ'
                    ) {
                        $maslolMapped = 'חילוני';
                    }

                    // לוג של המיפוי
                    log_message('debug', sprintf(
                        'Maslol mapping: Original="%s", Mapped="%s", is2Maslol="%s"',
                        $maslolValue,
                        $maslolMapped,
                        $is2Maslol
                    ));
                }

                if ($maslolMapped === null) {
                    log_message('debug', 'Skipping record with unmapped Maslol: ' . (isset($maslolValue) ? $maslolValue : 'undefined'));
                    continue;
                }

                $data = [
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s"),
                    'status' => 1,
                    'id' => substr($mSherut['ID'], 0, 36),
                    'MOSADNA' => isset($mSherut['MOSADNA']) ? substr($mSherut['MOSADNA'], 0, 512) : '',
                    'TEKENB' => isset($mSherut['AvailB']) ? substr((string)$mSherut['AvailB'], 0, 254) : '',
                    'TEKENH' => isset($mSherut['AvailH']) ? substr((string)$mSherut['AvailH'], 0, 254) : '',
                    'TEKENP' => isset($mSherut['AvailP']) ? substr((string)$mSherut['AvailP'], 0, 254) : '',
                    'MAS_NOTES' => isset($mSherut['MAS_NOTES']) ? $mSherut['MAS_NOTES'] : null,
                    'TAFKID' => isset($mSherut['Tafkid']) ? $mSherut['Tafkid'] : null,
                    'PICTURE1' => isset($mSherut['PICTURE1']) ? substr($mSherut['PICTURE1'], 0, 512) : '',
                    'PICTURE2' => isset($mSherut['PICTURE2']) ? substr($mSherut['PICTURE2'], 0, 512) : '',
                    'PICTURE3' => isset($mSherut['PICTURE3']) ? substr($mSherut['PICTURE3'], 0, 512) : '',
                    'PICTURE4' => isset($mSherut['PICTURE4']) ? substr($mSherut['PICTURE4'], 0, 512) : '',
                    'YEAR' => isset($mSherut['YEAR']) ? substr($mSherut['YEAR'], 0, 512) : '',
                    'Maslol' => $maslolMapped,
                    'is2Maslol' => $is2Maslol,
                    'Thum_Key' => isset($mSherut['Thum']['TchomMos_key']) ? $mSherut['Thum']['TchomMos_key'] : null,
                    'Thum_Value' => isset($mSherut['Thum']['TchomMos_value']) ? substr($mSherut['Thum']['TchomMos_value'], 0, 512) : '',
                    'Thum2_Key' => isset($mSherut['Thum']['Tchom2_key']) ? substr($mSherut['Thum']['Tchom2_key'], 0, 254) : '',
                    'Thum2_Value' => isset($mSherut['Thum']['Tchom2_value']) ? substr($mSherut['Thum']['Tchom2_value'], 0, 254) : '',
                    'Grain' => (isset($mSherut['Grain']) && $mSherut['Grain']) ? 1 : 0,
                    'City_Key' => isset($mSherut['City']['Key']) ? substr($mSherut['City']['Key'], 0, 512) : '',
                    'City_Value' => isset($mSherut['City']['Value']) ? $mSherut['City']['Value'] : '',
                    'City_Zone' => isset($mSherut['City']['Zone']) ? $mSherut['City']['Zone'] : '',
                    'City_ZoneName' => isset($mSherut['City']['ZoneName']) ? substr($mSherut['City']['ZoneName'], 0, 512) : '',
                    'Rak_Key' => isset($mSherut['Rak']['Key']) ? substr($mSherut['Rak']['Key'], 0, 512) : '',
                    'Rak_Value' => isset($mSherut['Rak']['Value']) ? $mSherut['Rak']['Value'] : '',
                    'Rak_Phone' => isset($mSherut['Rak']['Mobile']) ? substr($mSherut['Rak']['Mobile'], 0, 512) : '',
                    'ContractNo' => isset($mSherut['ContractNo']) ? substr($mSherut['ContractNo'], 0, 254) : '',
                    'sort' => 0
                ];

                $insertBatch[] = $data;
            }

            if (!empty($insertBatch)) {
                try {
                    return $this->db->insert_batch('mekSherutTemp', $insertBatch);
                } catch (Exception $e) {
                    log_message('error', 'Failed to insert batch into mekSherutTemp: ' . $e->getMessage());
                    return false;
                }
            }
        }

        return false;
    }

    public function apiClientGet($url = false, $data = false) {
        
//        $data = array(
//            'IDNO' => $jsonPosts['idno'],
//            'FirstName' => $jsonPosts['firstname'],
//            'LastName' => $jsonPosts['lastname'],
//        );
        
        
        
        if(empty($url)) {
            
            return 'noData';
            
        } else {
            

            $this->load->model('msiteWs');
            $checkDev = $this->msiteWs->getPostFromJson(array('isRotemDev'));
            
            $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
                           

            if( isset($checkDev['isRotemDev']) && $checkDev['isRotemDev'] ) {
                $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
                //die($baseUrl);
            }

            $url= $baseUrl.$url;
            
            //$url='https://vu-apiws-autosc.azurewebsites.net/api/'.$url;
            //echo $url;
            
            
            
            $data_string = '{}';
            
            
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));

            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);  //300
            curl_setopt($curl, CURLOPT_TIMEOUT, 20); //156 timeout in seconds
            
            $result = curl_exec($curl);
            curl_close($curl);

            $response = json_decode($result, true);
            //$array1 = json_decode(json_encode($response), True);
            
            
            
            
            if( isset($response['Items']) && !empty($response['Items']) ) {
            
                foreach ($response['Items'] as $value) {

                    if($url === 'https://vu-apiws-autosc.azurewebsites.net/api/v2/data/schools') {
                        
                        if(!empty($value['Place'])) {
                            $name = $value['Place'].' - '.$value['Value'];
                        } else {
                            $name = $value['Value'];
                        }
                        
                        $results[] = array('id' => $value['Key'],'name' => $name );
                        
                    } else {
                        $results[] = array('id' => $value['Key'],'name' => $value['Value'] );
                    }
                    
                    
                }

                } else {
                    return array();
                }

                $value = array();
                
                foreach ($results as $key => $row)
                {
                    $value[$key] = $row['name'];
                }

                array_multisort($value, SORT_ASC, $results);
            
                return $results;
            
        }
        
    }
    
    public function apiClientGetRegister($url = false, $data = false) {
    if(empty($url)) {
        return 'noData';
    } else {
        $this->load->model('msiteWs');
        $checkDev = $this->msiteWs->getPostFromJson(array('isRotemDev'));
        
        $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
                       
        if(isset($checkDev['isRotemDev']) && $checkDev['isRotemDev']) {
            $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
        }
        $url = $baseUrl . $url;
        
        // Convert data to JSON string
        $data_string = json_encode($data);
        
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data_string)
        ));
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);
        
        $result = curl_exec($curl);
        $err = curl_error($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        // If there's a cURL error, return it
        if ($err) {
            return ['error' => $err];
        }
        
        // Decode the response
        $response = json_decode($result, true);
        
        // Return the raw response for debugging
        return $response;
    }
}
    public function addRegisterLog($pagePosts, $responseClient) {
        
        
        $data = array(
                'status' => 1,
                'created_at' => date("Y-m-d H:i:s"),
                'FirstName' => $pagePosts['FirstName'],
                'LastName' => $pagePosts['LastName'],
                'IDNO' => $pagePosts['IDNO'],
                'Mobile' => $pagePosts['Mobile'],
                'BirthDate' => $pagePosts['BirthDate'],
                'CityCode' => $pagePosts['CityCode'],
                'Email' => $pagePosts['Email'],
                'PrvSchool' => $pagePosts['PrvSchool'],
                'sex' => $pagePosts['sex'],
                'Category' => $pagePosts['Category'],
                'YearYad' => $pagePosts['YearYad'],
                'passwordMd5'  => md5($pagePosts['Password']),
                'responseClient' => json_encode($responseClient)
        );

        $insert = $this->db->insert('registersLog', $data); 
        $insert_id = $this->db->insert_id();
        
        return $insert_id;
        
    }
    
    
    public function checkError($responseClient, $action = false) {
        
        if( 
            ( isset($responseClient['Result']) && $responseClient['Result'] == 'Error') ||
            ( $action == 'forgotPassword' &&  $responseClient['Result'] == "NoPhoneNumber" ) ||
            ( isset($responseClient['ErrorMessage']) && !empty($responseClient['ErrorMessage']) )  ||
            ( isset($responseClient['Message']) && $responseClient['Message'] == "An error has occurred.")
        ) {
            
            if(isset($responseClient['ErrorMessage'])) {
                
                if($action=='forgotPassword') {
                    if(isset($responseClient['ErrorMessage']) && !empty($responseClient['ErrorMessage'])) {
                        return $responseClient['ErrorMessage'] ;
                    } else {
                        return $this->getErrorMessage($responseClient['Result']);
                    }
                    
                } else {
                    return $this->getErrorMessage($responseClient['ErrorMessage']);
                }
                
                
                
            } else {
                return 'משהו השתבש...';
            }
            
        }
        
        return false;
        
    }
    
    public function getErrorMessage($ErrorMessage) {
        
        switch($ErrorMessage) {
            
            case "can't find user or password": 
                return "שם משתמש או סיסמה לא נכונים";
                
            case "מספר תעודת זהות או סיסמה אינם קיימים": 
                return "מספר תעודת זהות או סיסמה אינם קיימים";
                
            case "password is to short or to long": 
                return "שם משתמש או סיסמה לא נכונים";
                
            case "Account temporary frozen": 
                return "חשבונך לא פעיל/הוקפאה";
            
            case "משתמש קיים - סיסמה שגויה":
                return "קיים משתמש עם אותה ת.ז במערכת";
                
            case "NoPhoneNumber": 
                return "תעודת הזהות לא נמצאת במערכת";
                
            default:
                return "משהו השתבש...";
                
        }
        
    }
    
    private function setUniqueArray($array) {
        try {
            // If not an array or empty, return as is
            if (!is_array($array) || empty($array)) {
                return $array;
            }

            // Handle multi-dimensional arrays
            if (is_array(reset($array))) {
                $uniqueArray = [];
                foreach ($array as $item) {
                    if (!is_array($item)) {
                        continue;
                    }
                    // Ensure all values in the item array are scalar before json_encode
                    $processedItem = array_map(function($value) {
                        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
                    }, $item);
                    
                    // Create a unique key based on a consistent representation of the item
                    $key = md5(json_encode($processedItem, JSON_UNESCAPED_UNICODE));
                    if (!isset($uniqueArray[$key])) {
                        $uniqueArray[$key] = $item;
                    }
                }
                return array_values($uniqueArray);
            }

            // For simple arrays, just use array_unique
            return array_values(array_unique($array));
            
        } catch (Exception $e) {
            log_message('error', 'Error in setUniqueArray: ' . $e->getMessage());
            return $array;
        }
    }

        public function getSayarotUser($params=false) {
        
        $data = array(
            'SessionKey' => $params['SessionKey'],
            'idno' => $params['IDNO']
        );
        
        $sayarot = $this->apiPost('v2/volunteer/syr/my',$data);
        
        // $sayarot = array();
        
        if(isset($sayarot['Sayarot']) && !empty($sayarot['Sayarot'])) {
            
            return $this->setUniqueArray($sayarot['Sayarot']);
            
            
        } else {
            
            return array();
        }
        
        
    }
    
    
    public function removeSayeret($params = false ) {
        
        $data = array(
            'SessionKey' => $params['SessionKey'],
            'idno' => $params['IDNO'],
            'SyrID' => $params['sayarId'],
        );
        
        $sayarot = $this->apiPost('v2/volunteer/syr/remove',$data);
        
        if(isset($sayarot['ErrorMessage']) && !empty($sayarot['ErrorMessage'])) {
            
            $error = 'משהו השתבש, נא לנסות מאוחר יותר.';
            
        } else {
            $error = '';
        }
        
        $removeSayeretReturn = array(
            'newArray' => isset($sayarot['Sayarot']) ? $this->setUniqueArray($sayarot['Sayarot']) : array(),
            'errorMsg' => $error
        );
        
        $data['response'] = json_encode($sayarot);
        $data['actionType'] = 'removeSayeret';
        $insertLogSayarot = $this->insertLogSayarot($data);
        
        return $removeSayeretReturn;
        
    }
    
    
    public function addSayeret($params = false ) {
        
        
        $data = array(
            'SessionKey' => $params['SessionKey'],
            'idno' => $params['IDNO'],
            'SyrID' => $params['sayarId'],
        );
        
        $sayarot = $this->apiPost('v2/volunteer/syr/add',$data);
        
        if(isset($sayarot['ErrorMessage']) && !empty($sayarot['ErrorMessage'])) {
            
            //if($sayarot['ErrorMessage'] == 'מגבלת מתנדבים') {
                
                $error = 'ספר המקומות להרשמה אזלו';
                
            //}
            
            //$error = 'משהו השתבש, נא לנסות מאוחר יותר.';
            
        } else {
            $error = '';
        }
        
        $addSayeretReturn = array(
            'newArray' => isset($sayarot['Sayarot']) ? $this->setUniqueArray($sayarot['Sayarot']) : array(),
            'errorMsg' => $error
        );
        
        
        $data['response'] = json_encode($sayarot);
        $data['actionType'] = 'addSayeret';
        $insertLogSayarot = $this->insertLogSayarot($data); 
        
        
        return $addSayeretReturn;
        
    }
    
    
    
    public function apiPost($url = false, $data = false) {
        
        if(empty($url)) {
            
            return 'noData';
            
        } else {
            

            $this->load->model('msiteWs');
            $checkDev = $this->msiteWs->getPostFromJson(array('isRotemDev'));
            
            $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
            
            if( isset($checkDev['isRotemDev']) && $checkDev['isRotemDev'] ) {
                
                $baseUrl = 'https://vu-apiws-autosc.azurewebsites.net/api/';
                
            }

            $url= $baseUrl.$url;
            
            //$url='https://vu-apiws-autosc.azurewebsites.net/api/'.$url;
            //echo $url;
            
            if($data) {
                
                $data_string = json_encode($data);
                
            } else {
                $data_string = '{}';
            }   
            
            
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));

            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);  //300
            curl_setopt($curl, CURLOPT_TIMEOUT, 20); //156 timeout in seconds
            
            $result = curl_exec($curl);
            curl_close($curl);

            $response = json_decode($result, true);
            //$array1 = json_decode(json_encode($response), True);
            
            if( isset($checkDev['isRotemDev']) && $checkDev['isRotemDev'] && false ) { //&& false
                
                if( $url == 'https://vu-apiws-autosc.azurewebsites.net/api/v2/volunteer/info/set' ) {

                    echo "<pre>";
                    echo "URL: ".$url.'<br/>';

                    echo "REQUEST: ".'<br/>';
                    print_r($data);

                    echo "RESPONSE: ".'<br/>';
                    print_r($response);
                    die();
                }
                
            }
           
            return $response;
            
        }
        
    }
    
    
    public function checkSessionKey($idno, $sessionKey) {
        
        //return false;
        
        $userCredentials = $this->apiPost(
                'v2/volunteer/syr/my',
                array(
                    'idno' => $idno,
                    'SessionKey' => $sessionKey
                ));

        if(isset($userCredentials['ErrorMessage']) && !empty($userCredentials['ErrorMessage']) ) {

            return 'logOff';

        } else {
            
            return false;
            
        }
        
    }
    
    
    public function insertLogSayarot($params) {
        
        
        $data = array(
                'status' => 1,
                'created_at' => date("Y-m-d H:i:s"),
                'idno' => $params['idno'],
                'SyrID' => $params['SyrID'],
                'SessionKey' => $params['SessionKey'],
                'actionType' => isset($params['actionType']) ? $params['actionType'] : '',
                'response' => isset($params['response']) ? $params['response'] : ''
        );

        $insert = $this->db->insert('sayarotLog', $data); 
        $insert_id = $this->db->insert_id();
        
        return $insert_id;
        
        
    }
    
    public function getNameFromIdApi($id,$apiName) {
        
        
        $api = $this->ApiClientGet($url = 'v2/data/'.$apiName);
        
        if(!empty($api) && isset($api[0]['id'])) {
            
            foreach ($api as $value) {
                
                if($value['id'] == $id) {
                    
                    return $value['name'];
                }
                
            }
        }
        
        return '';
        
        //echo "<pre>";
        //print_r($api);die();
        
        
    }
    
    
    
    
    public function editUserDataOnApi( $getPosts, $action = false) {
        
        // Debug only if there's an error
        if (empty($action) || empty($getPosts)) {
            echo "<pre>Error - Missing Data:\n";
            echo "Action: ";
            print_r($action);
            echo "\ngetPosts: ";
            print_r($getPosts);
            die("\nDebug Stop - Missing Required Data");
        }
        
        // Check for required fields based on action
        $requiredFields = array();
        if ($action === 'step1_PersonalInformation') {
            $requiredFields = array('idno', 'SessionKey', 'BirthDate', 'Email', 'FirstName', 'LastName', 'Mobile', 'sex');
        } else if ($action === 'step2_Address') {
            $requiredFields = array('idno', 'SessionKey', 'street', 'city');
        } else if ($action === 'step3_Emergency') {
            $requiredFields = array('idno', 'SessionKey', 'relativename', 'relativephone');
        } else if ($action === 'step4_Studies') {
            $requiredFields = array('idno', 'SessionKey', 'sh_year1');
        } else if ($action === 'step5_Sherut') {
            $requiredFields = array('idno', 'SessionKey', 'YearYad', 'Category');
        }
        
        // Check if any required fields are missing
        $missingFields = array();
        foreach ($requiredFields as $field) {
            if (!isset($getPosts[$field]) || empty($getPosts[$field])) {
                $missingFields[] = $field;
            }
        }
        
        if (!empty($missingFields)) {
            echo "<pre>Error - Missing Required Fields for $action:\n";
            print_r($missingFields);
            echo "\nCurrent getPosts data:\n";
            print_r($getPosts);
            die("\nDebug Stop - Missing Fields");
        }
        
        if($action === 'step1_PersonalInformation' && !empty($getPosts) ) {
            
            //$city = !empty((int)$getPosts['CityCode']) ? $this->getNameFromIdApi($getPosts['CityCode'],'cities') : $getPosts['CityCode'];
            
            $city = !empty((int)$getPosts['CityCode']) ? $this->getCityName($getPosts['CityCode']) : $getPosts['CityCode'];
            
            $sendData = array(
                
                'idno' => $getPosts['idno'],
                'SessionKey' => $getPosts['SessionKey'],

                'birthdate' => changeDateFormat($getPosts['BirthDate'], "Y-m-d","Ymd"),
                //'city' => $city,
                'email' => $getPosts['Email'],
                'firstname' => $getPosts['FirstName'],
                'lastname' => $getPosts['LastName'],
                'mobile' => $getPosts['Mobile'],
                'prvschool' => $getPosts['PrvSchool'],
                'sex' => $getPosts['sex'] == 'man' ? '1' : '2',
            );
            
        }
        
        else if($action === 'step2_Address' && !empty($getPosts) ) {
            
            //$city = !empty((int)$getPosts['city']) ? $this->getNameFromIdApi($getPosts['city'],'cities') : $getPosts['city'];
            $city = !empty((int)$getPosts['CityCode']) ? $this->getCityName($getPosts['city']) : $getPosts['city'];
            
            $sendData = array(
                
                'idno' => $getPosts['idno'],
                'SessionKey' => $getPosts['SessionKey'],
                
                'city' => $city,
                'street' => $getPosts['street'],
                'appartment' => $getPosts['appartment'],
                'zip' => $getPosts['zip'],
                'mwnumber' => $getPosts['mwnumber']
            );
            
        } else if($action === 'step3_Emergency' && !empty($getPosts) ) {
            
            $sendData = array(
                
                'idno' => $getPosts['idno'],
                'SessionKey' => $getPosts['SessionKey'],
                
                'relativename' => $getPosts['relativename'],
                'relativephone' => $getPosts['relativephone'],
                'relativerelation' => $getPosts['relativerelation']
            );
            
        } else if($action === 'step4_Studies' && !empty($getPosts) ) {
            
            $sendData = array(
                
                'idno' => $getPosts['idno'],
                'SessionKey' => $getPosts['SessionKey'],
                
                'sh_year1' => $getPosts['sh_year1'],
                'prvschool' => !empty((int)$getPosts['PrvSchool']) ? $getPosts['PrvSchool'] : null
            );
            
        } else if($action === 'step5_Sherut' && !empty($getPosts) ) {
            
            $sendData = array(
                
                'idno' => $getPosts['idno'],
                'SessionKey' => $getPosts['SessionKey'],
                
                'year_yad' => $getPosts['YearYad'],
                'category' => $getPosts['Category']
            );
            
        } else {
            
            return false;
            
        }
            
        
        $editPratim = $this->apiPost('v2/volunteer/info/set',$sendData);

        if(isset($editPratim['Result']) && ( $editPratim['Result'] == 'Sucsses' || $editPratim['Result'] == 'Success' ) ) {

            if($action === 'step1_PersonalInformation') {
            
                $newUserData = array (
                    'firstname' => $getPosts['FirstName'],
                    'lastname' => $getPosts['LastName'],
                    'sex' => $getPosts['sex'] == 'man' ? '1' : '2'
                );

                return $newUserData;
            } else {
                return $editPratim;
            }

        } else {
            return false;
        }
            
        
    }
    
    public function getCityName($CityCode) {
        
        $this->db->select('valueName');
        $this->db->from('citiesTemp');
        $this->db->where('keyName', $CityCode);

        $result= $this->db->get();
        $data = $result->row_array();
        
        return isset($data['valueName']) ? $data['valueName'] : $CityCode;
        
    }
    

    public function makePictures($alt,$pic1,$pic2,$pic3,$pic4) {
        
        $pictures = array();
        $picturesReturn = array();
        
        $pre = 'https://sherut-leumi.org/datiot/pic.aspx?p=:';
        $last = '&w=';
        
        if(!empty($pic1)) {
            
            $pictures[] = array(
                'url' => $pre.$pic1.$last,
                'alt' => $alt
            );
                    
        }
        
        if(!empty($pic2)) {
            
            $pictures[] = array(
                'url' => $pre.$pic2.$last,
                'alt' => $alt
            );
        }
        
        if(!empty($pic3)) {
            
            $pictures[] = array(
                'url' => $pre.$pic3.$last,
                'alt' => $alt
            );
        }
        
        if(!empty($pic4)) {
            
            $pictures[] = array(
                'url' => $pre.$pic4.$last,
                'alt' => $alt
            );
            
        }
        
        
        if(!empty($pictures)) {
            
            $key = 0;
            
            foreach ($pictures as $value) {
                
                $value['key'] = $key;
                $picturesReturn[] = $value;
                $key++;
            }
            
        }
        
        return $picturesReturn;
        
        
    }
    
    
    
    public function checkSayarotRakazCount($RakId) {
        
        
        $this->db->select('id');
        $this->db->from('sayarotTEMP');
        $this->db->where('Rak_Key', $RakId);
        return $this->db->count_all_results();
        
        
    }
    
    
    public function getSynonyms($freeSearch = false) {
        
        if($freeSearch) {
            
            $this->db->select('word1,word2,word3,word4');
            $this->db->from('synonyms');
            $this->db->where('word1', $freeSearch);
            $this->db->or_where('word2', $freeSearch);
            $this->db->or_where('word3', $freeSearch);
            $this->db->or_where('word4', $freeSearch);
            
            $result= $this->db->get();
            $allSynonyms = $result->row_array();
            
            $words = array();
            
            if(!empty($allSynonyms)) {
                
                if(!empty($allSynonyms['word1'])) {
                    $words[] = $allSynonyms['word1'];
                }
                if(!empty($allSynonyms['word2'])) {
                    $words[] = $allSynonyms['word2'];
                }
                if(!empty($allSynonyms['word3'])) {
                    $words[] = $allSynonyms['word3'];
                }
                if(!empty($allSynonyms['word4'])) {
                    $words[] = $allSynonyms['word4'];
                }
                    
            }
            
            //print_r($words);die(' -asd');
            
            return $words;
            
        }
        
        
    }
    
    public function apiClientGetSchoolsOLD($url = 'v2/data/schools', $data = false) {
        
        
        if(empty($url)) {
            
            return 'noData';
            
        } else {
            

            $url='https://vu-apiws-autosc.azurewebsites.net/api/'.$url;
            //echo $url;
            
            $data_string = '{}';
            
            
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));

            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);  //300
            curl_setopt($curl, CURLOPT_TIMEOUT, 20); //156 timeout in seconds
            
            $result = curl_exec($curl);
            curl_close($curl);

            $response = json_decode($result, true);
            //$array1 = json_decode(json_encode($response), True);
            
            
            
            
            if( isset($response['Items']) && !empty($response['Items']) ) {
            
                $results = array();
                
                foreach ($response['Items'] as $value) {

                    //print_r($value);die();
                    
                    if($value['Type'] == 'מ"ד') {
                        
                        if(!empty($value['Place'])) {
                            $name = $value['Place'].' - '.$value['Value'];
                        } else {
                            $name = $value['Value'];
                        }

                        $results[] = array('id' => $value['Key'],'name' => $name );
                        
                    }
                    
                }

                } else {
                    return array();
                }

                $value = array();
                
                foreach ($results as $key => $row)
                {
                    if(!empty($row)) {
                        $value[$key] = $row['name'];
                    }
                    
                }

                array_multisort($value, SORT_ASC, $results);
            
                return $results;
            
        }
        
    }
    
    
    public function apiClientGetSchools() {
        
        $this->db->select('keyName as id, valueName as name, placeName, typeName');
        $this->db->from('schoolsTemp');
        $this->db->order_by('valueName ASC');
        //$this->db->where('status', 1);
        $result= $this->db->get();
        $schools = $result->result_array();
        
        $results = array();
        
        foreach ($schools as $value) {
            
            if($value['typeName'] == 'ממ"ד') {
                        
                if(!empty($value['placeName'])) {
                    $name = $value['placeName'].' - '.$value['name'];
                } else {
                    $name = $value['name'];
                }

                $results[] = array('id' => $value['id'],'name' => $name );

            }
            
        }
        
        return $results;
        
    }
    
    
    public function sendSMS($phone = FALSE, $message = FALSE, $from = 'Aguda', $param = FALSE) {
        
        //$from = '0723932946'; // FOR SMS SYSTEM
        
        if(!empty($phone) && !empty($message)) {
            
            $checkPhone = $this->returnNoCellsPhone($phone); 
            
            if(empty($checkPhone)) {
                
                if($param == 'multi') {

                $phonenumbers = explode(',', $phone);
                if(!empty($phonenumbers)) {
                    foreach ($phonenumbers as $value) {
                        $phones[] =  preg_replace('/[^0-9]/', '', $value);
                    }
                }
                $phone = implode(',', $phones);

            } else {
                $phone = preg_replace('/[^0-9]/', '', $phone);
            }


            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL,"http://api.multisend.co.il/MultiSendAPI/sendsms");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS,
                        "user=waveproject&password=Aa12345&from=".$from."&recipient=".$phone."&message=".$message);

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $server_output = curl_exec ($ch);

            curl_close ($ch);

            return $server_output;
                
                
                
            }
        }
    }
    
    
    public function returnNoCellsPhone($phone) {
        
        if( strlen($phone) >= 9 && strlen($phone) <= 13 ) { // 13 -> 972
            
            $contentPhone =  preg_replace('/[^0-9]/', '', $phone);
                    
            $phoneStart3 = substr($contentPhone, 0,3);
            $phoneStart2 = substr($contentPhone, 0,2);
            
            if(
                $phoneStart2=='02' ||
                $phoneStart2=='03' ||
                $phoneStart2=='04' ||
                $phoneStart2=='08' ||
                ($phoneStart2=='09' && !$phoneStart3=='972' ) ||
                $phoneStart3=='072' ||
                $phoneStart3=='073' ||
                $phoneStart3=='074' ||
                $phoneStart3=='076' ||
                $phoneStart3=='077' ||
                $phoneStart3=='078') {

                    return $phone;
                    
            } else {
                return NULL;
            }
                    
            
        } return '1';
        
    }
    
    public function cleanPhone($phone) {
        
        
        $filter = str_replace("-", "", $phone);
        $filter = str_replace(" ", "", $filter);
        $filter = str_replace("+972", "0", $filter);
        
        return $filter;
        
        
    }
    
    public function getBitlyUrl($long_url) {
        
        $apiv4 = 'https://api-ssl.bitly.com/v4/bitlinks';
        $genericAccessToken = '45d736689fd9e0855dfdfe1c29c708498c5130ac';

        $data = array(
            'long_url' => $long_url
        );
        $payload = json_encode($data);

        $header = array(
            'Authorization: Bearer ' . $genericAccessToken,
            'Content-Type: application/json',
            'Content-Length: ' . strlen($payload)
        );

        $ch = curl_init($apiv4);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($ch);
        $resultToJson = json_decode($result);

        if (isset($resultToJson->link)) {
            return $resultToJson->link;
        }
        else {
            return false;
        }
    }
    
    
    public function showHebrewYear($year=false) {
        
        //$year = array('1','1','2022');
        
        $str = jdtojewish(gregoriantojd( date($year[0]), date($year[1]), date($year[2])), true, CAL_JEWISH_ADD_GERESHAYIM); // for today
        $str1 = iconv ('WINDOWS-1255', 'UTF-8', $str); // convert to utf-8
        $last_word_start = strrpos ( $str1 , " ") + 1;
        $last_word_end = strlen($str1) - 1;
        $last_word = substr($str1, $last_word_start, $last_word_end);

        
        return $last_word; 


        
    }
       
    
  public function saveCities() {
    $url = 'https://vu-apiws-autosc.azurewebsites.net/api/v2/Data/cities';
    $postData = json_encode([
        'sessionKey' => '<string>',
        'requestID' => '<string>',
        'idno' => '<string>',
        'deviceID' => '<string>'
    ]);

    $headers = [
        'Content-Type: application/json',
        'Cookie: ARRAffinity=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350; ARRAffinitySameSite=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        echo 'cURL error: ' . curl_error($ch);
        curl_close($ch);
        return;
    }

    curl_close($ch);

    // Log the raw response for debugging
    file_put_contents('api_response.log', $response);

    $api = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'JSON decode error: ' . json_last_error_msg();
        return;
    }

    if (isset($api['items']) && is_array($api['items'])) {
        foreach ($api['items'] as $value) {
            $data = [
                'created_at' => date("Y-m-d H:i:s"),
                'updated_at' => date("Y-m-d H:i:s"),
                'keyName' => isset($value['Key']) ? $value['Key'] : null,
                'valueName' => isset($value['Value']) ? $value['Value'] : null,
                'minheletNumber' => isset($value['MinheletNumber']) ? $value['MinheletNumber'] : null,
                'areaId' => isset($value['AreaId']) ? $value['AreaId'] : null,
                'areaName' => isset($value['AreaName']) ? $value['AreaName'] : null
            ];

            $insert = $this->db->insert('citiesTemp', $data);
            if (!$insert) {
                echo 'Database insert error';
                return;
            }
        }

        $insert_id = $this->db->insert_id();
        echo 'DONE ALL ' . $insert_id;
    } else {
        echo 'Unexpected API response structure';
        print_r($api); // Debug the response structure
    }
}

    
public function saveSchools() {
    $url = 'https://vu-apiws-autosc.azurewebsites.net/api/v2/Data/schools';
    $postData = json_encode([
        'sessionKey' => '<string>',
        'requestID' => '<string>',
        'idno' => '<string>',
        'deviceID' => '<string>'
    ]);

    $headers = [
        'Content-Type: application/json',
        'Cookie: ARRAffinity=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350; ARRAffinitySameSite=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        echo 'cURL error: ' . curl_error($ch);
        curl_close($ch);
        return;
    }

    curl_close($ch);

    // Log the raw response for debugging
    file_put_contents('api_response.log', $response);

    $api = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'JSON decode error: ' . json_last_error_msg();
        return;
    }

    if (isset($api['items']) && is_array($api['items'])) {
        foreach ($api['items'] as $value) {
            $data = [
                'created_at' => date("Y-m-d H:i:s"),
                'updated_at' => date("Y-m-d H:i:s"),
                'keyName' => isset($value['Key']) ? $value['Key'] : null,
                'valueName' => isset($value['Name']) ? $value['Name'] : null,
                'typeName' => isset($value['Type']) ? $value['Type'] : null,
                'placeName' => isset($value['Place']) ? $value['Place'] : null,
                'schoolCode' => isset($value['Code']) ? $value['Code'] : null
            ];

            $insert = $this->db->insert('schoolsTemp', $data);
            if (!$insert) {
                echo 'Database insert error';
                return;
            }
        }

        $insert_id = $this->db->insert_id();
        echo 'DONE ALL ' . $insert_id;
    } else {
        echo 'Unexpected API response structure';
        print_r($api); // Debug the response structure
    }
}

  public function saveCitiesDev() {
    $url = 'https://vu-apiws-d.azurewebsites.net/api/v2/Data/cities';
    $postData = json_encode([
        'sessionKey' => '<string>',
        'requestID' => '<string>',
        'idno' => '<string>',
        'deviceID' => '<string>'
    ]);

    $headers = [
        'Content-Type: application/json',
        'Cookie: ARRAffinity=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350; ARRAffinitySameSite=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        echo 'cURL error: ' . curl_error($ch);
        curl_close($ch);
        return;
    }

    curl_close($ch);

    // Log the raw response for debugging
    file_put_contents('api_response.log', $response);

    $api = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'JSON decode error: ' . json_last_error_msg();
        return;
    }

    if (isset($api['items']) && is_array($api['items'])) {
        foreach ($api['items'] as $value) {
            $data = [
                'created_at' => date("Y-m-d H:i:s"),
                'updated_at' => date("Y-m-d H:i:s"),
                'keyName' => isset($value['Key']) ? $value['Key'] : null,
                'valueName' => isset($value['Value']) ? $value['Value'] : null,
                'minheletNumber' => isset($value['MinheletNumber']) ? $value['MinheletNumber'] : null,
                'areaId' => isset($value['AreaId']) ? $value['AreaId'] : null,
                'areaName' => isset($value['AreaName']) ? $value['AreaName'] : null
            ];

            $insert = $this->db->insert('citiesTempDev', $data);
            if (!$insert) {
                echo 'Database insert error';
                return;
            }
        }

        $insert_id = $this->db->insert_id();
        echo 'DONE ALL ' . $insert_id;
    } else {
        echo 'Unexpected API response structure';
        print_r($api); // Debug the response structure
    }
}

    
public function saveSchoolsDev() {
    $url = 'https://vu-apiws-d.azurewebsites.net/api/v2/Data/schools';
    $postData = json_encode([
        'sessionKey' => '<string>',
        'requestID' => '<string>',
        'idno' => '<string>',
        'deviceID' => '<string>'
    ]);

    $headers = [
        'Content-Type: application/json',
        'Cookie: ARRAffinity=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350; ARRAffinitySameSite=5b679e38c88f70f236b8d6cc40240ccaf8421fb899c96c89d40fd81ad7ed8350'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        echo 'cURL error: ' . curl_error($ch);
        curl_close($ch);
        return;
    }

    curl_close($ch);

    // Log the raw response for debugging
    file_put_contents('api_response.log', $response);

    $api = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo 'JSON decode error: ' . json_last_error_msg();
        return;
    }

    if (isset($api['items']) && is_array($api['items'])) {
        foreach ($api['items'] as $value) {
            $data = [
                'created_at' => date("Y-m-d H:i:s"),
                'updated_at' => date("Y-m-d H:i:s"),
                'keyName' => isset($value['Key']) ? $value['Key'] : null,
                'valueName' => isset($value['Name']) ? $value['Name'] : null,
                'typeName' => isset($value['Type']) ? $value['Type'] : null,
                'placeName' => isset($value['Place']) ? $value['Place'] : null,
                'schoolCode' => isset($value['Code']) ? $value['Code'] : null
            ];

            $insert = $this->db->insert('schoolsTempDev', $data);
            if (!$insert) {
                echo 'Database insert error';
                return;
            }
        }

        $insert_id = $this->db->insert_id();
        echo 'DONE ALL ' . $insert_id;
    } else {
        echo 'Unexpected API response structure';
        print_r($api); // Debug the response structure
    }
}


    
}
