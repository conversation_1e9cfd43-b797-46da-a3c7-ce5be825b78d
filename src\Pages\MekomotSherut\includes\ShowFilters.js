import React, { Component } from 'react'
//import loader from "../../../img/preLoader.gif";
import { Button } from "@mui/material";

export default class ShowFilters extends Component {

    constructor(props) {
        super(props)
    
        let states = this.props.currentState;

        this.state = {
            
            freeSearch : states.freeSearch,
            Maslol : states.Maslol,
            Thum : states.Thum,
            YEAR : states.YEAR,
            ZoneName : states.ZoneName,
            City : states.City,

            updateFilters : false

        }

        
    }

    
    
    handleClick = (param) => {

        if(param==='Maslol') {

            let removeObj = {
                freeSearch : '',
                Maslol : '',
                Thum : '',
                YEAR : '',
                ZoneName : '',
                City : '',
            }

            this.setState(removeObj, () => {
    
                this.props.callBack(param,true);
    
            })


        } else {

            this.setState({
                [param] : '',
    
            }, () => {
    
                this.props.callBack(param, false);
    
            })

        }

        
        
    }
    
    componentDidUpdate () {

        let states = this.props.currentState;

        /* console.log(this.props.updateFilters);
        console.log(this.state.updateFilters); */


        if(this.props.updateFilters !== this.state.updateFilters) {

            this.setState({


                freeSearch : states.freeSearch,
                Maslol : states.Maslol,
                Thum : states.Thum,
                YEAR : states.YEAR,
                ZoneName : states.ZoneName,
                City : states.City,

                updateFilters : this.props.updateFilters
            });

        }

    }


    render() {

        //console.log(this.props.searchData);

        let idState = this.state.Thum;
        let ThumFromId = this.props.searchData.allThum.filter(function(desc) {
            return desc.Thum_Key === idState;
        });

        idState = this.state.ZoneName;
        let ZoneNameFromId = this.props.searchData.allCity_Zone.filter(function(desc) {
            return desc.City_Zone === idState;
        });

        idState = this.state.City;
        let cityNameFromId = this.props.searchData.cities.filter(function(desc) {
            return desc.City_Value === idState;
        });

        return (
            <div className="showFilters">
                <ul>

                    <li key="title" className="title">תוצאות חיפוש:</li>

                    {this.state.freeSearch ? <li key={`freeSearch-${this.state.freeSearch}`}>
                        <Button onClick={ () => this.handleClick('freeSearch')  }>{this.state.freeSearch}</Button>
                    </li> : false }

                    {this.state.Maslol ? <li key={`maslol-${this.state.Maslol}`}>
                        <Button onClick={ () => this.handleClick('Maslol')  }>{this.state.Maslol}</Button>
                    </li> : false }

                    {this.state.YEAR ? <li key={`year-${this.state.YEAR}`}>
                        <Button onClick={ () => this.handleClick('YEAR')  }>{this.state.YEAR}</Button>
                    </li> : false }

                    {this.state.Thum ? <li key={`thum-${this.state.Thum}`}>
                        <Button onClick={ () => this.handleClick('Thum')  }>{ThumFromId[0].Thum_Value}</Button>
                    </li> : false }

                    {this.state.ZoneName ? <li key={`zoneName-${this.state.ZoneName}`}>
                        <Button onClick={ () => this.handleClick('ZoneName')  }>{ZoneNameFromId[0].City_ZoneName}</Button>
                    </li> : false }

                    {this.state.City ? <li key={`city-${this.state.City}`}>
                        <Button onClick={ () => this.handleClick('City')  }>{cityNameFromId[0].City_Key}</Button>
                    </li> : false }

                </ul>
            </div>
        )
    }
}
