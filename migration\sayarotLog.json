{"table": "sayarotLog", "controller": "<PERSON><PERSON><PERSON>", "method": "", "explain": "sayarotLog", "title": "idno", "description": "SyrID", "seo_title": "", "seo_description": "", "image": "", "fields": {"idno": {"sort": "200", "width": "col-md-12", "name": "idno", "type": "integer", "explain": "idno", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "SyrID": {"sort": "190", "width": "col-md-12", "name": "SyrID", "type": "integer", "explain": "SyrID", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "SessionKey": {"sort": "180", "width": "col-md-12", "name": "<PERSON><PERSON><PERSON>", "type": "integer", "explain": "<PERSON><PERSON><PERSON>", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "actionType": {"sort": "170", "width": "col-md-12", "name": "actionType", "type": "short", "explain": "actionType", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "response": {"sort": "160", "width": "col-md-12", "name": "response", "type": "long", "explain": "response", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}}}