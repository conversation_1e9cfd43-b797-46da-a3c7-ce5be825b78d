<?php defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>t extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('token'));
           if( $postCode['token'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    public function newRegister($jPData = FALSE) {
        
        //https://sherut-leumi.wdev.co.il/api/register/newRegister?sebas=1
        //https://sherut-leumi.wdev.co.il/api/assets/files/david/registerSend.json
            
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('register');
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if( !$this->input->get('sebas')  ) { return $this->output ->set_status_header(403); }
        
        $getPosts = array(
            'FirstName',
            'LastName',
            'IDNO', //TZ
            'MobileStart',
            'Mobile',
            'BirthDate',
            'CityCode',
            'Email',
            'PrvSchool',
            'sex',
            'Category',
            'YearYad',
            'Password'
        );
        
        if($this->input->get('sebas')) {
            
            $this->db->select('*');
            $this->db->from('registersLog');
            $this->db->where('id', 1);
            $result= $this->db->get();
            $pagePosts = $result->row_array();
            
            $pagePosts['Password'] = '12345678';
            //unset($pagePosts['Password']);
//        
        } else {
            
            $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
            
            $pagePosts['BirthDate'] = changeDateFormat($pagePosts['BirthDate'], $stFormatFrom = "Y-m-d", $stFormatTo = "Ymd");
            $pagePosts['sex'] = $pagePosts['sex'] == 'man' ? '1' : '2';
            $pagePosts['Mobile'] = $pagePosts['MobileStart'].'-'.$pagePosts['Mobile'];
            unset($pagePosts['cellularStart']);

        }
        
        $output['fields'] = $pagePosts;
        
        $output['responseClient'] = $this->sherutLeumi->ApiClient($url = 'v2/volunteer/registerSend', $pagePosts);
        $output['error'] = $this->sherutLeumi->checkError($output['responseClient']);
        
        if  (   !$output['error'] &&
                ( isset($output['responseClient']['SessionKey']) &&
                !empty($output['responseClient']['SessionKey']) )    
            )
        {
            
            $userData = array(
                'IDNO' => $pagePosts['IDNO'],
                'Category' => $pagePosts['Category'],
                'FirstName' => $pagePosts['FirstName'],
                'LastName' => $pagePosts['LastName'],
                'Sex' => $pagePosts['sex'],
                'ImageUrl' => '',
                'SessionKey' => $output['responseClient']['SessionKey']
                    
            );
            
            $output['ok'] = $userData;
        }
        
        
        if(!$this->input->get('sebas')) {
            $insertLog = $this->sherutLeumi->addRegisterLog($pagePosts, $output['responseClient']);
        }
        
        
        
        
        $this->data = $output;
        if($output['error']) {$header = '400'; };
        
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function getSayarotSearchParams($jPData = FALSE) {
        
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('sayarot'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas') ) { return $this->output ->set_status_header(403); }
        
        $this->db->select('Rak_Value,Rak_Key');
        $this->db->from('sayarotTEMP');
        $this->db->where('Rak_Value !=', '');
        $this->db->distinct('Rak_Key');
        //$this->db->where('MtnState >', 0);
        $result= $this->db->get();
        $allRakazot = $result->result_array();
        
        $this->db->select('City_Value,City_Key');
        $this->db->from('sayarotTEMP');
        //$this->db->where('City_Value !=', '');
        $this->db->distinct('City_Key');
//        $this->db->where('MtnState >', 0);
        $result= $this->db->get();
        $allCities = $result->result_array();
        
        $allCities =  $this->msiteWs->sortArray($allCities,$keyName = 'City_Value',$order = SORT_ASC, $is_integer = false);
        $allRakazot =  $this->msiteWs->sortArray($allRakazot,$keyName = 'Rak_Value',$order = SORT_ASC, $is_integer = false);
        
        $output['cities'] = $allCities;
        $output['rakazot'] = $allRakazot;
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function getSayarot($jPData = FALSE) {
        
        
        
        $sayaertPin = $this->getSayeretPin(); 
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('sayarot'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential','isRotemDev'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas') ) { return $this->output ->set_status_header(403); }
        
        //echo "<pre>";
        //print_r($jsonPosts);
        //die('-end-');
        
        
        
       $checkPopulate = $this->sherutLeumi->checkPopulate();
        
        $getPosts = array(
            'freeSearch',
            'rakaz',
            'sayeretId',
            'city',
            'limit',
            'page',
            'orderBy'
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        
        $this->db->select('*');
        $this->db->from('sayarotTEMP');
        //$this->db->where('MtnState >', 0);
        
        $rows = !empty($pagePosts['limit']) ? (int)$pagePosts['limit'] : 12;
        
        if(!empty($pagePosts['freeSearch'])) {
            $this->db->group_start();
                $this->db->like('Name', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('id', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('ArriveTo', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('Notes', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('Info', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('ArriveNotes', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('Mosadot', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('City_Value', $pagePosts['freeSearch'], 'both');
                $this->db->or_like('Rak_Value', $pagePosts['freeSearch'], 'both');
            $this->db->group_end();
        }
        
        if(!empty($pagePosts['rakaz'])) {
            $this->db->where('Rak_Key', $pagePosts['rakaz']);
        }
        
         if(!empty($pagePosts['sayeretId'])) {
            $this->db->where('id', $pagePosts['sayeretId']);
        }
        
        if(!empty($pagePosts['city'])) {
            $this->db->where('City_Key', $pagePosts['city']);
        }
        
        if(!empty($pagePosts['orderBy'])) {
            
            if($pagePosts['orderBy'] == 'מקומות שנותרו') {
                $this->db->order_by('placesLeft', 'DESC');
            }
            
            elseif($pagePosts['orderBy'] == 'מיקום') {
                $this->db->order_by('City_Value', 'ASC');
            }
            
            else {
                $this->db->order_by('SyrDay', 'ASC');
            }
            
        } else {
            
            $this->db->order_by('SyrDay', 'ASC');
            
        }
        
        
        //$this->db->order_by('id', 'DESC');
        
        $page = !empty((int)$pagePosts['page']) ? (int)$pagePosts['page'] : 0;
        $page = $page > 0 ? $rows*$page : $page;

        $this->db->limit($rows,$page);

        $result= $this->db->get();
        
        $sayarotDB = $result->result_array();
        
        $sayarot = array();
        
        if(!empty($sayarotDB)) {
            
            
            //INSERT SAYERET PIN! // ['id'] == '10454'
            if(     
                $page === 0 && !empty($sayaertPin) && empty($pagePosts['rakaz']  )
    //            empty($pagePosts['city']) &&
    //            empty($pagePosts['freeSearch']) 

                ) {
                $flagExist = false;
                foreach ($sayarotDB as $valueExist) {
                    if($valueExist['id'] == '10454') {
                        $flagExist = true;
                    }
                }
                
                if(!$flagExist && !empty($sayaertPin)) {$sayarot[] = $sayaertPin;};
            }
            
            
            foreach ($sayarotDB as $value) {
                
                //TESTS
                //if ($value['id'] == '9191') {
                    //$value['RegStart'] = '20211207';
                    //$value['placesLeft'] = '0';
                //}
                
                if(!empty($value['RegStart'])) {
                    $value['registerDate'] = changeDateFormat($value['RegStart'], "Ymd", "d.m.Y");
                    $value['SyrDayDate'] = changeDateFormat($value['SyrDay'], "Ymd", "d.m.Y");
                } else {
                    $value['registerDate'] = '---';
                }
                
                $value['outdated'] = '0';
                $value['notStartedReg'] = '0';
                
                $today = date("Y-m-d");
                $today_date = new DateTime($today);
                
                if(!empty($value['RegStart'])) {
                    
                    $regStart = changeDateFormat($value['RegStart'], "Ymd", "Y-m-d");
                    $regStartdate = new DateTime($regStart);
                    if ( !empty($value['RegStart']) && ($regStartdate > $today_date) ) {
                        
                        $value['notStartedReg'] = $value['registerDate'];
                        
                    }
                }
                
                if(!empty($value['SyrDay'])) {
                    
                    
                    $value['SyrDayDate'] = changeDateFormat($value['SyrDay'], "Ymd", "d.m.Y");
                    
                      
                    $enddate = changeDateFormat($value['SyrDay'], "Ymd", "Y-m-d");
                    $today_date = new DateTime($today);
                    $expiry_date = new DateTime($enddate);
                    
                    if ($expiry_date <= $today_date) { $value['outdated'] = '1';}
                    
                } else {
                    $value['SyrDayDate'] = '---';
                }
                
                if(!empty($value['Mosadot'])) {
                    
                    $Mosdot = json_decode($value['Mosadot'], true);
                    $text = '';
                    
                    if(isset($Mosdot[0])) {
                        
                        foreach ($Mosdot as $key => $valueMosdot) {
                            
                            if($key != 0) {
                                $text .= ' / ';
                            }
                            $text .= $valueMosdot['Name'];
                        }
                        
                    }
                    
                    $value['Mosadot'] = $text;
                    //$value['Mosadot'] = str_replace("~", " ", $value['Mosadot']);
                    
                } else {
                    $value['Mosadot'] = '---';
                }
                
                unset($value['RegStart']);
                
                
                //$value['notStartedReg'] = '22.01.2023 ב-12:00';
                
                $sayarot[] = $value;
                
            }
            
        }
        
        $output['items'] = $sayarot;
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    private function getSayeretPin() {
        
        
        $this->db->select('*');
        $this->db->from('sayarotTEMP');
        
        $this->db->where('id', '10454');
        $result= $this->db->get();
        
        $sayarotDB = $result->result_array();
        
        $sayarot = array();
        
        if(!empty($sayarotDB)) {
            
            foreach ($sayarotDB as $value) {
                
                if(!empty($value['RegStart'])) {
                    $value['registerDate'] = changeDateFormat($value['RegStart'], "Ymd", "d.m.Y");
                    $value['SyrDayDate'] = changeDateFormat($value['SyrDay'], "Ymd", "d.m.Y");
                } else {
                    $value['registerDate'] = '---';
                }
                
                $value['outdated'] = '0';
                $value['notStartedReg'] = '0';
                
                $today = date("Y-m-d");
                $today_date = new DateTime($today);
                
                if(!empty($value['RegStart'])) {
                    
                    $regStart = changeDateFormat($value['RegStart'], "Ymd", "Y-m-d");
                    $regStartdate = new DateTime($regStart);
                    if ( !empty($value['RegStart']) && ($regStartdate > $today_date) ) { $value['notStartedReg'] = $value['registerDate'];}
                }
                
                if(!empty($value['SyrDay'])) {
                    
                    
                    $value['SyrDayDate'] = changeDateFormat($value['SyrDay'], "Ymd", "d.m.Y");
                    
                      
                    $enddate = changeDateFormat($value['SyrDay'], "Ymd", "Y-m-d");
                    $today_date = new DateTime($today);
                    $expiry_date = new DateTime($enddate);
                    
                    if ($expiry_date <= $today_date) { $value['outdated'] = '1';}
                    
                } else {
                    $value['SyrDayDate'] = '---';
                }
                
                if(!empty($value['Mosadot'])) {
                    
                    $Mosdot = json_decode($value['Mosadot'], true);
                    $text = '';
                    
                    if(isset($Mosdot[0])) {
                        
                        foreach ($Mosdot as $key => $valueMosdot) {
                            
                            if($key != 0) {
                                $text .= ' / ';
                            }
                            $text .= $valueMosdot['Name'];
                        }
                        
                    }
                    
                    $value['Mosadot'] = $text;
                    //$value['Mosadot'] = str_replace("~", " ", $value['Mosadot']);
                    
                } else {
                    $value['Mosadot'] = '---';
                }
                
                unset($value['RegStart']);
                
                $sayarot[] = $value;
                
            }
            
        }
        
        $output = isset($sayarot[0]) ? $sayarot[0] : array();
        
        return $output;
        
        //echo "<pre>";
        //print_r($output);
        
//        $this->data = $output;
//         
//        return $this->output
//                ->set_status_header(!isset($header) ? 200 : $header)
//                ->set_content_type('application/json')
//                ->set_output(json_encode($this->data));
    }
    
    
    
    
    public function changeSayarotUser($jPData = FALSE) {
        
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('sayarot'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas') ) { $output['error'] = 'שגיאה'; return $this->output ->set_status_header(403); }
        
        $getPosts = array(
            'IDNO',
            'SessionKey',
            'sayarId'
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        
        if( $this->sherutLeumi->checkSessionKey($pagePosts['IDNO'], $pagePosts['SessionKey']) ) {
            
            $output = 'unauthorized';
            
            return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($output));
            
        };
        
        $output['newArray'] = array();
        
        $sayarotUser = $this->sherutLeumi->getSayarotUser(
                array(
                    'IDNO' => $pagePosts['IDNO'],
                    'SessionKey' => $pagePosts['SessionKey']
                ));
        
        $sayarotCount = count($sayarotUser);
        
        
        if(!empty($sayarotCount)) {
            
            $sayarExist = false;
            
            foreach ($sayarotUser as $value) {
                
                if($value == $pagePosts['sayarId']) {
                    
                    // Remove Sayar from DB
                    $removeSayeret = $this->sherutLeumi->removeSayeret(
                                    array(
                                        'IDNO' => $pagePosts['IDNO'],
                                        'SessionKey' => $pagePosts['SessionKey'],
                                        'sayarId' => $pagePosts['sayarId']
                                    ));
                    
                    if(isset($removeSayeret['errorMsg']) && empty($removeSayeret['errorMsg'])) {
                    
                        $output['newArray'] = $removeSayeret['newArray'];
                        
                    } else {
                        $output['error'] = isset($removeSayeret['errorMsg']) ? $removeSayeret['errorMsg'] : 'שגיאה';
                    }
                    
                    $sayarExist = true;
                }
                
            }
            
            // addSAYARFUNC
            if(!$sayarExist) {
                
                $addSayeret = $this->sherutLeumi->addSayeret(
                    array(
                        'IDNO' => $pagePosts['IDNO'],
                        'SessionKey' => $pagePosts['SessionKey'],
                        'sayarId' => $pagePosts['sayarId']
                    ));
                
                if(isset($addSayeret['errorMsg']) && empty($addSayeret['errorMsg'])) {
                    
                    $output['newArray'] = $addSayeret['newArray'];
                    
                } else {
                    $output['error'] = isset($addSayeret['errorMsg']) ? $addSayeret['errorMsg'] : 'שגיאה';
                }

            }
            
            
        } else {
            
            
             $addSayeret = $this->sherutLeumi->addSayeret(
                array(
                    'IDNO' => $pagePosts['IDNO'],
                    'SessionKey' => $pagePosts['SessionKey'],
                    'sayarId' => $pagePosts['sayarId']
                ));

            if(isset($addSayeret['errorMsg']) && empty($addSayeret['errorMsg'])) {
                    
                $output['newArray'] = $addSayeret['newArray'];

            } else {
                $output['error'] = isset($addSayeret['errorMsg']) ? $addSayeret['errorMsg'] : 'שגיאה';
            }
            
        }
        
        
        $output['sayarotUser'] = $sayarotUser;
        $output['sayarotCount'] = $sayarotCount;
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    public function getMySayarot($jPData = FALSE) {
        
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('sayarot'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas')) { return $this->output ->set_status_header(403); }
        
        $checkPopulate = $this->sherutLeumi->checkPopulate();
        
        $getPosts = array(
            'IDNO',
            'SessionKey'
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        
        $sayarotUser = $this->sherutLeumi->getSayarotUser(
                array(
                    'IDNO' => $pagePosts['IDNO'],
                    'SessionKey' => $pagePosts['SessionKey']
                ));
        
        if(!empty($sayarotUser)) {
            
            
            $this->db->select('*');
            $this->db->from('sayarotTEMP');
            $this->db->order_by('RegStart', 'ASC');
            
            foreach ($sayarotUser as $key => $value) {
                
                if($key == 0) {
                    $this->db->where('id', $value);
                }
                
                $this->db->or_where('id', $value);
                
            }
            
            
            //$this->db->order_by('id', 'DESC');
        
            $this->db->limit(10);

            $result= $this->db->get();

            $sayarotDB = $result->result_array();

            $sayarot = array();

            if(!empty($sayarotDB)) {

                    foreach ($sayarotDB as $value) {

                        if(!empty($value['RegStart'])) {
                            $value['registerDate'] = changeDateFormat($value['RegStart'], "Ymd", "d.m.Y");
                            $value['SyrDayDate'] = changeDateFormat($value['SyrDay'], "Ymd", "d.m.Y");
                        } else {
                            $value['registerDate'] = '---';
                        }

                        $value['outdated'] = '0';
                        $value['notStartedReg'] = '0';

                        if(!empty($value['SyrDay'])) {


                            $value['SyrDayDate'] = changeDateFormat($value['SyrDay'], "Ymd", "d.m.Y");

                            $today = date("Y-m-d");  
                            $enddate = changeDateFormat($value['SyrDay'], "Ymd", "Y-m-d");

                            $regStart = changeDateFormat($value['RegStart'], "Ymd", "Y-m-d");

                            $today_date = new DateTime($today);
                            $expiry_date = new DateTime($enddate);
                            $regStartdate = new DateTime($regStart);

                            if ($expiry_date <= $today_date) { $value['outdated'] = '1';}

                            if ( !empty($value['RegStart']) && ($regStartdate > $today_date) ) { $value['notStartedReg'] = $value['registerDate'];}

                        } else {
                            $value['SyrDayDate'] = '---';
                        }

                        if(!empty($value['Mosadot'])) {

                            $Mosdot = json_decode($value['Mosadot'], true);
                            $text = '';
                            if(isset($Mosdot[0])) {
                                foreach ($Mosdot as $key => $valueMosdot) {
                                    if($key != 0) {
                                        $text .= ' / ';
                                    }
                                    $text .= $valueMosdot['Name'];
                                }
                            }
                            $value['Mosadot'] = $text;
                            //$value['Mosadot'] = str_replace("~", " ", $value['Mosadot']);

                        } else {
                            $value['Mosadot'] = '---';
                        }

                        unset($value['RegStart']);

                        $sayarot[] = $value;

                    }

                }

        }
        
        else {
            
            $sayarot = array();
            
        }
        
        
        
        $output['sayarot'] = $sayarot;
        
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    //https://sherut-leumi.wdev.co.il/api/sayarot/populateSayarot
    public function populateSayarot($jPData = FALSE) {
        
        echo $this->sherutLeumi->populateSayarot();
        
    }
    
    //https://sherut-leumi.wdev.co.il/api/sayarot/populateMekomot
    public function populateMekomot($jPData = FALSE) {
        
        echo $this->sherutLeumi->checkPopulateMekSherutNEW();
        
    }
}