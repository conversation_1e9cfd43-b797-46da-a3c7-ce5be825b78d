
<?php
//echo '<pre>';
//print_r($migration);
//echo '</pre>';

function cmp($a, $b) {
    return ($a['sort'] > $b['sort']) ? -1 : 1;
}

if(isset($migration['fields']) && !empty($migration['fields'])) {
    usort($migration['fields'], "cmp");
}
?>
<style>
    .migration-field {
        padding: 10px;
    }
</style>
<div class="row">
    <?php echo form_open_multipart('admin/installation/put' . getQS(), array("class" => "ajax-installation")); ?>

    <div class="col col-sm-12">
<!--        <h4><?php echo isset($migration['explain']) ? "עדכון אובייקט " .'"'. $migration['explain'] .'"' : "הוסף אובייקט חדש"; ?></h4>-->
        <div class="row">
            <div class="col col-sm-12">
                <div class="row">
                    <div class="form-group col-lg-2">
                        <label for="put_migration_table">שם הטבלה באנגלית</label>
                        <input id="put_migration_table" dir="lrt" type="text" class="form-control" name="table" value="<?php echo isset($migration['table']) ? $migration['table'] : ""; ?>">
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="put_migration_explain">שם האובייקט בעברית</label>
                        <input id="put_migration_explain" type="text" class="form-control" name="explain" value="<?php echo isset($migration['explain']) ? $migration['explain'] : ""; ?>">
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="put_migration_controller">CONTROLLER</label>
                        <input id="put_migration_controller" dir="lrt" type="text" class="form-control" name="controller" value="<?php echo isset($migration['controller']) ? $migration['controller'] : "welcome"; ?>">
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="put_migration_method">METHOD</label>
                        <input id="put_migration_method" dir="lrt" type="text" class="form-control" name="method" value="<?php echo isset($migration['method']) ? $migration['method'] : ""; ?>">
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="put_migration_controller">SEO TITLE</label>
                        <input id="put_migration_seo_title" dir="lrt" type="text" class="form-control" name="seo_title" value="<?php echo isset($migration['seo_title']) ? $migration['seo_title'] : ""; ?>">
                    </div>
                    <div class="form-group col-lg-2">
                        <label for="put_migration_seo_description">SEO DESCRIPTION</label>
                        <input id="put_migration_seo_description" dir="lrt" type="text" class="form-control" name="seo_description" value="<?php echo isset($migration['seo_description']) ? $migration['seo_description'] : ""; ?>">
                    </div>
                </div>
            </div>
            <div class="col col-sm-12">
                <h4>הגדרת שדות בטבלה</h4>
                <div class="all-migration-fields">
                    <div class="row card blue lighten-5 migration-field" style="background-color: #d9e8f5;">
                        <div class="form-group col-lg-1">
                            <label>סדר</label>
                            <input dir="ltr" type="number" class="form-control" name="field_sort[0]" value="0">
                        </div>
                        <div class="form-group col-lg-1">
                            <label>רוחב</label>
                            <select name="field_width[0]" class="form-control">
                                <option value="col-md-12">12</option>
                                <option value="col-md-11">11</option>
                                <option value="col-md-10">10</option>
                                <option value="col-md-9">9</option>
                                <option value="col-md-8">8</option>
                                <option value="col-md-7">7</option>
                                <option value="col-md-6">6</option>
                                <option value="col-md-5">5</option>
                                <option value="col-md-4">4</option>
                                <option value="col-md-3">3</option>
                                <option value="col-md-2">2</option>
                                <option value="col-md-1">1</option>
                            </select>
                        </div>
                        
                        <div class="form-group col-lg-2">
                            <label>שם השדה באנגלית</label>
                            <input dir="ltr" type="text" class="form-control" name="field_name[0]">
                        </div>
                        <div class="form-group col-lg-2">
                            <label>סוג השדה</label>
                            <select name="field_type[0]" class="form-control" data-field-type="">
                                <option value="" disabled selected>בחר את סוג השדה</option>
                                <?php foreach(Obj::$Field_types as $key_type => $type) { ?>
                                    <option value="<?php echo isset($key_type) ? $key_type : ""; ?>"><?php echo isset($type) ? $type : ""; ?></option>
                                    <?php } ?>
                            </select>
                        </div>
                        <div class="form-group col-lg-2">
                            <label>שם השדה בעברית</label>
                            <input type="text" class="form-control" name="field_explain[0]">
                        </div>
                        <div class="form-group-no col-lg-2">
                            <p>
                                <input id="put_migration_title" type="radio" name="title" value="" <?php echo !isset($migration['title']) || $migration['title'] === '' ? "checked" : ""; ?> />
                                <label for="put_migration_title">title</label>
                            </p>
                            <p>
                                <input id="put_migration_description" type="radio" name="description" value="" <?php echo !isset($migration['description']) || $migration['description'] === '' ? "checked" : ""; ?> />
                                <label for="put_migration_description">description</label>
                            </p>
                            <p>
                                <input id="put_migration_image" type="radio" name="image" value="" <?php echo !isset($migration['image']) || $migration['image'] === '' ? "checked" : ""; ?> />
                                <label for="put_migration_image">image</label>
                            </p>
                        </div>
                        <div class="form-group col-lg-1">
                            <label>שפות</label>
                            <select name="field_lang[0]" class="form-control">
                                <option value="0">לא</option>
                                <option value="1">כן</option>
                            </select>
                        </div>
                        <div class="form-group col-lg-1">
                            <button type="submit" class="btn btn-primary">
                                הוסף
                            </button>
                        </div>
                        <div class="col-lg-12">
                            <div class="row options search" style="display: none;">
                                <span class="col-lg-12">אפשרויות חיפוש</span>
                                <div class="form-group col col-lg-3">
                                    <label>שם הטבלה</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_table[0]" value="">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>שדה הערך</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_field_value[0]" value="">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>שדה הטקסט</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_field_text[0]" value="">
                                </div>
                            </div>
                            
                            <div class="row options multipleTable" style="display: none;">
                                <span class="col-lg-12">טבלה להצגת נתונים לבחירה</span>
<!--                                <div class="form-group col col-lg-3">
                                    <label>שם הטבלה</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_multipleTable[0]" value="">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>שדה הערך</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_field_valuemultipleTable[0]" value="">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>שדה הטקסט</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_field_textmultipleTable[0]" value="">
                                </div>-->
                            </div>
                            
                            <div class="row options table" style="display: none;">
                                <span class="col-lg-12">אפשרויות טבלה</span>
                                <div class="form-group col col-lg-3">
                                    <label>שם הטבלה</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_table[0]" value="">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>שדה הערך</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_field_value[0]" value="">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>שדה הטקסט</label>
                                    <input dir="ltr" type="text" class="form-control" name="field_option_field_text[0]" value="">
                                </div>
                            </div>
                            <div class="row options image" style="display: none;">
                                <span class="col-lg-12">אפשרויות תמונה</span>
                                <div class="form-group col col-lg-3">
                                    <label>רוחב</label>
                                    <input dir="ltr" type="number" class="form-control" name="field_option_width[0]" value="0">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>גובה</label>
                                    <input dir="ltr" type="number" class="form-control" name="field_option_height[0]" value="0">
                                </div>
                            </div>
                            <div class="row options choice" style="display: none;">
                                <span class="col-lg-12">אפשרויות בחירה בודדת</span>
                                <div class="form-group col-lg-12">
                                    <div class="chips" data-input="#put_field_options_choices" data-seperator="|"></div>
                                    <input id="put_field_options_choices" type="hidden" name="field_option_choices[0]" value="">
                                </div>
                            </div>
                            <div class="row options multiple" style="display: none;">
                                <span class="col-lg-12">אפשרויות בחירה מרובה</span>
                                <div class="form-group col-lg-12">
                                    <div class="chips" data-input="#put_field_options_multiples" data-seperator="|"></div>
                                    <input id="put_field_options_multiples" type="hidden" name="field_option_multiples[0]" value="">
                                </div>
                            </div>
                            <div class="row options boolean" style="display: none;">
                                <span class="col-lg-12">אפשרויות כן/לא</span>
                                <div class="form-group col col-lg-3">
                                    <label>ערך עבור חיובי</label>
                                    <input type="text" class="form-control" name="field_option_yes[0]" value="כן">
                                </div>
                                <div class="form-group col col-lg-3">
                                    <label>ערך עבור שלילי</label>
                                    <input type="text" class="form-control" name="field_option_no[0]" value="לא">
                                </div>
                            </div>
                        </div> 
                    </div>

                    <?php if(isset($migration['fields']) && $migration['fields']) { ?>
                    <div>
                        
                        <?php $i = 0; foreach($migration['fields'] as $key => $row) { $i++; ?>
                        <hr/>
                        <div class="row card migration-field" style="background-color: <?php echo $i % 2 == 0 ? "#efefef" : "#d4d4d4"; ?>;">
                            <div class="form-group col-lg-1">
                                <label>סדר</label>
                                <input dir="ltr" type="number" class="form-control" name="field_sort[<?php echo $i; ?>]" value="<?php echo isset($row['sort']) ? $row['sort'] : $i; ?>">
                            </div>
                            <div class="form-group col-lg-1">
                                <label>רוחב</label>
                                <select name="field_width[<?php echo $i; ?>]" class="form-control">
                                    <option value="col-md-12" <?php echo isset($row['width']) && $row['width'] === "col-md-12" ? "selected" : ""; ?>>12</option>
                                    <option value="col-md-11" <?php echo isset($row['width']) && $row['width'] === "col-md-11" ? "selected" : ""; ?>>11</option>
                                    <option value="col-md-10" <?php echo isset($row['width']) && $row['width'] === "col-md-10" ? "selected" : ""; ?>>10</option>
                                    <option value="col-md-9" <?php echo isset($row['width']) && $row['width'] === "col-md-9" ? "selected" : ""; ?>>9</option>
                                    <option value="col-md-8" <?php echo isset($row['width']) && $row['width'] === "col-md-8" ? "selected" : ""; ?>>8</option>
                                    <option value="col-md-7" <?php echo isset($row['width']) && $row['width'] === "col-md-7" ? "selected" : ""; ?>>7</option>
                                    <option value="col-md-6" <?php echo isset($row['width']) && $row['width'] === "col-md-6" ? "selected" : ""; ?>>6</option>
                                    <option value="col-md-5" <?php echo isset($row['width']) && $row['width'] === "col-md-5" ? "selected" : ""; ?>>5</option>
                                    <option value="col-md-4" <?php echo isset($row['width']) && $row['width'] === "col-md-4" ? "selected" : ""; ?>>4</option>
                                    <option value="col-md-3" <?php echo isset($row['width']) && $row['width'] === "col-md-3" ? "selected" : ""; ?>>3</option>
                                    <option value="col-md-2" <?php echo isset($row['width']) && $row['width'] === "col-md-2" ? "selected" : ""; ?>>2</option>
                                    <option value="col-md-1" <?php echo isset($row['width']) && $row['width'] === "col-md-1" ? "selected" : ""; ?>>1</option>
                                </select>
                            </div>
                            
                            <div class="form-group col col-lg-2">
                                <label>שם השדה באנגלית</label>
                                <input dir="ltr" type="text" class="form-control" name="field_name[<?php echo $i; ?>]" value="<?php echo isset($row['name']) ? $row['name'] : ""; ?>">
                            </div>
                            <div class="form-group col col-lg-2">
                                <label>סוג השדה</label>
                                <select name="field_type[<?php echo $i; ?>]" class="form-control" data-field-type="<?php echo isset($row['type']) && $row['type'] === $key_type ? "selected" : ""; ?>">
                                    <option value="" disabled selected>בחר את סוג השדה</option>
                                    <?php foreach(Obj::$Field_types as $key_type => $type) { ?>
                                    <option value="<?php echo isset($key_type) ? $key_type : ""; ?>" <?php echo isset($row['type']) && $row['type'] === $key_type ? "selected" : ""; ?>><?php echo isset($type) ? $type : ""; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                            <div class="form-group col col-lg-2">
                                <label>שם השדה בעברית</label>
                                <input type="text" class="form-control" name="field_explain[<?php echo $i; ?>]" value="<?php echo isset($row['explain']) ? $row['explain'] : ""; ?>">
                            </div>
                            <div class="form-group col-lg-2">
                                <p>
                                    <input id="put_migration_title_<?php echo isset($key) ? $key : ""; ?>" type="radio" name="title" value="<?php echo isset($row['name']) ? $row['name'] : ""; ?>" <?php echo isset($migration['title']) && isset($row['name']) && $migration['title'] === $row['name'] ? "checked" : ""; ?> />
                                    <label for="put_migration_title_<?php echo isset($key) ? $key : ""; ?>">title</label>
                                </p>
                                <p>
                                    <input id="put_migration_description_<?php echo isset($key) ? $key : ""; ?>" type="radio" name="description" value="<?php echo isset($row['name']) ? $row['name'] : ""; ?>" <?php echo isset($migration['description']) && isset($row['name']) && $migration['description'] === $row['name'] ? "checked" : ""; ?> />
                                    <label for="put_migration_description_<?php echo isset($key) ? $key : ""; ?>">description</label>
                                </p>
                                <p>
                                    <input id="put_migration_image_<?php echo isset($key) ? $key : ""; ?>" type="radio" name="image" value="<?php echo isset($row['name']) ? $row['name'] : ""; ?>" <?php echo isset($migration['image']) && isset($row['name']) && $migration['image'] === $row['name'] ? "checked" : ""; ?> />
                                    <label for="put_migration_image_<?php echo isset($key) ? $key : ""; ?>">image</label>
                                </p>
                            </div>
<!--                            <div class="form-group col-lg-1">
                                <label>שפות</label>
                                <select name="field_lang[<?php echo $i; ?>]" class="form-control">
                                    <option value="0" <?php echo isset($row['lang']) && $row['lang'] === '0' ? "selected" : ""; ?>>לא</option>
                                    <option value="1" <?php echo isset($row['lang']) && $row['lang'] === '1' ? "selected" : ""; ?>>כן</option>
                                </select>
                            </div>-->
                            <div class="form-group col-lg-1">
                                <input type="hidden" name="field_lang[<?php echo $i; ?>]" value="0"/>
                                <button type="button" class="btn btn-danger" onclick="remove_migration_field(this)">הסר</button>
                            </div>
                            <div class="col-lg-12">
                                <div class="row options search" style="display: <?php echo isset($row['type']) && $row['type'] === 'search' ? "block" : "none"; ?>;">
                                    <span class="col-lg-12">אפשרויות חיפוש</span>
                                    <div class="form-group col col-lg-3">
                                        <label>שם הטבלה</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_table[<?php echo $i; ?>]" value="<?php echo isset($row['options']['table']) ? $row['options']['table'] : ""; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>שדה הערך</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_field_value[<?php echo $i; ?>]" value="<?php echo isset($row['options']['field_value']) ? $row['options']['field_value'] : ""; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>שדה הטקסט</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_field_text[<?php echo $i; ?>]" value="<?php echo isset($row['options']['field_text']) ? $row['options']['field_text'] : ""; ?>">
                                    </div>
                                </div>
                                <div class="row options table" style="display: <?php echo isset($row['type']) && $row['type'] === 'table' ? "block" : "none"; ?>;">
                                    <span class="col-lg-12">אפשרויות טבלה</span>
                                    <div class="form-group col col-lg-3">
                                        <label>שם הטבלה</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_table[<?php echo $i; ?>]" value="<?php echo isset($row['options']['table']) ? $row['options']['table'] : ""; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>שדה הערך</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_field_value[<?php echo $i; ?>]" value="<?php echo isset($row['options']['field_value']) ? $row['options']['field_value'] : ""; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>שדה הטקסט</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_field_text[<?php echo $i; ?>]" value="<?php echo isset($row['options']['field_text']) ? $row['options']['field_text'] : ""; ?>">
                                    </div>
                                </div>
                                
                                <div class="row options multipleTable" style="display: <?php echo isset($row['type']) && $row['type'] === 'multipleTable' ? "block" : "none"; ?>;">
                                    <span class="col-lg-12">טבלה להצגת נתונים לבחירה</span>
                                    <div class="form-group col col-lg-3">
                                        <label>שם הטבלה</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_table[<?php echo $i; ?>]" value="<?php echo isset($row['options']['table']) ? $row['options']['table'] : ""; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>שדה הערך</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_field_value[<?php echo $i; ?>]" value="<?php echo isset($row['options']['field_value']) ? $row['options']['field_value'] : ""; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>שדה הטקסט</label>
                                        <input dir="ltr" type="text" class="form-control" name="field_option_field_text[<?php echo $i; ?>]" value="<?php echo isset($row['options']['field_text']) ? $row['options']['field_text'] : ""; ?>">
                                    </div>
                                </div>
                                
                                <div class="row options image" style="display: <?php echo isset($row['type']) && $row['type'] === 'image' ? "block" : "none"; ?>;">
                                    <span class="col-lg-12">אפשרויות תמונה</span>
                                    <div class="form-group col col-lg-3">
                                        <label>רוחב</label>
                                        <input dir="ltr" type="number" class="form-control" name="field_option_width[<?php echo $i; ?>]" value="<?php echo isset($row['options']['width']) ? $row['options']['width'] : "0"; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>גובה</label>
                                        <input dir="ltr" type="number" class="form-control" name="field_option_height[<?php echo $i; ?>]" value="<?php echo isset($row['options']['height']) ? $row['options']['height'] : "0"; ?>">
                                    </div>
                                </div>
                                <div class="row options choice" style="display: <?php echo isset($row['type']) && $row['type'] === 'choice' ? "block" : "none"; ?>;">
                                    <span class="col-lg-12">אפשרויות בחירה בודדת</span>
                                    <div class="form-group col-lg-12">
                                        <input type="text" class="form-control" data-role="tagsinput" name="field_option_choices[<?php echo $i; ?>]" value="<?php echo isset($row['options']['choices']) ? $row['options']['choices'] : ""; ?>">
                                    </div>
                                </div>
                                <div class="row options multiple" style="display: <?php echo isset($row['type']) && $row['type'] === 'multiple' ? "block" : "none"; ?>;">
                                    <span class="col-lg-12">אפשרויות בחירה מרובה</span>
                                    <div class="form-group col-lg-12">
                                        <input type="text" class="form-control" data-role="tagsinput" name="field_option_multiples[<?php echo $i; ?>]" value="<?php echo isset($row['options']['multiples']) ? $row['options']['multiples'] : ""; ?>">
                                    </div>
                                </div>
                                <div class="row options boolean" style="display: <?php echo isset($row['type']) && $row['type'] === 'boolean' ? "block" : "none"; ?>;">
                                    <span class="col-lg-12">אפשרויות כן/לא</span>
                                    <div class="form-group col col-lg-3">
                                        <label>ערך עבור חיובי</label>
                                        <input type="text" class="form-control" name="field_option_yes[<?php echo $i; ?>]" value="<?php echo isset($row['options']['yes']) ? $row['options']['yes'] : ""; ?>">
                                    </div>
                                    <div class="form-group col col-lg-3">
                                        <label>ערך עבור שלילי</label>
                                        <input type="text" class="form-control" name="field_option_no[<?php echo $i; ?>]" value="<?php echo isset($row['options']['no']) ? $row['options']['no'] : ""; ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                    <?php } ?>

                </div>
            </div>
        </div>
    </div>
    
    
    <div class="fixed-action-btn" style="bottom: 45px; left: 24px;">
        <button type="submit" class="btn pulse btn-<?php echo isset($migration['explain']) ? "success" : "primary"; ?>"><?php echo isset($migration['explain']) ? "שמור" : "הוסף"; ?></button>
        <?php if(isset($migration['table']) && !empty($migration['table'])) { ?>
            <button type="button" data-build="<?php echo base_url('admin/installation/build/' . $migration['table']); ?>" class="btn pulse btn-info">צור טבלה</button>
        <?php } ?>
    </div>

    <?php echo form_close(); ?>
</div>