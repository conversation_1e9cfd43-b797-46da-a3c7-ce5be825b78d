import React, { useState, useEffect } from "react";
import loader from "../../img/preLoader.gif";
import { Container, Row, Col, Button } from "react-bootstrap"; //npm install react-bootstrap@next bootstrap@5.1.0
import Spinner from 'react-bootstrap/Spinner';
import CustomFloatInput from "../-Helpers-/forms/CustomFloatInput";
import RestUrls from "../../services/RestUrls";

const LoginPage = () => {
  const [state, setState] = useState({
    IDNO: "",
    Password: "",
    response: false,
    loading: false,
    checkInputs: false,
    error: null
  });

  // Add useEffect to clear localStorage when login page loads
  useEffect(() => {
    console.log("Login page mounted - clearing localStorage");
    // Clear all authentication-related items
    localStorage.removeItem("userData");
    localStorage.removeItem("userInfo");
    localStorage.removeItem("sessionKey");
    localStorage.removeItem("rakazid");
    localStorage.removeItem("sayeretid");
  }, []);

  const updateValue = (newValue) => {
    setState((prevState) => ({ ...prevState, ...newValue }));
  };

  const sendForm = (sendRules) => {
    if (!sendRules) {
      setState(prev => ({ ...prev, checkInputs: true }));
      return;
    }

    setState(prev => ({ ...prev, loading: true }));
    
    // Direct fetch call instead of using sendtoAdmin
    fetch(`${RestUrls.API_URL}v2/Volunteer/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        IDNO: state.IDNO,
        Password: state.Password,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        setState(prev => ({
          ...prev,
          response: data,
          loading: false,
          responseLogin: data
        }));
        
        // Handle successful login
        if (data.Result === "Success") {
          // Save sessionKey
          localStorage.setItem("sessionKey", data.SessionKey);
          
          // Store using the standard format for AuthContext
          const userData = {
            SessionKey: data.SessionKey,
            FirstName: data.FirstName,
            LastName: data.LastName,
            Sex: data.Sex,
            Category: data.Category,
            InService: data.InService
          };
          localStorage.setItem("userData", JSON.stringify(userData));
          
          // Keep backward compatibility with userInfo
          localStorage.setItem("userInfo", JSON.stringify({
            firstName: data.FirstName,
            lastName: data.LastName,
            sex: data.Sex,
            category: data.Category,
            inService: data.InService
          }));
          
          if (data.sayeretId) {
            localStorage.setItem("sayeretid", data.sayeretId);
          }
        }
      })
      .catch((error) => {
        console.error("Error logging in:", error);
        setState(prev => ({
          ...prev,
          loading: false,
          error: "שגיאה בהתחברות. בדקו את חיבור האינטרנט או נסו שוב מאוחר יותר."
        }));
      });
  };

  const sendRules = state.IDNO && state.Password;

  return (
    <div className="loginPage animate__animated animate__fadeIn">
      {state.response && console.log(state.response)}

      <Container>
        <Row className="justify-content-md-center">
          <Col lg={6}>
            <header>
              <h1 className="boldTypeFamily">התחברות למערכת</h1>
              <p>ברוכים השבים לאגודה להתנדבות</p>
            </header>

            {state.error && (
              <div className="alert alert-danger" role="alert">
                {state.error}
              </div>
            )}

            <div className="inputs">
              <div className="line">
                <CustomFloatInput
                  name="IDNO"
                  updateValue={updateValue}
                  value={state.IDNO}
                  placeholder="תעודת זהות"
                  cssClass=""
                  validationRules={{ required: true }}
                  typeInput="number"
                  checkInputs={state.checkInputs}
                  checked={() => setState(prev => ({ ...prev, checkInputs: false }))}
                />
              </div>

              <div className="line">
                <CustomFloatInput
                  name="Password"
                  updateValue={updateValue}
                  value={state.Password}
                  placeholder="סיסמה"
                  cssClass=""
                  validationRules={{ required: true }}
                  typeInput="password"
                  checkInputs={state.checkInputs}
                  checked={() => setState(prev => ({ ...prev, checkInputs: false }))}
                />
              </div>
            </div>

            <div className="text-center">
              <Button
                onClick={() => sendForm(sendRules)}
                size="lg"
                variant={sendRules ? "success" : "secondary"}
              >
                שליחה
              </Button>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default LoginPage;
