/**
 * Device Detection Utilities
 * מכיל פונקציות לזיהוי סוג המכשיר והסביבה
 */

/**
 * בדיקה אם האפליקציה רצה ב-iOS WebView
 * @returns {boolean} true אם זה iOS WebView
 */
export const isIOSWebView = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isIOS = userAgent.includes('iphone') || userAgent.includes('ipad');
  const isWebView = userAgent.includes('wkwebview') || typeof window.ReactNativeWebView !== 'undefined';
  
  return isIOS && isWebView;
};

/**
 * בדיקה אם האפליקציה רצה ב-Android WebView
 * @returns {boolean} true אם זה Android WebView
 */
export const isAndroidWebView = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isAndroid = userAgent.includes('android');
  const isWebView = userAgent.includes('wv') || typeof window.ReactNativeWebView !== 'undefined';
  
  return isAndroid && isWebView;
};

/**
 * בדיקה אם האפליקציה רצה ב-React Native WebView (כל פלטפורמה)
 * @returns {boolean} true אם זה React Native WebView
 */
export const isReactNativeWebView = () => {
  return typeof window.ReactNativeWebView !== 'undefined' && window.ReactNativeWebView !== null;
};

/**
 * בדיקה אם האפליקציה רצה במובייל (כל סוג)
 * @returns {boolean} true אם זה מובייל
 */
export const isMobileDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('iphone') || 
         userAgent.includes('ipad') || 
         userAgent.includes('android') ||
         userAgent.includes('mobile');
};

/**
 * קבלת מידע מפורט על המכשיר
 * @returns {object} אובייקט עם מידע על המכשיר
 */
export const getDeviceInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    isIOS: userAgent.includes('iphone') || userAgent.includes('ipad'),
    isAndroid: userAgent.includes('android'),
    isIOSWebView: isIOSWebView(),
    isAndroidWebView: isAndroidWebView(),
    isReactNativeWebView: isReactNativeWebView(),
    isMobile: isMobileDevice(),
    isWebView: isIOSWebView() || isAndroidWebView(),
    webViewType: isReactNativeWebView() ? 'ReactNativeWebView' : 'browser'
  };
};

/**
 * קבלת timeout מותאם לפלטפורמה
 * @param {number} defaultTimeout - timeout ברירת מחדל
 * @param {number} iosTimeout - timeout מותאם ל-iOS (אופציונלי)
 * @returns {number} timeout מותאם
 */
export const getPlatformTimeout = (defaultTimeout, iosTimeout = null) => {
  if (isIOSWebView()) {
    return iosTimeout || Math.min(defaultTimeout, 5000); // מקסימום 5 שניות ל-iOS
  }
  return defaultTimeout;
};

/**
 * שליחת הודעה ל-React Native (אם קיים)
 * @param {object} message - ההודעה לשליחה
 */
export const postMessageToReactNative = (message) => {
  try {
    const messageString = typeof message === 'string' ? message : JSON.stringify(message);
    
    if (window.ReactNativeWebView && typeof window.ReactNativeWebView.postMessage === 'function') {
      window.ReactNativeWebView.postMessage(messageString);
      console.log('Message sent to ReactNativeWebView:', messageString);
    } else {
      window.postMessage(messageString, '*');
      console.log('Message sent to window:', messageString);
    }
  } catch (error) {
    console.error('Error sending postMessage:', error);
  }
};

/**
 * לוג מותאם לפלטפורמה
 * @param {string} message - ההודעה
 * @param {any} data - נתונים נוספים
 */
export const platformLog = (message, data = null) => {
  const deviceInfo = getDeviceInfo();
  const prefix = deviceInfo.isIOSWebView ? '[iOS WebView]' : 
                 deviceInfo.isAndroidWebView ? '[Android WebView]' : 
                 deviceInfo.isReactNativeWebView ? '[React Native WebView]' : 
                 '[Browser]';
  
  if (data) {
    console.log(`${prefix} ${message}`, data);
  } else {
    console.log(`${prefix} ${message}`);
  }
};

export default {
  isIOSWebView,
  isAndroidWebView,
  isReactNativeWebView,
  isMobileDevice,
  getDeviceInfo,
  getPlatformTimeout,
  postMessageToReactNative,
  platformLog
};
