/**
 * SickLeaveForm - קומפוננט ייעודי לטיפול באישורי מחלה
 *
 * מאפשר העלאת אישורי מחלה עם בחירת תאריכי התחלה וסיום
 * מחשב אוטומטית את מספר ימי המחלה בהתאם לתאריכים שנבחרו
 * כאשר סטטוס המסמך הוא "Exist", מציג הודעה ולא מאפשר העלאה חדשה
 *
 * Props:
 * - formData: נתוני הטופס
 * - userJ: נתוני המשתמש
 */
import React, { useState, useEffect, useRef } from "react";
import {
  Backdrop,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { checkDatesAPI, sendFileFormToClientApi } from "./fileFunctions";
import { formatDate as formatDateUtil } from "./dateUtils";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CloseIcon from "@mui/icons-material/Close";
import UploadFile from "./UploadFile";
import { Button } from "../../../Components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "../../../Components/ui/card";
import { Badge } from "../../../Components/ui/badge";
import { Input } from "../../../Components/ui/input";
import { Label } from "../../../Components/ui/label";
import {
  Alert,
  AlertTitle,
  AlertDescription,
} from "../../../Components/ui/alert";
import IconButton from "@mui/material/IconButton";

// Add custom styles for date inputs
const dateInputStyles = `
    /* Customize date input fields to show proper placeholder */
    input[type="date"] {
        position: relative;
    }
    
    /* Hide the default calendar icon in Webkit browsers */
    input[type="date"]::-webkit-calendar-picker-indicator {
        background: transparent;
        color: transparent;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        cursor: pointer;
    }
    
    /* Always show our custom placeholder */
    input[type="date"].date-input-placeholder::placeholder {
        color: #6c757d;
        visibility: visible !important;
    }
`;

// Convert YYYY-MM-DD to DD/MM/YYYY for display
const toDisplayFormat = (isoDate) => {
  if (!isoDate) return "";
  const [year, month, day] = isoDate.split("-");
  return `${day}/${month}/${year}`;
};

// Convert DD/MM/YYYY to YYYY-MM-DD for API
const toApiFormat = (displayDate) => {
  if (!displayDate) return "";
  // If already in YYYY-MM-DD format, return as is
  if (/^\d{4}-\d{2}-\d{2}$/.test(displayDate)) return displayDate;

  const [day, month, year] = displayDate.split("/");
  return `${year}-${month}-${day}`;
};

const formatDate = (dateString) => {
  if (!dateString) return "";
  return formatDateUtil(dateString);
};

const StatusBadge = ({ status }) => {
  const getStatusText = () => {
    if (status === "Missing") return "חסר";
    if (status === "Pending") return "ממתין לאישור";
    if (status === "Exist") return "קיים";
    return "";
  };

  const getStatusIcon = () => {
    if (status === "Missing")
      return <WarningAmberIcon className="h-4 w-4 mr-1" />;
    if (status === "Pending")
      return <ErrorOutlineIcon className="h-4 w-4 mr-1" />;
    if (status === "Exist")
      return <CheckCircleOutlineIcon className="h-4 w-4 mr-1" />;
    return null;
  };

  const getVariant = () => {
    if (status === "Missing") return "warning";
    if (status === "Pending") return "outline";
    if (status === "Exist") return "success";
    return "default";
  };

  return (
    <Badge variant={getVariant()} className="font-medium">
      <span className="flex items-center">
        {getStatusIcon()}
        {getStatusText()}
      </span>
    </Badge>
  );
};

// Custom date input that displays and handles DD/MM/YYYY format
const DateInput = ({ id, label, value, displayValue, onChange, disabled }) => {
  // Handle raw input from user
  const handleChange = (e) => {
    const inputValue = e.target.value; // YYYY-MM-DD format
    const displayValue = toDisplayFormat(inputValue);
    onChange(inputValue, displayValue);
  };

  // Create a ref to handle the native date input
  const dateInputRef = useRef(null);

  // Add explicit focus handler that works on both mobile and desktop
  const handleFocus = () => {
    if (!disabled && dateInputRef.current) {
      // פתרון משופר לדסקטופ ומובייל
      if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
        // מובייל - שימוש בקליק מפורש ופוקוס
        dateInputRef.current.click();
        dateInputRef.current.focus();
      } else {
        // דסקטופ - שימוש ב-showPicker (אם זמין) או נפילה חזרה לפוקוס רגיל
        if (typeof dateInputRef.current.showPicker === "function") {
          dateInputRef.current.showPicker();
        } else {
          dateInputRef.current.focus();
        }
      }
    }
  };

  return (
    <div className="space-y-2">
      {label && (
        <Label htmlFor={id} className="block text-sm font-medium">
          {label}
        </Label>
      )}
      <div className="relative date-input-container">
        {/* Native date input - improved for both mobile and desktop */}
        <input
          ref={dateInputRef}
          type="date"
          id={id}
          name={id}
          className="opacity-0 absolute inset-0 w-full h-full cursor-pointer z-10"
          value={value} // YYYY-MM-DD format
          onChange={handleChange}
          disabled={disabled}
          style={{
            fontSize: "16px", // Prevents iOS zoom on focus
            WebkitAppearance: "none",
          }}
          // טריגר ישיר להתמודדות עם דפדפנים שונים
          onClick={(e) => e.stopPropagation()}
        />

        {/* Custom visible input - improved for both platforms */}
        <div
          className={`w-full border rounded-md px-3 py-2 flex items-center ${
            disabled ? "bg-gray-100 opacity-50" : "bg-white"
          }`}
          onClick={handleFocus}
          tabIndex={disabled ? -1 : 0}
          onKeyDown={(e) =>
            (e.key === "Enter" || e.key === " ") && handleFocus()
          }
          role="button"
          aria-label={`בחר ${label || "תאריך"}`}
          style={{ minHeight: "44px" }}
        >
          <span
            className={`flex-grow ${
              !displayValue ? "text-gray-500" : "text-black"
            }`}
          >
            {displayValue || "DD/MM/YYYY"}
          </span>
          <CalendarMonthIcon className="h-5 w-5 text-gray-400" />
        </div>
      </div>

      {displayValue && (
        <div className="text-xs text-gray-500 pt-1">תאריך: {displayValue}</div>
      )}
    </div>
  );
};

export default function SickLeaveForm({
  formData,
  texts,
  userJ,
  title,
  ...props
}) {
  const [showDateModal, setShowDateModal] = useState(false);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [datesInput, setDatesInput] = useState({
    StartDate: "",
    EndDate: "",
    StartDateDisplay: "",
    EndDateDisplay: "",
  });
  const [datesOk, setDatesOk] = useState(false);
  const processIdRef = useRef(Date.now());
  const operationInProgressRef = useRef(false);
  const [autoOpenFilePicker, setAutoOpenFilePicker] = useState(false);
  const fileInputRef = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);

  const displayTitle = title || texts?.title || "אישורי מחלה";

  useEffect(() => {
    return () => {
      setShowDateModal(false);
      setShowUploadForm(false);
      setDatesOk(false);
      operationInProgressRef.current = false;
    };
  }, [formData.FormID]);

  const handleUploadNewFile = () => {
    if (
      operationInProgressRef.current ||
      loading ||
      showDateModal ||
      showUploadForm
    ) {
      return;
    }

    operationInProgressRef.current = true;
    processIdRef.current = Date.now();

    setDatesInput({
      StartDate: "",
      EndDate: "",
      StartDateDisplay: "",
      EndDateDisplay: "",
    });
    setDatesOk(false);
    setShowUploadForm(false);

    setTimeout(() => {
      setShowDateModal(true);
      operationInProgressRef.current = false;
    }, 50);
  };

  const checkDates = () => {
    if (loading || operationInProgressRef.current) {
      return;
    }

    operationInProgressRef.current = true;
    setLoading(true);

    // Format dates to match the required backend format (YYYY-MM-DD)
    const formattedDates = {
      StartDate: datesInput.StartDate,
      EndDate: datesInput.EndDate,
    };

    checkDatesAPI(
      formattedDates,
      (result) => {
        setDatesOk(result);
        operationInProgressRef.current = false;
        setLoading(false);
      },
      (loadingState) => {
        setLoading(loadingState);
      }
    );
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // אחסן את הקובץ שנבחר
      setSelectedFile(file);

      // סגור את חלונית בחירת התאריכים
      setShowDateModal(false);

      // העבר למסך העלאת קובץ עם הקובץ הנבחר
      setTimeout(() => {
        // יצירת אובייקט FormData עם הקובץ והתאריכים
        const fileFormData = { ...formData };
        fileFormData.file = file; // הוסף את הקובץ לנתוני הטופס
        fileFormData.fileName = file.name;

        // עדכן את נתוני הטופס עם הקובץ החדש
        setShowUploadForm(true);

        // אם צריך להעביר את הקובץ לקומפוננט UploadFile
        // נעביר אותו דרך ה-props

        // אפס את דגל ההתקדמות
        operationInProgressRef.current = false;
      }, 300);
    }
  };

  const handleDateSelected = () => {
    if (!datesOk?.ok || loading || operationInProgressRef.current) {
      return;
    }

    operationInProgressRef.current = true;

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    if (isMobile) {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    } else {
      setShowDateModal(false);
      setTimeout(() => {
        setShowUploadForm(true);
        operationInProgressRef.current = false;
      }, 300);
    }
  };

  const handleCancelUpload = () => {
    if (operationInProgressRef.current) {
      return;
    }

    operationInProgressRef.current = true;
    setAutoOpenFilePicker(false); // איפוס הדגל

    setShowUploadForm(false);
    setDatesOk(false);
    setDatesInput({
      StartDate: "",
      EndDate: "",
      StartDateDisplay: "",
      EndDateDisplay: "",
    });

    setTimeout(() => {
      operationInProgressRef.current = false;
    }, 100);
  };

  const handleUploadSuccess = () => {
    operationInProgressRef.current = false;
    setShowUploadForm(false);
    setAutoOpenFilePicker(false); // איפוס הדגל

    setTimeout(() => {
      setDatesOk(false);
      setDatesInput({
        StartDate: "",
        EndDate: "",
        StartDateDisplay: "",
        EndDateDisplay: "",
      });

      if (!operationInProgressRef.current) {
        operationInProgressRef.current = true;
        if (props.refreshForms) {
          props.refreshForms();
        }
        setTimeout(() => {
          operationInProgressRef.current = false;
        }, 100);
      }
    }, 500);
  };

  const getMessage = () => {
    if (formData.Status === "Pending") {
      return formData.Msg || texts?.pendingText || "המסמך ממתין לאישור,]";
    } else if (formData.Status === "Exist") {
      return (
        formData.Msg ||
        texts?.existText ||
        "המסמך אושר, ניתן להעלות מסמך חדש במידת הצורך"
      );
    } else {
      return formData.Msg || texts?.startText || "המסמך לא קיים";
    }
  };

  const renderContent = () => {
    if (showUploadForm && datesOk?.ok) {
      // העבר את הקובץ הנבחר לקומפוננט UploadFile
      return (
        <UploadFile
          {...props}
          userJ={userJ}
          formData={{
            ...formData,
            file: selectedFile, // העבר את הקובץ שנבחר
            fileName: selectedFile?.name || "", // העבר גם את שם הקובץ
          }}
          texts={texts}
          customTitle={displayTitle}
          countDaysFirst={true}
          datesInput={{
            StartDate: datesInput.StartDate,
            EndDate: datesInput.EndDate,
            StartDateDisplay: datesInput.StartDateDisplay,
            EndDateDisplay: datesInput.EndDateDisplay,
          }}
          datesOk={datesOk}
          onCancel={handleCancelUpload}
          onUploadSuccess={handleUploadSuccess}
          processId={processIdRef.current}
          key={`upload-${processIdRef.current}`}
          refreshForms={props.refreshForms}
          autoOpenFilePicker={false} // אין צורך בפתיחה אוטומטית כי כבר בחרנו קובץ
          initialSelectedFile={selectedFile} // העבר את הקובץ שנבחר כפרמטר נפרד
          onFilePickerOpened={() => setAutoOpenFilePicker(false)}
        />
      );
    }

    return (
      <CardContent className="p-4">
        <div className="mb-4 space-y-2">
          <div className="flex items-center space-x-2 space-x-reverse">
            <StatusBadge status={formData.Status} />
            <p className="text-sm text-gray-600">{getMessage()}</p>
          </div>
        </div>

        <Button
          onClick={handleUploadNewFile}
          disabled={operationInProgressRef.current || loading || showUploadForm}
          variant="default"
          className="w-full"
        >
          <span>העלאת אישור מחלה חדש</span>
          <CloudUploadIcon className="mr-2 h-4 w-4" />
        </Button>
      </CardContent>
    );
  };

  const calculateDays = () => {
    if (!datesInput.StartDate || !datesInput.EndDate) return 0;

    // Make sure we're using the correct date format for calculation
    let startDate, endDate;

    try {
      // Handle both formats, ensuring we use YYYY-MM-DD for calculation
      startDate = new Date(datesInput.StartDate);
      endDate = new Date(datesInput.EndDate);

      const diffTime = Math.abs(endDate - startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      return diffDays;
    } catch (error) {
      console.error("Error calculating days:", error);
      return 0;
    }
  };

  const handleSubmit = async () => {
    if (datesInput.StartDate && datesInput.EndDate && formData.file) {
      const formData = new FormData();
      formData.append("file", formData.file);
      // Use YYYY-MM-DD format for API
      formData.append(
        "startDate",
        datesInput.StartDate || toApiFormat(datesInput.StartDateDisplay)
      );
      formData.append(
        "endDate",
        datesInput.EndDate || toApiFormat(datesInput.EndDateDisplay)
      );
      formData.append("daysCount", calculateDays());

      await sendFileFormToClientApi(formData);
      setDatesOk({ ok: true, dates: { SikDays: calculateDays() } });
    }
  };

  // Format for display purposes
  const displayStartDate =
    datesInput.StartDateDisplay || toDisplayFormat(datesInput.StartDate);
  const displayEndDate =
    datesInput.EndDateDisplay || toDisplayFormat(datesInput.EndDate);

  return (
    <Card dir="rtl" className="shadow-sm border rounded-lg overflow-hidden">
      <style>{dateInputStyles}</style>
      <CardHeader className="pb-2">
        <div className="flex items-center">
          <CalendarMonthIcon className="h-5 w-5 mr-2 text-primary" />
          <CardTitle className="text-lg font-medium">{displayTitle}</CardTitle>
        </div>
        <CardDescription className="text-sm">
          {texts?.filesType || "(jpg,png,pdf)"}
        </CardDescription>
      </CardHeader>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*,.pdf"
        style={{ display: "none" }}
      />

      {renderContent()}

      <Dialog
        open={showDateModal}
        onClose={() => {
          if (!loading && !operationInProgressRef.current)
            setShowDateModal(false);
        }}
        disableBackdropClick={loading || operationInProgressRef.current}
        disableEscapeKeyDown={loading || operationInProgressRef.current}
        maxWidth="sm"
        fullWidth
        dir="rtl"
      >
        <div className="bg-white rounded-lg max-w-md mx-auto transition-y-10">
          <DialogTitle className="flex items-center justify-between">
            <h2 className="text-xl font-semibold flex items-center">
              <CalendarMonthIcon className="mr-2 h-5 w-5 text-primary" />
              {displayTitle}
            </h2>
            <IconButton
              onClick={() => {
                if (!loading && !operationInProgressRef.current)
                  setShowDateModal(false);
              }}
              disabled={loading || operationInProgressRef.current}
              size="small"
              aria-label="close"
            >
              <CloseIcon className="h-4 w-4" />
            </IconButton>
          </DialogTitle>

          <DialogContent>
            {!datesOk?.ok && (
              <div className="space-y-4 mb-6">
                <p className="text-sm">
                  אנא בחר את התאריכים המופיעים באישור המחלה שלך:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <DateInput
                    id="startDate"
                    label="תאריך התחלה"
                    value={datesInput.StartDate}
                    displayValue={datesInput.StartDateDisplay}
                    onChange={(value, displayValue) => {
                      setDatesInput({
                        ...datesInput,
                        StartDate: value,
                        StartDateDisplay: displayValue,
                      });
                    }}
                    disabled={loading || operationInProgressRef.current}
                  />

                  <DateInput
                    id="endDate"
                    label="תאריך סיום"
                    value={datesInput.EndDate}
                    displayValue={datesInput.EndDateDisplay}
                    onChange={(value, displayValue) => {
                      setDatesInput({
                        ...datesInput,
                        EndDate: value,
                        EndDateDisplay: displayValue,
                      });
                    }}
                    disabled={loading || operationInProgressRef.current}
                  />
                </div>
              </div>
            )}

            {loading && (
              <Backdrop open={loading}>
                <CircularProgress color="primary" />
              </Backdrop>
            )}

            <div className="space-y-4">
              {datesOk?.error && (
                <Alert variant="destructive">
                  <ErrorOutlineIcon className="h-4 w-4 mr-2" />
                  <AlertDescription>{datesOk.error}</AlertDescription>
                </Alert>
              )}

              {datesOk?.ok && (
                <div className="space-y-3">
                  {datesOk?.warning && (
                    <Alert variant="warning">
                      <WarningAmberIcon className="h-4 w-4 mr-2" />
                      <AlertDescription>{datesOk.warning}</AlertDescription>
                    </Alert>
                  )}

                  <div className="flex items-center justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (!loading && !operationInProgressRef.current)
                          setDatesOk(false);
                      }}
                    >
                      לבחירה חוזרת
                    </Button>
                    <span className="text-sm flex items-center text-green-600">
                      <CheckCircleOutlineIcon className="h-4 w-4 mr-1" />
                      מספר ימים באישור: {datesOk.dates.SikDays}
                    </span>
                  </div>

                  <div className="text-sm text-gray-600 text-right">
                    <div>תאריך התחלה: {datesInput.StartDateDisplay}</div>
                    <div>תאריך סיום: {datesInput.EndDateDisplay}</div>
                  </div>
                </div>
              )}
            </div>
          </DialogContent>

          <DialogActions className="flex space-x-2 space-x-reverse justify-end p-4">
            {datesInput.StartDate && datesInput.EndDate && datesOk?.ok ? (
              <Button
                onClick={handleDateSelected}
                disabled={
                  loading || !datesOk?.ok || operationInProgressRef.current
                }
                variant="default"
              >
                אישור והמשך להעלאת הקובץ
              </Button>
            ) : (
              <Button
                onClick={checkDates}
                disabled={
                  loading ||
                  !datesInput.StartDate ||
                  !datesInput.EndDate ||
                  operationInProgressRef.current
                }
                variant="default"
              >
                בדיקת תאריכים
              </Button>
            )}

            <Button
              onClick={() => {
                if (!loading && !operationInProgressRef.current)
                  setShowDateModal(false);
              }}
              variant="outline"
              disabled={loading || operationInProgressRef.current}
            >
              ביטול
            </Button>
          </DialogActions>
        </div>
      </Dialog>
    </Card>
  );
}
