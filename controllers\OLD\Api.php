<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Api extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
        
        // error reporting (this is a demo, after all!)
        ini_set('display_errors',1);
        error_reporting(E_ALL);

        $this->load->library('oauth2');
        
    }
    
    public function login() {
        if($this->input->post()) {
            $result = $this->oauth2->handleTokenRequest();
            print_r($result);
        }
        echo '<form method="post">
            <label>Login</label><br />
            <input type="text" name="username" value="">
            <input type="password" name="password" value="">
            <button type="submit">login</button>
          </form>';
        
    }
    
    public function index() {
        //echo date("Y-m-d H:i:s"); die();
        $data = array(
            'client_id' => 'testclient',
            'client_secret' => 'testpass',
            'grant_type' => 'client_credentials'
        );
        $endpoint = base_url('api/token');
        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_HEADER, TRUE);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json')); 
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        
        $result = curl_exec($ch);
        
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // evaluate for success response
        if ($status != 200) {
            throw new Exception("Error: call to URL $endpoint failed with status $status, response " . $result . ", curl_error " . curl_error($ch) . ", curl_errno " . curl_errno($ch) . "\n");
        }
        
        curl_close($ch);

    }
    
    public function token() {
        $result = $this->oauth2->handleTokenRequest();
        return $this->output->set_status_header('200')->set_content_type('application/json')->set_output(json_encode($result));
    }
    
    public function resource() {
        $result = $this->oauth2->verifyResourceRequest();
        return $this->output->set_status_header('200')->set_content_type('application/json')->set_output(json_encode($result));
    }
    
    public function create_client() {
        $data = json_decode($this->input->raw_input_stream, TRUE);
        $result = $this->oauth2->create_client($data['client_id'], $data['client_secret'], $data['redirect_uri']);
        return $this->output->set_status_header('200')->set_content_type('application/json')->set_output(json_encode($result));
    }
    
    public function create_user() {
        $data = json_decode($this->input->raw_input_stream, TRUE);
        $result = $this->oauth2->create_user($data['username'], $data['password'], $data['firstname'], $data['lastname']);
        return $this->output->set_status_header('200')->set_content_type('application/json')->set_output(json_encode($result));
    }
}
