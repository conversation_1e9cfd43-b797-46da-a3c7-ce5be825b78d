<div class="panel panel-default">
    <!-- Default panel contents -->
    <div class="panel-heading">
        שאלונים
        <small>
            <?php showInfo($total, $limit, $page); ?>
        </small>
        <div style="clear: both;"></div>
    </div>
    <!-- Table -->
    <table class="table table-hover">
        <thead>
            <tr>
                <th><?php showOrder('id', '#'); ?></th>
                <th><?php showOrder('created_at', 'תאריך פתיחה'); ?></th>
                <th><?php showOrder('name', 'שם'); ?></th>
                <th><?php showOrder('status', 'סטטוס'); ?></th>
                <th>ערוך</th>
                <th>מחק שאלון</th>
            </tr>
        </thead>
        <tbody>
            <?php if(isset($objects) && !empty($objects)) { foreach($objects as $row) { ?>
                <tr id="row<?php echo $row->Id(); ?>" role="row">
                    <td>
                        <?php echo $row->Arg('id'); ?>
                    </td>
                    <td>
                        <?php echo $row->Datetime('created_at'); ?>
                    </td>
                    <td>
                        <?php echo $row->Arg('name'); ?>
                    </td>
                    <td>
                        <?php 
                            if($row->Arg('status') == 'נפתח תיק') {$class = 'info';}
                            else if($row->Arg('status') == 'לא רלוונטי') {$class = 'danger';}
                            else if($row->Arg('status') == 'לקוח חושב על זה') {$class = 'warning';}
                            else if($row->Arg('status') == 'רלוונטי') {$class = 'success';}
                            else $class = 'default';
                        ?>
                        <?php echo form_open(current_url() . '?change_status_id=' .  $row->Id(), array('onChange' => 'submit()') ); ?>
                            <select class="form-control btn-<?php echo $class;?>" name="status">
                                <option selected="" value="<?php echo $row->Arg('status'); ?>"><?php echo $row->Arg('status'); ?></option>
                                <option value="רלוונטי">רלוונטי</option>
                                <option value="לא רלוונטי">לא רלוונטי</option>
                                <option value="לקוח חושב על זה">לקוח חושב על זה</option>
                                <option value="נפתח תיק">נפתח תיק</option>
                            </select>
                        <?php echo form_close(); ?>
                    </td>
                    <td>
                        <a class="btn btn-default" href="<?php echo base_url('שאלון_לקוח?id='.$row->Arg('id_questions')); ?>" target="_blank">צפייה / עריכה</a>
                    </td>
                    <td>
                        <a class="btn btn-xs btn-danger" onclick="return confirm('בטוח שרוצה למחוק את כל השאלון?')" href="<?php echo current_url().'?dellete='.$row->Arg('id_questions'); ?>" target="_self">מחק שאלון</a>
                    </td>
                </tr>
            <?php }} ?>
        </tbody>
    </table>
    <div class="row">
        <div class="col-md-12 text-center">
            <?php showPages($total, $limit, $page); ?>
        </div>
    </div>

</div>




