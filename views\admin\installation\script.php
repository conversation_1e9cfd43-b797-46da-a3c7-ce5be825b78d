<script>
    function remove_migration_field(obj) {
        var $row = $(obj).closest(".migration-field");
        $row.fadeOut(function(){
            $row.remove();
        });
    }
    
    $(document).ready(function(){
        
        $(document).on("change", "select[data-field-type]", function() {
            var $select = $(this);
            var type = $(this).find("option:selected").val();
            //$(this).attr("data-field-type", $(this).find("option:selected").val());
            $select.closest(".migration-field").find(".options").fadeOut(function(){
                $select.closest(".migration-field").find(".options." + type).fadeIn();
            });
        });
        
        $(document).on("click", "*[data-build]", function() {
            var url = $(this).attr("data-build");
            $.get(url, function(response) {
                console.log(response);
            });
        });

        $(document).on("submit", "form.ajax-installation", function(e) {
            e.preventDefault();

            $(this).ajaxSubmit({
                beforeSubmit: function(formData, jqForm, options) {
                    console.log(formData);
                    /* check validate */
                    /* return Boolean */
                },
                success: function(responseText, statusText, xhr, $form) {
                    console.log(responseText);
                    if(typeof responseText.load !== 'undefined') {
                        $("#migrationWrapper").load(responseText.load, function(){
                            startComponents();
                        });
                    }
                    
                },
                error: function(response){
                    console.log(response);
                }
            });

            return false;
        });
    });
</script>