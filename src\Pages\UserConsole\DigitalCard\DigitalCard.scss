.digital-card-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  direction: rtl;

  .card-content {
    margin-top: 20px;
  }

  .card-paper {
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 12px;
  }

  .card-preview-container {
    width: 100%;
    max-width: 600px;
    margin: 20px auto;
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: #f8f9fa;
  }

  .card-preview-image {
    max-width: 100%;
    max-height: 500px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    }
  }

  .card-info-container {
    width: 100%;
    max-width: 600px;
    margin: 20px auto;
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .MuiTypography-root {
      margin-bottom: 12px;
    }
  }

  .card-image-container {
    width: 100%;
    max-width: 500px;
    margin-bottom: 20px;
    text-align: center;
  }

  .card-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .card-details {
    width: 100%;
    text-align: center;
    margin-bottom: 20px;

    h2 {
      margin-bottom: 16px;
    }

    p {
      margin-bottom: 8px;
    }
  }

  .error-container {
    padding: 16px;
    margin-top: 20px;
    border-radius: 8px;
  }
}

@media (max-width: 600px) {
  .digital-card-container {
    padding: 16px;

    .card-paper {
      padding: 16px;
    }

    .card-preview-container {
      padding: 16px;
      margin: 16px auto;
    }

    .card-preview-image {
      max-height: 400px;
    }

    .card-info-container {
      padding: 30px 16px;
      margin: 16px auto;
    }
  }
}