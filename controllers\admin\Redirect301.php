<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Redirect301 extends CI_Controller {
    
    private $table = 'redirect301';
    
    public function __construct() {
        parent::__construct();
        //if is not logged in die  
        if( ! $this->aauth->is_loggedin() ) { 
            redirect(base_url('admin?redirect=' . current_url() . getQS()), 'refresh', 401);
            die('The user is not connected.'); 
        } else if( ! $this->aauth->is_allowed($this->router->fetch_class() . '_' . $this->router->fetch_method())) {
            die('The user not have permission to view the content.'); 
        }
        $this->load->model('msite');
    }
    
    public function index() {
        //show page of all objects
        $data['q'] = $this->input->get('q') ? $this->input->get('q') : '';
        $data['page'] = $this->input->get('page') ? $this->input->get('page') : 1;
        $data['limit'] = $this->input->get('limit') ? $this->input->get('limit') : 3;
        $data['order'] = $this->input->get('order') ? $this->input->get('order') : 'created_at';
        $data['sort'] = $this->input->get('sort') ? $this->input->get('sort') : 'DESC';
        $data['from_date'] = $this->input->get('from_date') ? changeDateFormat($this->input->get('from_date')) : FALSE;
        $data['to_date'] = $this->input->get('to_date') ? changeDateFormat($this->input->get('to_date')) : FALSE;

        
        if($data['from_date']) {$this->msite->set_where("created_at >= '".$data['from_date']."'");}
        if($data['to_date']) {$this->msite->set_where("created_at <= '".$data['to_date']."'");}
        $data['total'] = $this->msite->count_all_objects($this->table);
        if($data['from_date']) {$this->msite->set_where("created_at >= '".$data['from_date']."'");}
        if($data['to_date']) {$this->msite->set_where("created_at <= '".$data['to_date']."'");}
        $this->msite->limit_objects($data['page'], $data['limit']);
        $this->msite->sort_objects($data['order'], $data['sort']);
        $data['objects'] = $this->msite->get_all_objects($this->table);
        
        
        $data['view'] = $this->router->fetch_class() . '/index';
        $this->load->view('admin/index', $data);
    }
    
    public function put() {
        $data = array(
            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($this->table, 'sort') + 10,
            'status' => $this->input->post('status') && $this->input->post('status') > 0 ? 1 : 0,
            'created_at' => date("Y-m-d H:i:s"),
            'from' => urldecode($this->input->post('from')),
            'to' => urldecode($this->input->post('to'))
        );
        
        if($insert_id = $this->msite->insert_object($this->table, $data)) {
            $data['id'] = $insert_id;
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
        
    }
    
    public function update($update_id) {
        //update the object
        $data = array(
            'from' => urldecode($this->input->post('from')),
            'to' => urldecode($this->input->post('to'))
        );
        
        if($this->msite->update_object($this->table, $update_id, $data)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function status($update_id) {
        $obj = $this->msite->get_object($this->table, $update_id);
        $status = $obj->Arg('status') > 0 ? 0 : 1;
        if($this->msite->update_object($this->table, $update_id, array("status" => $status))) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function destroy($delete_id) {
        if($this->msite->delete_object($this->table, $delete_id)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
}