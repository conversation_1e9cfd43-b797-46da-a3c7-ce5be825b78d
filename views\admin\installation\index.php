
<div class="row">
    <div class="col-lg-12 col-sm-12">
        <div class="dropdown">
            <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenuMigration" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                <?php echo isset($migration['explain']) ? "עדכון אובייקט " .'"'. $migration['explain'] .'"' : "הוסף אובייקט חדש"; ?>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuMigration">
                <li class="<?php echo ! $migration ? "active" : ""; ?>"><a href="<?php echo base_url('admin/installation/index'); ?>">הוספת אובייקט חדש</a></li>
                <?php if($migrations)foreach($migrations as $row) { ?>
                <li class="<?php echo $migration_file && $migration_file === $row ? "active" : ""; ?>"><a href="<?php echo base_url('admin/installation/index/' . $row); ?>"><?php echo $row; ?></a></li>
                <?php } ?>
            </ul>
        </div>
        <hr/>

<!--        <div class="list-group">
            <a href="<?php echo base_url('admin/installation/index'); ?>" class="list-group-item <?php echo ! $migration ? "active" : ""; ?>">הוספת אובייקט חדש</a>
            <?php if($migrations)foreach($migrations as $row) { ?>
            <a href="<?php echo base_url('admin/installation/index/' . $row); ?>" class="list-group-item <?php echo $migration_file && $migration_file === $row ? "active" : ""; ?>"><?php echo $row; ?></a>
            <?php } ?>
        </div>-->
    </div>
    <div class="col-lg-12 col-sm-12">
        <div class="row">
            <div id="migrationWrapper" class="col col-lg-12">
                <?php if(file_exists(VIEWPATH . 'admin/installation/migration.php')) { $this->load->view('admin/installation/migration'); } ?>
            </div>
        </div>
    </div>
</div>





