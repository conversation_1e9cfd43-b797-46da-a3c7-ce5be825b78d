<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Seo extends CI_Controller {
    
    private $table = 'seo';
    
    public function __construct() {
        parent::__construct();
        //if is not logged in die  
        if( ! $this->aauth->is_loggedin() ) { 
            redirect(base_url('admin?redirect=' . current_url() . getQS()), 'refresh', 401);
            die('The user is not connected.'); 
        } else if( ! $this->aauth->is_allowed($this->router->fetch_class() . '_' . $this->router->fetch_method())) {
            die('The user not have permission to view the content.'); 
        }
        $this->load->model('msite');
    }
    
    public function index() {
        //show page of all objects
        $data['q'] = $this->input->get('q') ? $this->input->get('q') : '';
        $data['page'] = $this->input->get('page') ? $this->input->get('page') : 1;
        $data['limit'] = $this->input->get('limit') ? $this->input->get('limit') : 100;
        $data['order'] = $this->input->get('order') ? $this->input->get('order') : 'id';
        $data['sort'] = $this->input->get('sort') ? $this->input->get('sort') : 'DESC';
        
        $data['total'] = $this->msite->count_all_objects($this->table);
        $this->msite->limit_objects($data['page'], $data['limit']);
        $this->msite->sort_objects($data['order'], $data['sort']);
        $data['objects'] = $this->msite->get_all_objects($this->table);
        
        
        $data['view'] = $this->router->fetch_class() . '/index';
        $this->load->view('admin/index', $data);
    }
    
    public function show($obj_id) {
        //show page of all objects
        $data['seo'] = $this->msite->get_seo($obj_id);
        
        $this->load->view('admin/' . $this->router->fetch_class() . '/object', $data);
    }
    
    public function update_map() {
        if($this->msite->update_site_map()) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function update($update_id) {
        //update the object
        $data = array(
            'title' => $this->input->post('title'),
            'description' => $this->input->post('description'),
            'image' => $this->input->post('image'),
            'canonical' => urldecode($this->input->post('canonical')),
            'friendly' => $this->input->post('friendly'),
            'robots' => $this->input->post('robots'),
        );
        
        if($langs = $this->config->item('available_lang')) {
            $field_name = $field['name'];
            foreach ($langs as $lang_key => $lang_value) {
                $data_seo[$lang_key . '_title'] = $this->input->post($lang_key . '_title');
                $data_seo[$lang_key . '_description'] = strip_tags($this->input->post($lang_key . '_description'));
                $data_seo[$lang_key . '_friendly'] = url_title($this->input->post($lang_key . '_friendly'));
            }
        }
        
        if($this->msite->update_object($this->table, $update_id, $data)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function status($update_id) {
        $obj = $this->msite->get_object($this->table, $update_id);
        $status = $obj->Arg('status') > 0 ? 0 : 1;
        if($this->msite->update_object($this->table, $update_id, array("status" => $status))) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function destroy($delete_id) {
        if($this->msite->delete_object($this->table, $delete_id)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
}