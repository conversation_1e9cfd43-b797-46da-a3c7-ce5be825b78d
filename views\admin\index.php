<?php
$loginuser = $this->aauth->get_user();
define('IS_DEVELOPER', $loginuser->username === 'developers' ? true : false);
$migrations = directory_map(APPPATH . 'migration/'); 

?>
<!DOCTYPE html>
<html>
    <head>
        <title>WaveProject ADMIN</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        <link type="text/css" rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(CSS . 'bootstrap-rtl.css'); ?>">
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(PLUGINS . 'sweetalert/sweetalert.css'); ?>">
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(PLUGINS . 'datepicker/bootstrap-datetimepicker.min.css'); ?>"/>
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(PLUGINS . 'bootstrap-tagsinput-latest/dist/bootstrap-tagsinput.css'); ?>"/>
        
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(PLUGINS . 'cropper/cropper.css'); ?>"/>
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(PLUGINS . 'select2/css/select2.min.css'); ?>"/>
        
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(PLUGINS . 'bootstrap-colorpickersliders-master/dist/bootstrap.colorpickersliders.min.css'); ?>"/>
        
        <link type="text/css" rel="stylesheet" href="<?php echo base_url(ASSETS . 'css/admin.css?v='.VERSION); ?>"/>
    </head>
    <body>
        <div class="wrapper">
            <nav class="navbar navbar-inverse1 navbar-fixed-top" style="border-bottom: solid 1px #c3c5ce;background-color: white;">
                <div class="container" style="overflow: inherit;">
                    <div class="navbar-header" style="width: 100%;margin: 0;">
                        <button type="button" class="navbar-toggle collapsed pull-right" style="margin: 8px 0;" data-toggle="collapse" data-target="#navbar">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        <a class="navbar-brand" href="<?php echo base_url('admin/site'); ?>" style="padding: 10px;width: 200px;">
                            <img class="img-responsive" src="<?php echo base_url(IMG . 'admin/logo.png'); ?>" alt="logo"/>
                        </a>
                        <a href="<?php echo base_url('admin/welcome/logout'); ?>" class="navbar-brand pull-left" style="padding: 15px 0;" data-toggle="tooltip" data-placement="bottom" title="יציאה ממערכת הניהול">
                            <span class="glyphicon glyphicon-log-out"></span>
                        </a>
                        
                        <div id="navbar" class="collapse navbar-collapse" style="width: 100%; margin: 0;">
                            <ul class="nav navbar-nav">
                                <?php if(IS_DEVELOPER){ ?>
                                <li class="<?php echo $this->router->fetch_class() === 'installation' ? 'active' : ''; ?>">
                                    <a href="<?php echo base_url('admin/installation'); ?>">פיתוח</a>
                                </li>
                                <?php } ?>
<!--                                <li class="<?php //echo $this->router->fetch_class() === 'projects' ? 'active' : ''; ?>">
                                    <a href="<?php //echo base_url('admin/projects'); ?>">פרויקטים</a>
                                </li>-->
                                
                                <li class="<?php echo $this->router->fetch_class() === 'seo' ? 'active' : ''; ?>">
                                    <a href="<?php echo base_url('admin/seo'); ?>">מפת אתר</a>
                                </li>
<!--                                <li class="<?php echo $this->router->fetch_class() === 'redirect301' ? 'active' : ''; ?>">
                                    <a href="<?php echo base_url('admin/redirect301'); ?>">הפניות</a>
                                </li>-->
                                <li class="<?php echo $this->router->fetch_class() === 'site' ? 'active' : ''; ?>">
                                    <a href="<?php echo base_url('admin/site'); ?>">דפי האתר</a>
                                </li>
                                <?php if(IS_DEVELOPER){ ?>
                                <li class="dropdown <?php echo $this->router->fetch_class() === 'obj' ? 'active' : ''; ?>">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        תוכן אתר DEV<span class="caret"></span>
                                    </a>
                                    <ul class="dropdown-menu">
                                        <?php if($migrations)foreach($migrations as $row) { ?>
                                        <li><a href="<?php echo base_url('admin/ObjectAdmin/index/' . str_replace(".json", "", $row)); ?>"><?php echo str_replace(".json", "", $row); ?></a></li>
                                        <?php } ?>
                                    </ul>
                                </li>
                                <?php } ?>
                                
                                <?php //if(IS_DEVELOPER){ ?>
                                <li class="dropdown <?php echo $this->router->fetch_class() === 'obj' ? 'active' : ''; ?>">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                        תוכן אתר <span class="caret"></span>
                                    </a>
                                    <ul class="dropdown-menu">
<!--                                        <li><a href="<?php //echo base_url('admin/ObjectAdmin/index/recommends'); ?>">לקוחות ממליצים</a></li>-->
                                        <li>-----</li>
                                        <li><a href="<?php echo base_url('admin/ObjectAdmin/index/projects'); ?>">פרויקטים</a></li>
<!--                                        <li><a href="<?php echo base_url('admin/ObjectAdmin/index/users'); ?>">אנשים לפרויקט</a></li>-->
                                        
                                        <li><a href="<?php echo base_url('admin/ObjectAdmin/index/SuccessDB'); ?>">סיפורי הצלחה</a></li>
                                        <li><a href="<?php echo base_url('admin/ObjectAdmin/index/questions'); ?>">שאלות נפוצות</a></li>
                                        <li><a href="<?php echo base_url('admin/ObjectAdmin/index/categories'); ?>">קטגוריות </a></li>
                                        
                                        <li><a href="<?php echo base_url('admin/ObjectAdmin/index/galleryProjects'); ?>">גלרית תמונות לפרויקטים</a></li>
                                        
                                        
<!--                                        <li><hr/></li>
                                        <li><a href="<?php //echo base_url('admin/robot/'); ?>">שאלוני INBOX</a></li>-->
                                        
                                </li>
                                <?php //} ?>
                                

                            </ul>

                        </div><!--/.nav-collapse -->
                    </div>
                    
                    
                    
                </div>
            </nav>
            <div id="formProgress" class="progress" style="display: none;position: fixed;top: 50px;left: 0;width: 100%;z-index: 10000;">
                <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" style="width: 45%">
                    <span class="sr-only">45% Complete</span>
                </div>
            </div>

            <div class="container margin-top" style="min-height: 600px; margin-top: 80px;">
                <?php if(isset($view) && file_exists(VIEWPATH . 'admin/' . $view . '.php')) { $this->load->view('admin/' . $view); } ?>
            </div>
            
            <footer class="text-center" style="color: #959a9f;">
                <!--<img style="display: block; margin: 10px auto;max-width: 200px;" class="img-responsive" src="<?php echo base_url(IMG . 'admin/ak-b.png'); ?>" alt="logo"/>-->
                <p>&copy; מערכת ניהול</p>
            </footer>
        </div>
        
        <!-- Modal -->
        <div class="modal fade" id="seoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            
        </div>
        
        
        <script type="text/javascript" src="//code.jquery.com/jquery-2.1.1.min.js"></script>
        <script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
        <!--script type="text/javascript" src="//cdn.ckeditor.com/4.6.2/standard/ckeditor.js"></script>-->
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'ckeditor/ckeditor.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(JS . 'jquery.form.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'cropper/cropper.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'sweetalert/sweetalert.min.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'datepicker/moment-with-locales.min.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'datepicker/bootstrap-datetimepicker.min.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'bootstrap-tagsinput-latest/dist/bootstrap-tagsinput.min.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'select2/js/select2.min.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'bootstrap-colorpickersliders-master/dist/tinycolor.min.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(PLUGINS . 'bootstrap-colorpickersliders-master/dist/bootstrap.colorpickersliders.min.js'); ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(JS . 'admin.js?v='.VERSION); ?>"></script>
        
        
        
        <?php if(isset($script) && file_exists(VIEWPATH . 'admin/' . $script . '.php')) { $this->load->view('admin/' . $script); } ?>
    </body>
</html>
