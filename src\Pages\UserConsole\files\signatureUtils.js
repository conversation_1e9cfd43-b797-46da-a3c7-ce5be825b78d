/**
 * signatureUtils.js - כלים לטיפול בחתימות במסמכים
 *
 * מכיל פונקציות עזר למציאת, הוספת ועדכון חתימות
 */

/**
 * מציאת מיקום מתאים להחתימה במסמך
 * @param {HTMLElement} container - אלמנט המכיל את המסמך
 * @param {string} documentType - סוג המסמך (101, 102, 104)
 * @returns {HTMLElement} האלמנט המתאים להוספת החתימה אליו
 */
export const findSignaturePlacement = (container, documentType) => {
  // טיפול מיוחד לדוח תקבולים
  if (documentType === "104") {
    // חפש את התוכן המרכזי של הדוח
    const mainContent = container.querySelector("table:last-of-type");
    if (mainContent) {
      // החזר את הנקודה בסוף הדוח
      return mainContent.parentNode;
    }
  }

  const keywords = [
    "חתימה",
    "מנהל",
    "אישור",
    'יו"ר',
    "חותמת",
    "חתימת",
    'מנכ"ל האגודה',
    'מנכ"ל',
    "חותמת האגודה",
  ];

  // חיפוש אלמנטים המכילים את מילות המפתח
  const targetNodes = Array.from(
    container.querySelectorAll("div, p, span, td, th")
  ).filter((el) =>
    keywords.some((keyword) => el.textContent?.trim().includes(keyword))
  );

  if (targetNodes.length > 0) {
    // מיון לפי הסבירות להיות שדה חתימה
    targetNodes.sort((a, b) => {
      const aHasSignature = a.textContent.includes("חתימה");
      const bHasSignature = b.textContent.includes("חתימה");
      return bHasSignature - aHasSignature;
    });
    return targetNodes[0];
  }

  // חיפוש תאים ריקים בטבלאות
  const emptyCells = Array.from(container.querySelectorAll("td")).filter(
    (cell) => cell.textContent.trim() === "" && cell.offsetWidth > 100
  );

  if (emptyCells.length > 0) {
    return emptyCells[0];
  }

  // ברירת מחדל - הוסף בסוף המסמך
  return container;
};

/**
 * מצא אזור מתאים לחתימה בדוח תקבולים
 * @param {HTMLElement} container - אלמנט המכיל את המסמך
 * @returns {HTMLElement} האלמנט המתאים להוספת החתימה
 */
export const findSignatureContainerInReceipt = (container) => {
  // חפש אלמנטים עם הטקסט "בברכה והצלחה" או "סמנכ"ל כספים"
  const possibleContainers = Array.from(
    container.querySelectorAll("div, p, span")
  ).filter((el) => {
    const text = el.textContent.trim();
    return (
      text.includes("בברכה והצלחה") ||
      text.includes('סמנכ"ל כספים') ||
      text.includes("ברכה") ||
      text.includes("בכבוד רב")
    );
  });

  if (possibleContainers.length > 0) {
    // בחר את האלמנט הכי סביר שיכיל את החתימה
    return possibleContainers[possibleContainers.length - 1];
  }

  // ברירת מחדל - החזר את הקונטיינר
  return container;
};

/**
 * התאמת החתימה בטופס 101
 * @param {HTMLElement} container - אלמנט המכיל את המסמך
 * @param {Object} signatures - אובייקט המכיל נתיבים לחתימות
 */
export const adjustSignatureForForm101 = (container, signatures) => {
  try {
    console.log("🎯 התאמה מתקדמת של חתימות בטופס 101");

    // מחיקת חתימות קודמות
    const existingSignatures = container.querySelectorAll(
      ".signature-area, .signature-box, img[src*='yaron'], img[src*='aguda']"
    );
    existingSignatures.forEach((sig) => sig.parentNode?.removeChild(sig));

    // פונקציה ליצירת חתימה בלבד
    function createSignatureOnly(imgSrc) {
      const wrapper = document.createElement("div");
      wrapper.style.cssText = `
        width: 100px;
        height: 60px;
        position: relative;
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;
      `;

      const img = document.createElement("img");
      img.src = imgSrc;
      img.alt = "חתימה";
      img.style.cssText = `
        max-width: 100px;
        height: auto;
        opacity: 0.85;
        position: absolute;
        bottom: 0;
        right: -300px;
        top: 0px;
      `;

      wrapper.appendChild(img);
      return wrapper;
    }

    // חיפוש "בכבוד רב"
    const respectNode = Array.from(
      container.querySelectorAll("div, p, span")
    ).find((el) => el.textContent.trim() === "בכבוד רב");

    if (!respectNode || !respectNode.parentNode) {
      console.warn("לא נמצא טקסט 'בכבוד רב'.");
      return;
    }

    // יצירת קונטיינר חתימות
    const signatureRow = document.createElement("div");
    signatureRow.style.cssText = `
      display: flex;
      justify-content: flex-end;
      gap: 100px;
      margin-top: 5px;
      margin-bottom: 10px;
    `;

    // יצירת החתימות
    const agudaSig = createSignatureOnly(signatures.aguda);
    const yaronSig = createSignatureOnly(signatures.yaron);

    signatureRow.appendChild(agudaSig);
    signatureRow.appendChild(yaronSig);

    // הוספת רווח קטן
    const spacer = document.createElement("div");
    spacer.style.height = "10px";

    // הוספה למסמך אחרי "בכבוד רב"
    respectNode.parentNode.insertBefore(spacer, respectNode.nextSibling);
    respectNode.parentNode.insertBefore(signatureRow, spacer.nextSibling);

    console.log("✅ חתימות הוזזו מתחת לבכבוד רב ויושרו לימין");
  } catch (error) {
    console.error("❌ שגיאה במיקום חתימות:", error);
  }
};

/**
 * התאמת החתימה בדוח תקבולים
 * @param {HTMLElement} container - אלמנט המכיל את המסמך
 * @param {HTMLElement} signatureElement - אלמנט החתימה (אם קיים)
 * @param {string} yaronPath - נתיב לחתימת ירון
 */
export const adjustSignatureForReceiptReport = (
  container,
  signatureElement,
  yaronPath
) => {
  // אם זה דוח תקבולים
  try {
    console.log("מתחיל עיבוד התאמת חתימה עבור דוח תקבולים - גרסה מוקטנת");

    // מחק את כל החתימות הקיימות במסמך לפני הוספת חתימה חדשה
    const existingSignatures = container.querySelectorAll(
      ".signature-area, .signature-box, img[src*='yaron'], img[src*='aguda']"
    );
    console.log("מוחק חתימות קיימות:", existingSignatures.length);
    existingSignatures.forEach((sig) => {
      if (sig.parentNode) {
        sig.parentNode.removeChild(sig);
      }
    });

    // בדיקה אם יש לנו מיכל חתימה תקין
    const wrapper = signatureElement.closest(".signature-box");
    if (wrapper && wrapper.parentNode) {
      wrapper.parentNode.removeChild(wrapper);
    }

    // מצא את המיקום המתאים להוספת החתימה וטקסט נלווה
    const targetContainer = findSignatureContainerInReceipt(container);

    // שמור את הטקסט המקורי אם יש
    const originalText = targetContainer.textContent.trim();

    // נקה את האלמנט המקורי
    targetContainer.innerHTML = "";

    // הוסף רווח גדול לפני החתימות
    const upperSpacerDiv = document.createElement("div");
    upperSpacerDiv.style.cssText = `
      height: 40px;
      width: 100%;
      clear: both;
    `;
    targetContainer.appendChild(upperSpacerDiv);

    // צור מיכל חתימה חדש עם יישור ימין
    const signatureWrapper = document.createElement("div");
    signatureWrapper.className = "signature-area";
    signatureWrapper.style.cssText = `
      text-align: right !important;
      width: 100%;
      margin: 15px 0 5px 0;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      position: relative;
      right: 30px;
    `;

    // הוסף את החתימה עצמה - מוקטנת בגרסה זו
    const img = document.createElement("img");
    img.src = yaronPath;
    img.alt = "חתימה";
    // עדכון הסגנון לחתימה קטנה יותר
    img.style.cssText = `
      width: 60px !important; 
      max-width: 60px !important;
      height: auto !important;
      margin: 5px 0;
      opacity: 0.85;
    `;

    // הוספת שכבת ניטור לחתימה
    const imgLoaded = () => {
      console.log(
        "חתימת דוח תקבולים נטענה עם גודל:",
        img.width,
        "x",
        img.height
      );
      // שינוי אילוצי הגודל לאחר טעינה
      setTimeout(() => {
        img.style.width = "60px";
        img.style.maxWidth = "60px";
        console.log("אילוץ שינוי גודל סופי:", img.style.width);
      }, 10);
    };

    img.addEventListener("load", imgLoaded);
    img.onload = imgLoaded;

    signatureWrapper.appendChild(img);

    // הוסף את מיכל החתימה
    targetContainer.appendChild(signatureWrapper);

    // הוסף את הטקסט מחדש מתחת לחתימה עם יישור לימין
    if (originalText) {
      const textDiv = document.createElement("div");
      textDiv.style.cssText = `
        text-align: right !important; 
        width: 100%; 
        margin: 2px 0; 
        direction: rtl;
        font-size: 11pt;
      `;
      textDiv.textContent = originalText;
      targetContainer.appendChild(textDiv);
    }

    // התאם את כל האלמנטים בחלק התחתון של המסמך ליישור ימין
    const bottomElements = Array.from(
      container.querySelectorAll(
        ".signature-area ~ div, .signature-area ~ p, .signature-area ~ span"
      )
    );

    bottomElements.forEach((element) => {
      element.style.textAlign = "right";
      element.style.direction = "rtl";
    });

    console.log("נוספה חתימה חדשה קטנה לדוח תקבולים עם יישור ימין");
  } catch (error) {
    console.error("שגיאה בהתאמת חתימה לדוח תקבולים:", error);
  }
};

/**
 * הוספת חתימות מוכרות למסמך
 * @param {HTMLElement} container - אלמנט המכיל את המסמך
 * @param {string} documentType - סוג המסמך
 * @param {Object} signatures - אובייקט המכיל נתיבים לחתימות ידועות
 */
export const addKnownSignatureImages = async (
  container,
  documentType,
  signatures
) => {
  const modalContent = document.querySelector(".showHtml");
  const hasVisibleSignatures =
    modalContent &&
    modalContent.querySelectorAll(
      "img:not([src*='logo']), [style*='background-image']"
    ).length > 0;

  // רק הוסף חתימות ברירת מחדל אם לא נמצאו חתימות
  if (hasVisibleSignatures) return;

  // רשימת חתימות ברירת מחדל
  const knownSignatures = [
    { url: signatures.yaron, keywords: ["מנהל", 'יו"ר', "ירון"] },
    {
      url: signatures.aguda,
      keywords: ["אגודה", "חותמת", "אישור"],
    },
  ];

  // בחר חתימה מתאימה לפי סוג המסמך
  let signatureToUse = knownSignatures[1].url; // ברירת מחדל: חתימת האגודה

  // בטפסים שונים - בחר חתימה מתאימה
  if (documentType === "101") {
    // טופס 101 - חתימת מנהל
    signatureToUse = knownSignatures[0].url;
  }

  try {
    // צור אלמנט תמונה
    const img = document.createElement("img");
    img.src = signatureToUse;
    img.alt = "חתימה";

    // התאמת גודל החתימה לפי סוג המסמך
    if (documentType === "104") {
      // חתימה קטנה יותר לדוחות תקבולים
      img.style.maxWidth = "60px";
      img.style.width = "60px";
    } else {
      img.style.maxWidth = "160px";
    }

    img.style.height = "auto";
    img.style.display = "block";
    img.style.margin = "10px auto";
    img.style.filter = "contrast(1.4) brightness(0.95)";

    // עטוף במסגרת חתימה
    const wrapper = document.createElement("div");
    wrapper.className = "signature-box";

    // התאמת גודל המסגרת לפי סוג המסמך
    if (documentType === "104") {
      wrapper.style.maxWidth = "80px";
      wrapper.style.margin = "20px 30px 10px 0";
    } else {
      wrapper.style.maxWidth = "180px";
      wrapper.style.margin = "30px 0 10px 30px";
    }

    wrapper.style.border = "1px solid #aaa";
    wrapper.style.padding = "8px";
    wrapper.style.textAlign = "center";
    wrapper.style.pageBreakInside = "avoid";
    wrapper.style.position = "relative";

    if (documentType !== "104") {
      wrapper.style.left = "20px";
    } else {
      wrapper.style.right = "20px";
    }

    wrapper.appendChild(img);

    // מצא מיקום מתאים לחתימה
    let placementNode = findSignaturePlacement(container, documentType);

    // אם מדובר בטופס 101, יש לוודא שהחתימה תהיה הרחק מהכותרת
    if (documentType === "101") {
      // הוסף דיב מרווח לפני החתימה
      const spacerDiv = document.createElement("div");
      spacerDiv.style.height = "40px";
      spacerDiv.style.width = "100%";
      spacerDiv.style.clear = "both";
      placementNode.appendChild(spacerDiv);
    }

    placementNode.appendChild(wrapper);

    // טיפול ספציפי בחתימה עבור דוח תקבולים
    if (documentType === "104") {
      // שימוש בגרסה מוקטנת של החתימה
      const smallerSignature = signatures.yaron;
      adjustSignatureForReceiptReport(container, img, smallerSignature);
    }
  } catch (error) {
    console.error("שגיאה בהוספת חתימה:", error);
  }
};

/**
 * שמירה וטיפול בחתימות נראות במסמך
 * @param {HTMLElement} container - אלמנט המכיל את המסמך
 * @param {string} documentType - סוג המסמך
 * @param {Object} signatures - אובייקט המכיל נתיבים לחתימות ידועות
 */
export const preserveViewSignatures = async (
  container,
  documentType,
  signatures
) => {
  const modalContent = document.querySelector(".showHtml");
  if (!modalContent) return;

  // טיפול מיוחד לדוח תקבולים
  if (documentType === "104") {
    return; // נצא מהפונקציה כדי לתת לטיפול הייעודי לפעול
  }

  const visibleSignatures = modalContent.querySelectorAll(
    "img:not([src*='logo']), [style*='background-image']"
  );

  console.log("חתימות שמופיעות במסך:", visibleSignatures.length);

  // אם יש חתימות נראות במסמך
  if (visibleSignatures.length > 0) {
    // מעקב אחר חתימות שכבר עובדו למניעת כפילויות
    const processedSignatures = new Set();

    for (let i = 0; i < visibleSignatures.length; i++) {
      const signature = visibleSignatures[i];
      try {
        let signatureUrl = "";

        if (signature.tagName === "IMG") {
          signatureUrl = signature.src;
        } else {
          // חלץ URL מתוך סגנון background-image
          const bgStyle = window.getComputedStyle(signature).backgroundImage;
          signatureUrl = bgStyle
            .replace(/^url\(['"]?/, "")
            .replace(/['"]?\)$/, "");
        }

        // דלג אם כבר עיבדנו חתימה זו
        if (processedSignatures.has(signatureUrl)) continue;
        processedSignatures.add(signatureUrl);

        // בחר את החתימה המקומית המתאימה
        let localSignatureUrl = signatures.aguda; // ברירת מחדל

        // בדוק האם ניתן לזהות את סוג החתימה לפי ה-URL
        const lowerUrl = signatureUrl.toLowerCase();
        if (lowerUrl.includes("yaron")) {
          localSignatureUrl = signatures.yaron;
        } else if (lowerUrl.includes("aguda") || lowerUrl.includes("bbbb")) {
          localSignatureUrl = signatures.aguda;
        }

        // צור אלמנט חתימה
        const signatureClone = document.createElement("img");
        signatureClone.src = localSignatureUrl;
        signatureClone.alt = "חתימה";
        signatureClone.style.maxWidth = "160px";
        signatureClone.style.height = "auto";
        signatureClone.style.display = "block";
        signatureClone.style.margin = "10px auto";
        signatureClone.style.filter = "contrast(1.4) brightness(0.95)";

        // צור מסגרת חתימה
        const wrapper = document.createElement("div");
        wrapper.className = "signature-box";
        wrapper.style.border = "1px solid #aaa";
        wrapper.style.padding = "8px";
        wrapper.style.margin = "30px 0 10px 30px";
        wrapper.style.textAlign = "center";
        wrapper.style.maxWidth = "180px";
        wrapper.style.pageBreakInside = "avoid";
        wrapper.style.position = "relative";
        wrapper.style.left = "20px";
        wrapper.appendChild(signatureClone);

        // מצא מיקום מתאים
        let placementNode = findSignaturePlacement(container, documentType);

        // אם מדובר בטופס 101, יש לוודא שהחתימה תהיה הרחק מהכותרת
        if (documentType === "101") {
          // הוסף דיב מרווח לפני החתימה
          const spacerDiv = document.createElement("div");
          spacerDiv.style.height = "40px";
          spacerDiv.style.width = "100%";
          spacerDiv.style.clear = "both";
          placementNode.appendChild(spacerDiv);
        }

        placementNode.appendChild(wrapper);
      } catch (error) {
        console.error("שגיאה בהעתקת חתימה:", error);
      }
    }
  } else {
    // אם אין חתימות נראות, הוסף חתימה ברירת מחדל לפי סוג המסמך
    await addKnownSignatureImages(container, documentType, signatures);
  }
};
