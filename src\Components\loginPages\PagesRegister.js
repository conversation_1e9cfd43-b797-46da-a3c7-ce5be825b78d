import React from "react";
import ContentWrapper from "./ContentWrapper";
import { RestUrls } from "../-Helpers-/config";

const PagesRegister = (props) => {
  const bgDesktop = RestUrls.pagesPictures;

  return props.isMobile ? (
    <div className="body-container PagesRegister Mobile">
      <ContentWrapper {...props} />
    </div>
  ) : (
    <div
      className="body-container PagesRegister Desktop"
    >
      <ContentWrapper {...props} />
    </div>
  );
};

export default PagesRegister;
