<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

defined('BASEPATH') OR exit('No direct script access allowed');

class Installation extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
        //if is not logged in die  
        if( ! $this->aauth->is_loggedin() ) { 
            redirect(base_url('admin?redirect=' . current_url() . getQS()), 'refresh', 401);
            die('The user is not connected.'); 
        } else if( ! $this->aauth->is_allowed($this->router->fetch_class() . '_' . $this->router->fetch_method())) {
            die('The user not have permission to view the content.'); 
        }
        $this->load->helper('file');
    }
    
    public function index($migration = FALSE) {
        $data['migration_file'] = $migration;
        $migration_file = $migration ? read_file(APPPATH . 'migration/'.$migration) : FALSE;
        $data['migration'] = $migration_file ? json_decode($migration_file, TRUE) : FALSE;
        
        $this->load->helper('directory');
        $data['migrations'] = directory_map(APPPATH . 'migration/');
        
        
        $data['view'] = $this->router->fetch_class() . '/index';
        $data['script'] = $this->router->fetch_class() . '/script';
        $this->load->view('admin/index', $data);
    }
    
    public function migration($migration) {
        $data['migration_file'] = $migration;
        $migration_file = $migration ? read_file(APPPATH . 'migration/'.$migration) : FALSE;
        $data['migration'] = $migration_file ? json_decode($migration_file, TRUE) : FALSE;
        
        $this->load->helper('directory');
        $data['migrations'] = directory_map(APPPATH . 'migration/');
        
        $this->load->view('admin/installation/migration', $data);
    }
    
    public function put() {
        $table = $this->input->post("table");
        $explain = $this->input->post("explain");
        $controller = $this->input->post("controller");
        $method = $this->input->post("method");
        
        $seo_title = $this->input->post("seo_title");
        $seo_description = $this->input->post("seo_description");
        
        $title = $this->input->post("title");
        $description = $this->input->post("description");
        $image = $this->input->post("image");
        
        $field_sort = $this->input->post("field_sort");
        $field_width = $this->input->post("field_width");
        
        $field_name = $this->input->post("field_name");
        $field_lang = $this->input->post("field_lang");
        $field_type = $this->input->post("field_type");
        $field_explain = $this->input->post("field_explain");
        $field_option_yes = $this->input->post("field_option_yes");
        $field_option_no = $this->input->post("field_option_no");
        $field_option_width = $this->input->post("field_option_width");
        $field_option_height = $this->input->post("field_option_height");
        $field_option_choices = $this->input->post("field_option_choices");
        $field_option_multiples = $this->input->post("field_option_multiples");
        
        $field_option_table = $this->input->post("field_option_table");
        $field_option_field_text = $this->input->post("field_option_field_text");
        $field_option_field_value = $this->input->post("field_option_field_value");
        
        $migration = array(
            "table" => $table,
            "controller" => $controller,
            "method" => $method,
            "explain" => $explain,
            "title" => $title,
            "description" => $description,
            "seo_title" => $seo_title,
            "seo_description" => $seo_description,
            "image" => $image,
            "fields" => array(),
        );
        
        if(!empty($field_name)) {
            foreach($field_name as $key => $value) {
                if(empty($field_name[$key])) continue;
                
                $field = array(
                    "sort" => $field_sort[$key],
                    "width" => $field_width[$key],
                    "name" => $field_name[$key],
                    "type" => $field_type[$key], 
                    "explain" => $field_explain[$key],
                    "lang" => $field_lang[$key],
                    "options" => array(
                        "yes" => $field_option_yes[$key],
                        "no" => $field_option_no[$key],
                        "width" => $field_option_width[$key],
                        "height" => $field_option_height[$key],
                        "choices" => $field_option_choices[$key],
                        "multiples" => $field_option_multiples[$key],
                        "table" => $field_option_table[$key],
                        "field_value" => $field_option_field_value[$key],
                        "field_text" => $field_option_field_text[$key],
                        
                    ),
                );
                if($field_lang[$key] > 0 && $langs = $this->config->item('available_lang')) {
                    $temp_field_name = $field["name"];
                    $temp_field_explain = $field["explain"];
                    foreach ($langs as $lang_key => $lang_value) {
                        $field["name"] = $lang_key . '_' . $temp_field_name;
                        $field["explain"] = $temp_field_explain . "(" . $lang_value . ")";
                        $migration["fields"][$field["name"]] = $field;
                    }
                } else {
                    $migration["fields"][$field_name[$key]] = $field;
                }
            }
            
        }

        if ( ! write_file(APPPATH . 'migration/'.$table.'.json', json_encode($migration))) {
            $data['error'] = "cant write to file";
            $this->output->set_status_header('500');
        } else {
            $data['success'] = TRUE;
            $data['load'] = base_url('admin/installation/migration/' . $table . '.json');
            $this->output->set_status_header('200');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function build($migration) {
        $data['migration_file'] = $migration;
        $migration_file = $migration ? read_file(APPPATH . 'migration/' . $migration . '.json') : FALSE;
        $data['migration'] = $migration_file ? json_decode($migration_file, TRUE) : FALSE;
        
        $this->load->dbforge();
        $attributes = array(
            'ENGINE' => 'InnoDB',
            'DEFAULT CHARSET' => 'utf8',
            'AUTO_INCREMENT' => '1',
        );

        $this->dbforge->add_field('id');
        $this->dbforge->add_field("`created_at` datetime NOT NULL");
        $this->dbforge->add_field("`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
        $this->dbforge->add_field("`status` tinyint(1) NOT NULL DEFAULT '0'");
        $this->dbforge->add_field("`sort` int(4) NOT NULL DEFAULT '0'");
        $this->dbforge->add_field("`seo_id` int(11) NOT NULL DEFAULT '0'");
        $this->dbforge->add_field("`lang` varchar(8) DEFAULT NULL");
        
        $this->dbforge->create_table($data['migration']['table'], TRUE, $attributes);
        
        foreach($data['migration']['fields'] as $field) {
            if( ! $this->db->field_exists($field['name'], $data['migration']['table']) ) {
                if($field['type'] === "datetime" || $field['type'] === "date" || $field['type'] === "time") {
                    $this->dbforge->add_column($data['migration']['table'], array($field['name'] => array('type' => 'datetime', 'null' => TRUE)));
                } else if($field['type'] === "table" || $field['type'] === "map" || $field['type'] === "short" || $field['type'] === "image" || $field['type'] === "file" || $field['type'] === "email" || $field['type'] === "tel" || $field['type'] === "video" || $field['type'] === "choice" || $field['type'] === "color" || $field['type'] === "search" || $field['type'] === "multipleTable") {
                    $this->dbforge->add_column($data['migration']['table'], array($field['name'] => array('type' => 'varchar', 'null' => TRUE, 'constraint' => 512)));
                } else if($field['type'] === "long" || $field['type'] === "html" || $field['type'] === "multiple") {
                    $this->dbforge->add_column($data['migration']['table'], array($field['name'] => array('type' => 'text', 'null' => TRUE)));
                } else if($field['type'] === "integer") {
                    $this->dbforge->add_column($data['migration']['table'], array($field['name'] => array('type' => 'int', 'default' => 0, 'constraint' => 6)));
                } else if($field['type'] === "double") {
                    $this->dbforge->add_column($data['migration']['table'], array($field['name'] => array('type' => 'double', 'default' => 0)));
                } else if($field['type'] === "boolean") {
                    $this->dbforge->add_column($data['migration']['table'], array($field['name'] => array('type' => 'tinyint', 'default' => 0, 'constraint' => 1)));
                }

            }
            /*
            if($langs = $this->config->item('available_lang')) {
                $field_name = $field['name'];
                foreach ($langs as $lang_key => $lang_value) {
                    $field['name'] = isset($field['lang']) && $field['lang'] > 0 ? $lang_key . '_' . $field_name : $field_name;
                    
                }
                
            }
             * 
             */
        }
        
        $data = $this->db->field_data($data['migration']['table']);
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function build_langs() {
        $this->load->dbforge();
        $seo_table = Msite::TABLE_SEO;
        if($this->config->item('available_lang')) {
            foreach ($this->config->item('available_lang') as $lang_key => $lang_value) {
                $field_name = $lang_key . '_title';
                if( ! $this->db->field_exists($field_name, $seo_table) ) {
                    $this->dbforge->add_column($seo_table, array($field_name => array('type' => 'varchar', 'null' => TRUE, 'constraint' => 255)));
                }
                
                $field_name = $lang_key . '_description';
                if( ! $this->db->field_exists($field_name, $seo_table) ) {
                    $this->dbforge->add_column($seo_table, array($field_name => array('type' => 'text', 'null' => TRUE)));
                }
                
                $field_name = $lang_key . '_friendly';
                if( ! $this->db->field_exists($field_name, $seo_table) ) {
                    $this->dbforge->add_column($seo_table, array($field_name => array('type' => 'varchar', 'null' => TRUE, 'constraint' => 255)));
                }
            }
        }
        
        $page_table = Msite::TABLE_PAGES;
        if($this->config->item('available_lang')) {
            foreach ($this->config->item('available_lang') as $lang_key => $lang_value) {
                $field_name = $lang_key . '_title';
                if( ! $this->db->field_exists($field_name, $page_table) ) {
                    $this->dbforge->add_column($page_table, array($field_name => array('type' => 'varchar', 'null' => TRUE, 'constraint' => 255)));
                }
            }
        }
        
        $data['success'] = TRUE;
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
}