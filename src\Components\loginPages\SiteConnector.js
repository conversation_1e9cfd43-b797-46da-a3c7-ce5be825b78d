import React, { useEffect, useState, Suspense, lazy, Component } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { toast } from "react-toastify";
import { getAllUrlParams } from "../-Helpers-/UrlParameters";

const Pages = lazy(() => import("./Pages"));
const PagesRegister = lazy(() => import("./PagesRegister"));
const PagesUserConsole = lazy(() => import("./PagesUserConsole"));
const PagesMekomotSherut = lazy(() => import("./PagesMekomotSherut"));

// Error Boundary component to catch errors in lazy-loaded components
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Component error caught by boundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return this.props.fallback || (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          padding: '20px',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>שגיאה בטעינת הדף</h3>
          <p style={{ marginBottom: '15px' }}>אירעה שגיאה בטעינת הדף</p>
          <button
            onClick={() => {
              this.setState({ hasError: false });
              window.location.reload();
            }}
            style={{
              padding: '10px 20px',
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            רענן דף
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

const SiteConnector = (props) => {
  console.log("SiteConnector rendered with page:", props.page);
  const navigate = useNavigate();
  const location = useLocation();
  const { checkSessionValidity, logout, user } = useAuth();
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Always check for logout parameter first, regardless of page
    const urlParams = getAllUrlParams(location.search);
    if (urlParams.logout === "1" || urlParams.logout === true) {
      console.log("Logout parameter detected, logging out...");
      logout("התנתקת מהמערכת בהצלחה");
      return;
    }

    // Handle logout page
    if (props.page === "logout") {
      console.log("Logout page detected, logging out...");
      logout("התנתקת מהמערכת בהצלחה");
      return;
    }

    // Skip ALL authentication checks for mekomotSherutPages (sherutPlaces)
    if (props.page === "mekomotSherutPages") {
      console.log("Skipping ALL authentication checks for sherutPlaces - public page");
      return;
    }

    // Skip session validation for public pages
    const publicPages = ["register", "login", "reSendPass", "loginQueryUrl"];
    if (publicPages.includes(props.page)) {
      console.log("Skipping session check for public page:", props.page);
      return;
    }

    // Skip for iframe cases
    const isIframe = location.pathname === '/sherutPlaces' && location.search.includes('iframe=1');
    if (isIframe) {
      console.log("Skipping session check for iframe");
      return;
    }

    // Only check session for authenticated pages with logged-in users
    if (user) {
      console.log("Checking session for authenticated user...");

      // בדיקה אם זה iOS WebView - אם כן, נדלג על בדיקת סשן
      const isIOSWebView = () => {
        const userAgent = navigator.userAgent.toLowerCase();
        return (userAgent.includes('iphone') || userAgent.includes('ipad')) &&
               (userAgent.includes('wkwebview') || typeof window.ReactNativeWebView !== 'undefined');
      };

      if (isIOSWebView()) {
        console.log("iOS WebView detected - skipping session validation in SiteConnector");
        return; // לא נבצע בדיקת סשן ב-iOS כדי למנוע תקיעות
      }

      const validateSession = async () => {
        try {
          const isValid = await checkSessionValidity(user);
          if (!isValid) {
            toast.error("פג תוקף החיבור שלך למערכת, אנא התחבר/י מחדש");
            logout("פג תוקף החיבור שלך למערכת");
          }
        } catch (error) {
          console.error("Session validation failed:", error);
          // Don't logout on network errors
        }
      };
      validateSession();
    }
  }, [location, user, checkSessionValidity, logout, props.page]);

  // Error fallback
  if (hasError) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        padding: '20px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>שגיאה בטעינת הדף</h3>
        <p style={{ marginBottom: '15px' }}>אירעה שגיאה בטעינת הדף</p>
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          רענן דף
        </button>
      </div>
    );
  }

  let PageComponent;
  if (
    props.page === "register" ||
    props.page === "login" ||
    props.page === "reSendPass" ||
    props.page === "loginQueryUrl"
  ) {
    PageComponent = PagesRegister;
  } else if (props.page === "userConsolePages") {
    console.log("Rendering PagesUserConsole for userConsolePages");
    PageComponent = PagesUserConsole;
  } else if (props.page === "mekomotSherutPages") {
    PageComponent = PagesMekomotSherut;
  } else {
    PageComponent = Pages;
  }

  return (
    <ErrorBoundary
      fallback={
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          padding: '20px',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>שגיאה בטעינת הדף</h3>
          <p style={{ marginBottom: '15px' }}>אירעה שגיאה בטעינת הדף</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            רענן דף
          </button>
        </div>
      }
    >
      <Suspense fallback={<div style={{padding: '20px', textAlign: 'center'}}>טוען...</div>}>
        <PageComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export default SiteConnector;
