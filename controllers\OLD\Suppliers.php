<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Suppliers extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+OtzarHaaretz';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+OtzarHaaretz-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        $this->load->model('msiteWs');
        $this->load->model('OtzarHaretz');
        
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    
    public function getSuppliers($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            //$this->db->select('*');
            $this->db->select('id,name,phone,address');
            $this->db->from('suppliers');
            $this->db->where('status', 1);
            $this->db->order_by('name', 'ASC');
            $result= $this->db->get();
            $suppliers = $result->result_array();
            
            $allSuppliers = array();
            
            if(!empty($suppliers)) {
                
                
                $suppliers = $this->OtzarHaretz->orderByFavoriteSuppliers($suppliers, $jsonPosts);
                
                
                foreach ($suppliers as $value) {
                    
                    $this->db->select('id');
                    $this->db->from('cashiersSuppliers');
                    $this->db->where('status', 1);
                    $this->db->where('supplierId', $value['id']);
                    $result= $this->db->get();
                    
                    if($result->num_rows() > 0) {
                        
                        
                        
                        $name = character_limiter($value['name'], 30,'...');
                        $value['name'] = $name;
                        $allSuppliers[] = $value;
                        
                        
                    }
                    
                    
                }
                
            }
            
            $output['suppliers'] = $allSuppliers;
                           

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function getSuppliersSystem($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','page','limit','search'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
            $rows = !empty($jsonPosts['limit']) ? (int)$jsonPosts['limit'] : 8;
            
            
            $this->db->select('id');
            $this->db->from('suppliers');
            if(!empty($jsonPosts['search'])) {
               $this->db->like('name', $jsonPosts['search'], 'both');
                $this->db->or_like('ownerName', $jsonPosts['search'], 'both');
                $this->db->or_like('agent', $jsonPosts['search'], 'both');
                $this->db->or_like('phone', $jsonPosts['search'], 'both');
                $this->db->or_like('address', $jsonPosts['search'], 'both');
            }
            
            $result1= $this->db->get();
            
            $totalPages = ceil($result1->num_rows() / $rows); //ONLY ROUND UP
        
            
            
            $this->db->select('id,name,phone,ownerName,address,comments,status,agent,email,username,tz');
            $this->db->from('suppliers');
            
            if(!empty($jsonPosts['search'])) {
                
                $this->db->like('name', $jsonPosts['search'], 'both');
                $this->db->or_like('ownerName', $jsonPosts['search'], 'both');
                $this->db->or_like('agent', $jsonPosts['search'], 'both');
                $this->db->or_like('phone', $jsonPosts['search'], 'both');
                $this->db->or_like('address', $jsonPosts['search'], 'both');
                
            }
            
            $output['search'] = $jsonPosts['search'];
            
            
            $data = array();
            
            $this->db->order_by('id', 'DESC');
            //$this->db->order_by('id', 'ASC');
            
            //$this->db->where('firstName', 'asdasdad');

            $page = !empty((int)$jsonPosts['page']) ? (int)$jsonPosts['page'] : 0;
            $page = $page > 0 ? $rows*$page : $page;
            //$page = !empty($jsonPosts['search']) ? 0 : $page;
            
            $this->db->limit($rows,$page);
            
            $result= $this->db->get();
            $clientsDb = $result->result_array();
            
            
            
            if(!empty($clientsDb)) {
                
                
                
                foreach ($clientsDb as $supplier) {
                    
                    $data[] = array (
                            'id' => $supplier['id'],
                            'status' => $supplier['status'],
                            'name' => $supplier['name'],
                            'agent' => $supplier['agent'],
                            'phone' => str_replace("+972", "", $supplier['phone']),
                            'ownerName' => $supplier['ownerName'],
                            'email' => $supplier['email'],
                            'address' => character_limiter($supplier['address'], 15,'...'),
                            'fullAddress' => $supplier['address'],
                            'comments' => $supplier['comments'],
                            'username' => $supplier['username'],
                            'tz' => $supplier['tz']
                        );
                    
                }
                
            }
            
            $output['totalPages'] = $totalPages;
            $output['clients'] = $data;
            
            
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function editSupplierSystem($param = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(
                    array('userId',
                        'token',
                        'userCredential',
                        'rowId',
                        'name',
                        'agent',
                        'phone',
                        'email',
                        'ownerName',
                        'address',
                        'comments',
                        'pass',
                        'tz'
                        )
                );
        
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        $output['rowId'] = $jsonPosts['rowId'];
        
        
        if($checkUserCredentials != 'unauthorized' ) {
            
            
            if( !empty($jsonPosts['name']) &&
                !empty($jsonPosts['ownerName']) &&
                !empty($jsonPosts['rowId']) &&
                !empty($jsonPosts['phone']) ) 
                {

                

                $newData = array(
                                'name' => $jsonPosts['name'],
                                'ownerName' => $jsonPosts['ownerName'],
                                'agent' => $jsonPosts['agent'],
                                'email' => $jsonPosts['email'],
                                'comments' => $jsonPosts['comments'],
                                'address' => $jsonPosts['address'],
                                'phone' => $jsonPosts['phone'],
                                'tz' => $jsonPosts['tz']
                            );
                
                if(!empty($jsonPosts['pass']) &&  strlen($jsonPosts['pass']) >= 4) {
                    
                    $newData['passwordMd5'] = md5($jsonPosts['pass']);
                    
                }
                
                $update = false;
                $this->db->where('id', $jsonPosts['rowId']);
                $update = $this->db->update('suppliers', $newData); 
                

                
                
                if($update) {
                    $output['ok'] = 'הפרטים עודכנו בהצלחה';
                }              
                
                else {
                
                    $output['error'] = 'הפרטים לא עודכנו';
                }
                
                
            } else {

                $output['error'] = 'הפרטים לא עודכנו';

            }    
                    
        }
            
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    public function manualInsertSupplier ($param = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(
                    array('userId',
                        'token',
                        'userCredential',
                        'name',
                        'agent',
                        'phone',
                        'email',
                        'tz',
                        'ownerName',
                        'address',
                        'comments')
                );
        
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        
        if($checkUserCredentials != 'unauthorized' ) {
            
            if( !empty($jsonPosts['name']) &&
                !empty($jsonPosts['ownerName']) &&
                !empty($jsonPosts['phone']) ) 
                {
                

                $table = 'suppliers';
                
                $jsonPosts['phone'] = $this->OtzarHaretz->cleanPhone($jsonPosts['phone']);
                
                $username = $this->OtzarHaretz->CheckUsernameSupplierExist($jsonPosts['phone']);
                
                
                $newData = array(
                    
                    'lang' => NULL,
                    'sort' => '0',
                    'status' => 1,
                    'created_at' => date("Y-m-d H:i:s"),

                    'username' => $username,
                    'passwordMd5' => md5($jsonPosts['tz']),

                    'name' => $jsonPosts['name'],
                    'ownerName' => $jsonPosts['ownerName'],
                    'agent' => $jsonPosts['agent'],
                    'email' => $jsonPosts['email'],
                    'tz' => $jsonPosts['tz'],
                    'comments' => $jsonPosts['comments'],
                    'address' => $jsonPosts['address'],
                    'phone' => $jsonPosts['phone']
                );
                
                $insert = $this->db->insert($table, $newData); 
                $insert_id = $this->db->insert_id(); 
                
                if($insert_id) {
                    
                    $cashier = array(
                           'lang' => NULL,
                           'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($table, 'sort') + 10,
                           'status' => 1,
                           'created_at' => date("Y-m-d H:i:s"),
                           'name' => '1',
                           'code' => '123',
                           'supplierId' => $insert_id
                    );

                    $insert = $this->db->insert('cashiersSuppliers', $cashier);
                    
                    $output['response'] = array( 'addLeadtoDb' => 'true' );
                    
                } else {
                    
                    $output['error'] = 'שגיאת מערכת';
                    
                }
                
                    
                
            } else {

                $output['error'] = 'שגיאת מערכת';

            }    
                    
        }
            
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function suppliersCsv() {
        
        
        //let token = md5(user.id + user.phone);
        //let csvDownload = ConstantsNames.base_url + 'Suppliers/csv?token=' + token + '&id=' +  md5(user.id);
        
        $token = $this->input->get('token');
        $search = $this->input->get('search');
        
        $search = ($search == 'undefined') ? '' : $search;
        
        
        if( md5(md5($this->data['code'])) !== $token ) {die('ERROR');}
        
        

        $this->db->select('id,name,phone,ownerName,address,comments,status,agent,email,tz');
        $this->db->from('suppliers');

        if(!empty($search)) {

            $this->db->like('name',$search, 'both');
            $this->db->or_like('ownerName', $search, 'both');
            $this->db->or_like('agent', $search, 'both');
            $this->db->or_like('phone', $search , 'both');
            $this->db->or_like('address', $search, 'both');

        }
        
        $this->db->order_by('id', 'DESC');
        
        $result= $this->db->get();
        $clientsDb = $result->result_array();
            

        $data = array();

        if(!empty($clientsDb)) {

            foreach ($clientsDb as $supplier) {
                    
                $data[] = array (
                        'id' => $supplier['id'],
                        'status' => $supplier['status'],
                        'name' => $supplier['name'],
                        'ownerName' => $supplier['ownerName'],
                        'tz' => $supplier['tz'],
                        'phone' => str_replace("+972", "", $supplier['phone']),
                        'email' => $supplier['email'],
                        'address' => $supplier['address'],
                        'agent' => $supplier['agent'],
                        'comments' => $supplier['comments']
                    );

            }

        }
        
        
        $dateFile = date('d_m_Y');  //date('m');
        $filename = 'suppliers_'.$dateFile.'_'.rand(111,999);
        
        $csv = array();
        
        $csv[] = array (
            'id' => '#',
            'status' => 'סטטוס',
            'name' => 'שם',
            'ownerName' => 'איש קשר',
            'tz' => 'ת.ז.',
            'phone' => 'טלפון',
            'email' => 'מייל',
            'address' => 'כתובת',
            'agent' => 'סוכן',
            'comments' => 'הערות'
        );
        
        $excellData = array_merge($csv,$data);
        
        
        
        header('Content-Encoding: UTF-8'); 
        header('Content-type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename='.$filename.'.csv');
        header("Pragma: no-cache");
        header("Expires: 0");

        $handle = fopen('php://output', 'w');
        fwrite($handle, "\xEF\xBB\xBF");
        

        foreach ($excellData as $data_array) {
            fputcsv($handle, $data_array);
        }
            fclose($handle);
        exit;
        
        
        
        
        //force_download($filename, $data);
    }
    
    
    
    
    
    
    
    public function getCashiers($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','supplierId'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            //$this->db->select('*');
            $this->db->select('id,name,code,phone');
            $this->db->from('cashiersSuppliers');
            $this->db->where('status', 1);
            $this->db->where('supplierId', $jsonPosts['supplierId']);
            $result= $this->db->get();
            $cashiersSuppliers = $result->result_array();
            
            $allCashiersSuppliers = array();
            
            if(!empty($cashiersSuppliers)) {
                foreach ($cashiersSuppliers as $value) {

                    $name = character_limiter($value['name'], 20,'...');
                    $value['name'] = $name;
                    $allCashiersSuppliers[] = $value;
                }
            } 
            
            $output['cashiers'] = $allCashiersSuppliers;
            
            $output['countCashiers'] = count($allCashiersSuppliers);
                           

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function getMoneySupplier($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('admin','superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
        
            $output['money'] = $this->OtzarHaretz->getSupplierMoney($jsonPosts);
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    public function addCashier($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','kupaName','kupaPass','kupaPhone','supplierId'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if( !empty($jsonPosts['kupaName']) && !empty($jsonPosts['kupaPass']) ) {
                
                if ( strlen($jsonPosts['kupaPass']) >= 3 ) {

                $supplierId = !empty($jsonPosts['supplierId']) ? $jsonPosts['supplierId'] : $jsonPosts['userId'];
                    
                $cashier = array(
                    'lang' => NULL,
                    'sort' => '',
                    'status' => 1,
                    'created_at' => date("Y-m-d H:i:s"),
                    'name' => $jsonPosts['kupaName'],
                    'code' => $jsonPosts['kupaPass'],
                    'phone' => $jsonPosts['kupaPhone'],
                    'supplierId' => $supplierId
                );

                $insert = $this->db->insert('cashiersSuppliers', $cashier);
                
                    if($insert) {

                        $output['ok'] = 'הקופה הוספה בהצלחה';

                    } else {
            
                        $output['error'] = 'שגיאה';
                    }
                
                } else {
                    
                    $output['error'] = 'הסיסמה חייבת להיות מעל ארבעה תווים';
                    
                }
                
                
            } else {
            
                
                $output['error'] = 'שגיאה';
                
                
            }
                        
                        
            
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function deletteCashier($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('admin','superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','rowId','supplierId'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        //$output['id'] = $jsonPosts['rowId'];
        
        if($checkUserCredentials != 'unauthorized') {
            
            if( !empty($jsonPosts['rowId']) ) {
                
                $this->db->select('id');
                $this->db->from('cashiersSuppliers');
                
                $supplierId = !empty($jsonPosts['supplierId']) ? $jsonPosts['supplierId'] : $jsonPosts['userId'];
                
                $this->db->where('supplierId', $supplierId);
                $result= $this->db->get();
                $cashiers = $result->result_array();
                
                
                $output['count'] = count($cashiers);
                
                
                if( count($cashiers) >= 2) {
                    
                    $updateData = array(
                        'status' => 0
                    );
                    
                    
                    $this->db->where('id', $jsonPosts['rowId']);
                    $update = $this->db->update('cashiersSuppliers', $updateData); 
                    
                    //$this->db->where('id', $jsonPosts['rowId']);
                    //$this->db->delete('cashiersSuppliers');
                    
                }
                
                
            }
                        
            
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function editCashier($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','rowId','kupaName','kupaPass','kupaPhone'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if(!empty($jsonPosts['rowId'])) {
                
                $cashier = array(
                    'name' => $jsonPosts['kupaName'],
                    'code' => $jsonPosts['kupaPass'],
                    'phone' => $jsonPosts['kupaPhone']
                );
                
                $this->db->where('id', $jsonPosts['rowId']);
                $update = $this->db->update('cashiersSuppliers', $cashier); 
                
                if($update) {
                    
                    $output['ok'] = '1';
                    
                } else {
                    $output['error'] = 'errorUpdate';
                }
                
            }
            
            $output['error'] = 'errorUpdate';

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    
    public function getReportData($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('admin','superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','startDate','endDate','supplierId'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            $output['data'] = $this->OtzarHaretz->getSupplierReport($jsonPosts);
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function csv() {
        
        
        //let token = md5(user.id + user.phone);
        //let csvDownload = ConstantsNames.base_url + 'Suppliers/csv?token=' + token + '&id=' +  md5(user.id);
        
        $token = $this->input->get('token');
        $id = $this->input->get('id');
        
        $params['startDate'] = $this->input->get('startDate');
        $params['endDate'] = $this->input->get('endDate');
                
        $this->db->select('*');
        $this->db->from('suppliers');
        $this->db->where('MD5(id)', $id);
        
        $result= $this->db->get();
        $user = $result->row_array();
        
        if( md5($user['id'].$user['phone']) !== $token ) {die('ERROR');}
        
//        $this->load->helper('file');
//        $this->load->helper('download');
        
        
        //print_r($user);
        
        $month = date('M');  //date('m');
        $filename = 'ID'.$user['id'].'_'.$month.'_'.date('Y');
        
        $this->db->select('*');
        $this->db->from('transactions');
        $this->db->where('supplierId', $user['id']);
        $this->db->order_by('created_at', 'DESC');
        
        $today = date("Y-m-d 00:00:00");
        $startDate = isset($params['startDate']) && !empty($params['startDate']) ? changeDateFormat($params['startDate'].' 0:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;
        $endDate = isset($params['endDate']) && !empty($params['endDate']) ? changeDateFormat($params['endDate'].' 24:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;

        $flag = array();

        if($startDate) {
            $this->db->where('created_at >=', $startDate );
        }

        if($endDate) {
            $this->db->where('created_at <=', $endDate );
        } 

        if( !$startDate && !$endDate ) {

            $this->db->where('created_at >=', $today );
        }
        
        
        
        $result = $this->db->get();
        
        
        
        $query = $result->result_array();
        
        $csv = array();
        
        $csv[] = array (
            'date' => 'תאריך',
            'name' => 'שם לקוח',
            'money' => 'סכום',
            'kupa' => 'שם קופה',
            'token' => 'אסמכתא'
        );
        
        
        foreach ($query as $value) {
            
            $this->db->select('firstName,lastName');
            $this->db->from('leadsLandpage');
            $this->db->where('id', $value['userId'] );
            $result= $this->db->get();
            $clientDb = $result->row_array();
            
            $nameDB = $clientDb['firstName'].' '.$clientDb['lastName'];
            $name = character_limiter($nameDB, 10,'...');
            
            
            $this->db->select('name');
            $this->db->from('cashiersSuppliers');
            $this->db->where('id', $value['cashierId'] );
            $result = $this->db->get();
            $cashier = $result->row_array();
            
            $csv[] = array (
                'date' => changeDateFormat($value['created_at'], 'Y-m-d H:i:s', 'd/m/Y H:i'),
                'name' => $name,
                'money' => $value['money'],
                'kupa' => $cashier['name'],
                'token' => $value['token'],
            );
            
        }
        
        $data = $csv;
        
        header('Content-Encoding: UTF-8'); 
        header('Content-type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename='.$filename.'.csv');
        header("Pragma: no-cache");
        header("Expires: 0");

        $handle = fopen('php://output', 'w');
        fwrite($handle, "\xEF\xBB\xBF");
        

        foreach ($data as $data_array) {
            fputcsv($handle, $data_array);
        }
            fclose($handle);
        exit;
        
        
        
        
        //force_download($filename, $data);
    }
        
    
    
    public function onOffSupplier($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','clientId','currentStatus'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized' ) {
            
            $output['current'] = $jsonPosts['currentStatus'];
            
            if(!empty($jsonPosts['clientId'])) {
                
                $status = $jsonPosts['currentStatus'] == 'false' ? 0 : 1;
                
                $params = array(
                    'status' => $status
                );
                
                $this->db->where('id', $jsonPosts['clientId']);
                $this->db->update('suppliers', $params); 
                
                $output['ok'] = '1';
                
            } else {
                $output['error'] = '1';
            }
        
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function makeFavoriteSuppliers($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','supplierId'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if( !empty($jsonPosts['supplierId'])  ) {
                
                
                $table = 'favoritesSuppliers';
                
                //$this->db->select('*');
                $this->db->select('id');
                $this->db->from($table);
                $this->db->where('supplierId', $jsonPosts['supplierId']);
                $this->db->where('userId', $jsonPosts['userId']);
                $this->db->where('status', 1);
                $result= $this->db->get();
                $row = $result->row_array();

                if($result->num_rows() > 0) {

                    
                    $this->db->where('id', $row['id']);
                    $this->db->delete($table);
                    
                    $output['OK'] = 'delette';

                } else {
                    
                       $data = array(
                        'lang' => NULL,
                        'sort' => '',
                        'status' => 1,
                        'created_at' => date("Y-m-d H:i:s"),
                        'userId' => $jsonPosts['userId'],
                        'supplierId' => $jsonPosts['supplierId']
                               
                    );

                    $insert = $this->db->insert($table, $data); 
                    $insert_id = $this->db->insert_id();


                    $output['OK'] = 'insert: '.$insert_id;

                }
                
                
            }
            
            else {
                $output['error'] = 'error';
            }
            
           
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
}