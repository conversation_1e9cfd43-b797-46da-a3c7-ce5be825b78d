<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

defined('BASEPATH') OR exit('No direct script access allowed');

class Site extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
        
        //if is not logged in die  
        if( ! $this->aauth->is_loggedin() ) { 
            redirect(base_url('admin?redirect=' . current_url() . getQS()), 'refresh', 401);
            die('The user is not connected.'); 
        } else if( ! $this->aauth->is_allowed($this->router->fetch_class() . '_' . $this->router->fetch_method())) {
            die('The user not have permission to view the content.'); 
        }
        $this->load->model('msite');
        
    }
    
    public function index($selected_page_id = FALSE) {
        $data['selected_page_id'] = $selected_page_id;
        $data['selected_page'] = $selected_page_id ? $this->msite->get_page($selected_page_id) : FALSE;
        
        $data['objects'] = $this->msite->get_all_page_objects($selected_page_id);
        
        $data['pages'] = $this->msite->get_all_pages();
        $data['view'] = $this->router->fetch_class() . '/index';
        $data['script'] = $this->router->fetch_class() . '/script';
        $this->load->view('admin/index', $data);
        
    }
    
    public function page($selected_page_id = FALSE) {
        $data['selected_page_id'] = $selected_page_id;
        $data['selected_page'] = $selected_page_id ? $this->msite->get_page($selected_page_id) : FALSE;
        
        $data['view'] = $this->router->fetch_class() . '/page';
        $data['script'] = $this->router->fetch_class() . '/script';
        $this->load->view('admin/index', $data);
        
    }
    
    public function put_page($parent_page_id = FALSE) {
        $data = array(
            'parent_id' => $this->input->post('parent_id'),
            'title' => $this->input->post('title'),
            'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
            'controller' => $this->input->post('controller'),
            'method' => $this->input->post('method'),
            'param' => '',//$this->input->post('param'),
            'sort' => $this->input->post('sort') ? $this->input->post('sort') : 0,
            'status' => $this->input->post('status') ? 1 : 0,
            'created_at' => date("Y-m-d H:i:s"),
        );
        
        if($langs = $this->config->item('available_lang')) {
            foreach ($langs as $lang_key => $lang_value) {
                $data[$lang_key . '_title'] = $this->input->post($lang_key . '_title');
            }
        }
                
        if($insert_id = $this->msite->insert_page($data)) {
            $data_seo = array(
                'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'controller' => $this->input->post('controller'),
                'method' => $this->input->post('method'),
                'param' => $this->input->post('param'),
                'robots' => $this->input->post('robots') ? $this->input->post('robots') : 'index, follow',
                'friendly' => $this->input->post('friendly') ? $this->input->post('friendly') : url_title($this->input->post('title')),
                'image' => $this->input->post('image') ? $this->input->post('image') : NULL,
                'canonical' => base_url($this->input->post('controller') . '/' . $this->input->post('method')),
            );
            if($langs = $this->config->item('available_lang')) {
                foreach ($langs as $lang_key => $lang_value) {
                    $data_seo[$lang_key . '_title'] = $this->input->post($lang_key . '_title');
                    $data_seo[$lang_key . '_description'] = strip_tags($this->input->post($lang_key . '_description'));
                    $data_seo[$lang_key . '_friendly'] = url_title($this->input->post($lang_key . '_title'));
                }
            }
            $seo_id = $this->msite->insert_object(Msite::TABLE_SEO, $data_seo);
            $this->msite->update_page($insert_id, array('seo_id' => $seo_id));
            
            if($langs = $this->config->item('available_lang')) {
                foreach ($langs as $lang_key => $lang_value) {
                    $data_h1 = array(
                        'page_id' => $insert_id,
                        'explain' => 'כותרת ראשית',
                        'type' => 'short',
                        'keyword' => 'h1',
                        'value' => $this->input->post($lang_key . '_title'),
                        'lang' => $lang_key,
                        'sort' => 0,
                        'status' => 1,
                        'created_at' => date("Y-m-d H:i:s"),
                    );
                    $this->msite->insert_page_object($data_h1);

                }
            } else {
                $data_h1 = array(
                    'page_id' => $insert_id,
                    'explain' => 'כותרת ראשית',
                    'type' => 'short',
                    'keyword' => 'h1',
                    'value' => $this->input->post('title'),
                    'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
                    'sort' => 0,
                    'status' => 1,
                    'created_at' => date("Y-m-d H:i:s"),
                );
                $this->msite->insert_page_object($data_h1);
            }
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function update_page($page_id) {
        $obj = $this->msite->get_page($page_id);
        $data = array(
            'parent_id' => $this->input->post('parent_id'),
            'title' => $this->input->post('title'),
            'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
            'controller' => $this->input->post('controller'),
            'method' => $this->input->post('method'),
            'param' => '',//$this->input->post('param'),
            'sort' => $this->input->post('sort') ? $this->input->post('sort') : 0,
            'status' => $this->input->post('status') ? 1 : 0,
        );
        
        if($langs = $this->config->item('available_lang')) {
            foreach ($langs as $lang_key => $lang_value) {
                $data[$lang_key . '_title'] = $this->input->post($lang_key . '_title');
            }
        }
        
        if($is_update = $this->msite->update_page($page_id, $data)) {
            $data_seo = array(
                'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'controller' => $this->input->post('controller'),
                'method' => $this->input->post('method'),
                'param' => $this->input->post('param'),
                'robots' => $this->input->post('robots') ? $this->input->post('robots') : 'index, follow',
                'friendly' => $this->input->post('friendly') ? $this->input->post('friendly') : url_title($this->input->post('title')),
                'image' => $this->input->post('image') ? $this->input->post('image') : NULL,
                'canonical' => base_url($this->input->post('controller') . '/' . $this->input->post('method')),
            );
            
            if($langs = $this->config->item('available_lang')) {
                foreach ($langs as $lang_key => $lang_value) {
                    $data_seo[$lang_key . '_title'] = $this->input->post($lang_key . '_title');
                    $data_seo[$lang_key . '_description'] = strip_tags($this->input->post($lang_key . '_description'));
                    $data_seo[$lang_key . '_friendly'] = url_title($this->input->post($lang_key . '_title'));
                }
            }
            
            $this->msite->update_object(Msite::TABLE_SEO, $obj->Arg('seo_id'), $data_seo);
            
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function destroy_page($page_id) {
        $page = $this->msite->get_page($page_id);
        
        if($page->Arr('childs')) {
            foreach($page->Arr('childs') as $child) {
                $page_objects = $this->msite->get_all_page_objects($child->Id());
                
                if($page_objects) {
                    foreach($page_objects as $obj) {
                        if($obj->Arg('type') === "image") {
                            $filename = apppicIMG . $obj->Arg('value');
                            if(file_exists($filename)) {
                                unlink($filename);
                            }
                        } else if($obj->Arg('type') === "file") {
                            $filename = appFILES . $obj->Arg('value');
                            if(file_exists($filename)) {
                                unlink($filename);
                            }
                        }
                        $this->msite->delete_page_object($obj->Id());
                    }
                }
                
                if($child->Arg('seo_id')) {
                    $this->msite->delete_seo($child->Arg('seo_id'));
                }
                
                $this->msite->delete_page($child->Id());
            }
        }
        
        $page_objects = $this->msite->get_all_page_objects($page_id);
                
        if($page_objects) {
            foreach($page_objects as $obj) {
                if($obj->Arg('type') === "image") {
                    $filename = apppicIMG . $obj->Arg('value');
                    if(file_exists($filename)) {
                        unlink($filename);
                    }
                } else if($obj->Arg('type') === "file") {
                    $filename = appFILES . $obj->Arg('value');
                    if(file_exists($filename)) {
                        unlink($filename);
                    }
                }
                $this->msite->delete_page_object($obj->Id());
            }
        }
        
        if($page->Arg('seo_id')) {
            $this->msite->delete_seo($page->Arg('seo_id'));
        }
        
        if($this->msite->delete_page($page_id)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
        
        
    }
    
    public function put_object($page_id = FALSE) {
        $keyword = $this->input->post('keyword');
        $type = $this->input->post('type');
        $explain = $this->input->post('explain');
        
        $data = array(
            'page_id' => $page_id,
            'explain' => $explain,
            'type' => $type,
            'keyword' => $keyword,
            'sort' => $this->input->post('sort') ? $this->input->post('sort') : 0,
            'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
            'can_update' => $this->input->post('can_update') ? $this->input->post('can_update') : 0,
            'status' => $this->input->post('status') && $this->input->post('status') > 0 ? 1 : 0,
            'created_at' => date("Y-m-d H:i:s"),
        );
        
        if(!$keyword) {
            return $this->output
                    ->set_status_header('500')
                    ->set_content_type('application/json')
                    ->set_output(json_encode(array(
                        'error' => "keyword must be supply and unique for this page"
                    )));
        }
        
        if($insert_id = $this->msite->insert_page_object($data)) {
            $data['id'] = $insert_id;
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function update_object($object_id) {
        $object = $this->msite->get_page_object($object_id);
        $page_id = $object->Arg('page_id');
        $keyword = $this->input->post('keyword');
        $type = $this->input->post('type');
        $explain = $this->input->post('explain');
        $extra = $this->input->post('extra');
        
        $data = array(
            'explain' => $explain,
            'type' => $type,
            'keyword' => $keyword,
            'extra' => $extra,
            'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
            'can_update' => $this->input->post('can_update') ? $this->input->post('can_update') : 0,
            'sort' => $this->input->post('sort') ? $this->input->post('sort') : 0,
            'status' => $this->input->post('status') && $this->input->post('status') > 0 ? 1 : 0,
        );
        
        if($keyword) {
            switch($object->Arg('type')) {
                case "arg":
                    $data['value'] = $this->input->post('value');
                    break;
                case "image":
                    $config['upload_path']          = apppicIMG;
                    $config['allowed_types']        = 'gif|jpg|png';
                    $config['max_size']             = 0;
                    $config['max_width']            = 0;
                    $config['max_height']           = 0;
                    $config['file_name']            = "image_page_" . $page_id . "_" . $keyword;

                    $this->load->library('upload', $config);
                    $this->upload->initialize($config, TRUE);
                    if($this->upload->do_upload('value')) {
                        $filename = apppicIMG . $object->Arg('value');
                        if(file_exists($filename)) {
                            unlink($filename);
                        }

                        $filedata = $this->upload->data(); 
                        $data['value'] = $filedata['file_name'];
                    }
                    break;
                case "file":
                    $config['upload_path']          = appFILES;
                    $config['allowed_types']        = '*';
                    $config['file_name']            = "file_page_" . $page_id . "_" . $keyword;

                    $this->load->library('upload', $config);
                    $this->upload->initialize($config, TRUE);
                    if($this->upload->do_upload('value')) {
                        $filename = appFILES . $object->Arg('value');
                        if(file_exists($filename)) {
                            unlink($filename);
                        }

                        $filedata = $this->upload->data(); 
                        $data['value'] = $filedata['file_name'];
                    }
                    break;
                default :
                    $data['value'] = $this->input->post('value');
            }
        } else {
            return $this->output
                    ->set_status_header('500')
                    ->set_content_type('application/json')
                    ->set_output(json_encode(array(
                        'error' => "keyword must be supply and unique for this page"
                    )));
        }
        
        if($this->msite->update_page_object($object_id, $data)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function update_object_value($object_id) {
        $object = $this->msite->get_page_object($object_id);
        $page_id = $object->Arg('page_id');
        $keyword = $object->Arg('keyword');
        
        
        if($keyword) {
            switch($object->Arg('type')) {
                case "arg":
                    $data['value'] = $this->input->post('value');
                    break;
                case "image":
                    $config['upload_path']          = apppicIMG;
                    $config['allowed_types']        = 'gif|jpg|png';
                    $config['max_size']             = 0;
                    $config['max_width']            = 0;
                    $config['max_height']           = 0;
                    $config['file_name']            = "image_page_" . $page_id . "_" . $keyword;

                    $this->load->library('upload', $config);
                    $this->upload->initialize($config, TRUE);
                    if($this->upload->do_upload('value')) {
                        $filename = apppicIMG . $object->Arg('value');
                        if(file_exists($filename)) {
                            unlink($filename);
                        }

                        $filedata = $this->upload->data(); 
                        $data['value'] = $filedata['file_name'];
                    }
                    break;
                case "file":
                    $config['upload_path']          = appFILES;
                    $config['allowed_types']        = '*';
                    $config['file_name']            = "file_page_" . $page_id . "_" . $keyword;

                    $this->load->library('upload', $config);
                    $this->upload->initialize($config, TRUE);
                    if($this->upload->do_upload('value')) {
                        $filename = appFILES . $object->Arg('value');
                        if(file_exists($filename)) {
                            unlink($filename);
                        }

                        $filedata = $this->upload->data(); 
                        $data['value'] = $filedata['file_name'];
                    }
                    break;
                default :
                    $data['value'] = $this->input->post('value');
            }
        } else {
            return $this->output
                    ->set_status_header('500')
                    ->set_content_type('application/json')
                    ->set_output(json_encode(array(
                        'error' => "keyword must be supply and unique for this page"
                    )));
        }
        
        if($this->msite->update_page_object($object_id, $data)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function destroy_object($object_id) {
        $object = $this->msite->get_page_object($object_id);
        
        if($object->Arg('type') === "image") {
            $filename = apppicIMG . $object->Arg('value');
            if(file_exists($filename)) {
                unlink($filename);
            }
        } else if($object->Arg('type') === "file") {
            $filename = appFILES . $object->Arg('value');
            if(file_exists($filename)) {
                unlink($filename);
            }
        }
        
        if($this->msite->delete_page_object($object_id)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
        
        
    }
    
   
    
}