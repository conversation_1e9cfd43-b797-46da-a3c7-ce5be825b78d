<div class="panel panel-default">
    <!-- Default panel contents -->
    <div class="panel-heading">
        טבלת לידים
        <small>
            <?php showInfo($total, $limit, $page); ?>
        </small>
        
        <form action="" method="get" class="form-inline pull-left">
            <div class="form-group">
                <input type="text" name="from_date" value="<?php echo $from_date; ?>" class="form-control input-sm date" placeholder="מתאריך">
            </div>
            <div class="form-group">
                <input type="text" name="to_date" value="<?php echo $to_date; ?>" class="form-control input-sm date" placeholder="עד תאריך">
            </div>
            
            
            <button type="submit" class="btn btn-sm btn-hover btn-default">חפש</button>
            <a href="<?php echo base_url('admin/lead/csv' . getQS()); ?>" class="btn btn-sm btn-hover btn-warning">יצא לאקסל</a>
        </form>
        <div style="clear: both;"></div>
    </div>
    <!-- Table -->
    <table class="table table-hover">
        <thead>
            <tr>

                <th><?php showOrder('created_at', 'תאריך'); ?></th>
                <th><?php showOrder('name', 'שם מלא'); ?></th>
                <th><?php showOrder('email', 'אימייל'); ?></th>
                <th><?php showOrder('phone', 'טלפון'); ?></th>
                <th><?php showOrder('message', 'הודעה'); ?></th>
                <th><?php showOrder('kind', 'עמוד'); ?></th>
                <th class="text-center"><?php showOrder('status', 'סטאטוס'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if(isset($objects) && !empty($objects)) { foreach($objects as $row) { ?>
                <tr id="row<?php echo $row->Id(); ?>" role="row">
                    <td> 
                        <?php echo $row->Datetime('created_at', "H:i - d/m/Y"); ?>
                    </td>
                    <td>
                        <?php echo $row->Arg('name'); ?>
                    </td>
                    <td>
                        <?php echo $row->Arg('email'); ?>
                    </td>
                    <td>
                        <?php echo $row->Arg('phone'); ?>
                    </td>
                    <td style="max-width: 200px;overflow-wrap: break-word;">
                        <?php echo $row->Arg('message'); ?>
                    </td>
                    <td style="max-width: 100px;overflow-wrap: break-word;">
                        <?php echo $row->Arg('kind'); ?>
                    </td>
                    <td class="text-center">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/lead/status/' . $row->Id()); ?>">
                                <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                            </button> 
                            <button type="button" data-url="<?php echo base_url('admin/' . $this->router->fetch_class() . '/destroy/' . $row->Id() . getQS()); ?>" class="btn btn-danger delete">
                                מחק
                            </button>
                        </div>

                    </td>
                </tr>
            <?php }} ?>
        </tbody>
    </table>
    <div class="row">
        <div class="col-md-12 text-center">
            <?php showPages($total, $limit, $page); ?>
        </div>
    </div>
</div>





