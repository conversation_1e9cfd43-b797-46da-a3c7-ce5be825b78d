<div id="alertModal" class="modal fade modal-alert <?php echo $alert['class'] ?> <?php echo $this->input->get('mobile') == 1 ? 'mobile' : ''; ?>" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title text-right"><?php echo isset($alert['title']) ? $alert['title'] : ''; ?></h4>
            </div>
            <div class="modal-body">
                <p><?php echo isset($alert['message']) ? $alert['message'] : ''; ?></p>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
<?php if(isset($alert) && $alert) { ?>
$("#alertModal").modal('show');
<?php } ?>
    
function popup(title, message) {
    var popup = $("#alertModal");
    popup.find(".modal-title").text(title);
    popup.find(".modal-body p").text(message);
    popup.modal();
}

function close_popup() {
    var popup = $("#alertModal");
    popup.find(".modal-title").text('');
    popup.find(".modal-body p").text('');
    popup.modal('hide');
}
</script>

<?php if (isset($ShowPopup) and !empty($ShowPopup)): ?>
    <script>
        popup('<?php echo $ShowPopup['title'] ?>', '<?php echo $ShowPopup['errorMessage'] ?>');
    </script>
<?php endif; ?>

