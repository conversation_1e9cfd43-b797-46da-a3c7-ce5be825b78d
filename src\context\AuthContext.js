import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import debugHelper from '../utils/debugHelper';

const AuthContext = createContext();

// Helper to safely parse user data from localStorage
export const getSafeUserData = () => {
  try {
    const userDataStr = localStorage.getItem("userData");
    if (!userDataStr || userDataStr === "undefined" || userDataStr === "") {
      return null;
    }
    return JSON.parse(userDataStr);
  } catch (error) {
    console.error("Error parsing userData from localStorage:", error);
    return null;
  }
};

// Helper to decode JWT and check expiry
const isJWTExpired = (token) => {
  try {
    // JWT structure: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      return true; // Invalid JWT format
    }
    
    // Decode the payload (base64url)
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
    
    // Check expiry
    if (payload.exp) {
      const currentTime = Math.floor(Date.now() / 1000);
      return currentTime > payload.exp;
    }
    
    // If no expiry claim, consider it expired for safety
    return true;
  } catch (error) {
    console.error("Error decoding JWT:", error);
    return true; // Consider expired on error
  }
};

// Helper to get/set last validation timestamp
const getLastValidationTimestamp = () => {
  const timestamp = localStorage.getItem('lastSessionValidation');
  return timestamp ? parseInt(timestamp, 10) : null;
};

const setLastValidationTimestamp = () => {
  localStorage.setItem('lastSessionValidation', Date.now().toString());
};

const clearLastValidationTimestamp = () => {
  localStorage.removeItem('lastSessionValidation');
};

// Grace period in milliseconds (e.g., 30 minutes)
const VALIDATION_GRACE_PERIOD = 30 * 60 * 1000;

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const API_BASE_URL = process.env.REACT_APP_ENVIRONMENT === "dev"
    ? process.env.REACT_APP_API_BASE_URL_DEV
    : process.env.REACT_APP_API_BASE_URL;

  debugHelper.log('AuthProvider initialized', { API_BASE_URL });

  // Helper to check if running in iOS WebView
  const isIOSWebView = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    return (userAgent.includes('iphone') || userAgent.includes('ipad')) &&
           (userAgent.includes('wkwebview') || typeof window.ReactNativeWebView !== 'undefined');
  };

  // פונקציה לבדיקת תוקף הסשן מול השרת
  const checkSessionValidity = async (userData) => {
    if (!userData || !userData.SessionKey) {
      return false;
    }

    // בדיקה אם זה iOS WebView
    if (isIOSWebView()) {
      console.log("iOS WebView detected - performing local validation");
      
      // First check JWT expiry locally
      if (isJWTExpired(userData.SessionKey)) {
        console.log("JWT is expired - session invalid");
        return false;
      }
      
      // Check if we're within grace period from last successful validation
      const lastValidation = getLastValidationTimestamp();
      if (lastValidation) {
        const timeSinceValidation = Date.now() - lastValidation;
        if (timeSinceValidation < VALIDATION_GRACE_PERIOD) {
          console.log("Within grace period - accepting cached validation");
          return true;
        }
      }
      
      // Try server validation with shorter timeout
      try {
        const response = await axios.post(
          `${API_BASE_URL}/api/test/checkAuthorization`,
          { sessionKey: userData.SessionKey },
          { timeout: 2000 } // Very short timeout for iOS
        );
        
        if (response.status === 200) {
          setLastValidationTimestamp();
          return true;
        }
        return false;
      } catch (error) {
        console.error("iOS WebView session validation error:", error);
        
        // If we have a recent validation and JWT is not expired, allow access
        if (lastValidation && !isJWTExpired(userData.SessionKey)) {
          const timeSinceValidation = Date.now() - lastValidation;
          // Allow up to 2 hours since last validation in case of network issues
          if (timeSinceValidation < 2 * 60 * 60 * 1000) {
            console.log("Network error but within extended grace period - allowing access");
            return true;
          }
        }
        
        // Prompt user to retry
        toast.warning("בעיית חיבור - נא לרענן את הדף או להתחבר מחדש");
        return false;
      }
    }

    // Standard validation for non-iOS WebView
    try {
      const response = await axios.post(
        `${API_BASE_URL}/api/test/checkAuthorization`,
        { sessionKey: userData.SessionKey },
        { timeout: 3000 }
      );
      
      if (response.status === 200) {
        setLastValidationTimestamp();
        return true;
      }
      return false;
    } catch (error) {
      console.error("Session validation error:", error);
      return false;
    }
  };

  // פונקציה להתנתקות
  const logout = (message = "התנתקת מהמערכת") => {
    localStorage.removeItem("userData");
    localStorage.removeItem("rakazid");
    localStorage.removeItem("sayeretid");
    clearLastValidationTimestamp();
    setUser(null);
    navigate("/login");
    // הוספת הודעת toast
    if (message) {
      toast.success(message);
    }
  };

  // פונקציה להתחברות
  const login = (userData) => {
    try {
      localStorage.setItem("userData", JSON.stringify(userData));
      setLastValidationTimestamp(); // Set validation timestamp on login
      setUser(userData);
    } catch (error) {
      console.error("Error saving user data:", error);
      setError("שגיאה בשמירת נתוני המשתמש");
    }
  };

  // בדיקת אותנטיקציה בטעינת האפליקציה
  useEffect(() => {
    const initAuth = async () => {
      debugHelper.log('🔐 AuthContext: Starting initialization');
      console.log('🔐 AuthContext: Current URL:', window.location.href);

      // Skip auth initialization for public pages
      const currentPath = window.location.pathname;
      if (currentPath.startsWith('/sherutPlaces')) {
        debugHelper.log('🏢 Skipping auth initialization for public sherutPlaces page');
        setLoading(false);
        return;
      }

      setLoading(true);
      // Add timeout to prevent infinite loading
      const timeoutDuration = isIOSWebView() ? 5000 : 8000;
      const timeoutId = setTimeout(() => {
        debugHelper.log('⏰ Authentication timeout reached');
        console.warn("Authentication timeout reached");
        setLoading(false);
        
        if (isIOSWebView()) {
          console.log("iOS WebView timeout - checking local JWT validity");
          const userData = getSafeUserData();
          if (userData && !isJWTExpired(userData.SessionKey)) {
            const lastValidation = getLastValidationTimestamp();
            if (lastValidation && (Date.now() - lastValidation) < VALIDATION_GRACE_PERIOD) {
              setUser(userData);
            } else {
              toast.warning("פג תוקף החיבור - נא להתחבר מחדש");
              logout("");
            }
          }
        } else {
          setError("זמן טעינה חריג - נסו לרענן את הדף");
        }
      }, timeoutDuration);

      try {
        const userData = getSafeUserData();
        debugHelper.log('AuthContext: Checking userData', { hasUserData: !!userData });
        console.log("AuthContext: Initializing with userData:", userData ? "exists" : "null");

        if (userData) {
          console.log("AuthContext: Checking session validity...");

          try {
            const isSessionValid = await checkSessionValidity(userData);
            console.log("AuthContext: Session valid:", isSessionValid);

            if (isSessionValid) {
              setUser(userData);
              console.log("AuthContext: User set successfully");
            } else {
              console.log("AuthContext: Session invalid, clearing data");
              // ניקוי נתונים מקומיים
              localStorage.removeItem("userData");
              localStorage.removeItem("rakazid");
              localStorage.removeItem("sayeretid");
              clearLastValidationTimestamp();
              setUser(null);
            }
          } catch (sessionError) {
            console.error("Session check failed:", sessionError);
            
            // For iOS WebView, check JWT expiry before keeping user logged in
            if (isIOSWebView() && !isJWTExpired(userData.SessionKey)) {
              const lastValidation = getLastValidationTimestamp();
              if (lastValidation && (Date.now() - lastValidation) < VALIDATION_GRACE_PERIOD) {
                setUser(userData);
              } else {
                toast.warning("לא ניתן לאמת את החיבור - נא להתחבר מחדש");
              }
            }
          }
        } else {
          console.log("AuthContext: No user data found");
        }
      } catch (error) {
        console.error("Authentication initialization error:", error);
        setError("שגיאה באימות המשתמש - " + error.message);
      } finally {
        clearTimeout(timeoutId);
        setLoading(false);
        console.log("AuthContext: Initialization completed");
      }
    };

    initAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // בדיקה תקופתית של תוקף הסשן
  useEffect(() => {
    if (!user) return;

    // For iOS WebView, use less frequent checks
    const checkInterval = isIOSWebView() ? 30 * 60 * 1000 : 15 * 60 * 1000; // 30 min for iOS, 15 min for others

    const sessionCheckInterval = setInterval(async () => {
      try {
        // First check JWT expiry locally
        if (isJWTExpired(user.SessionKey)) {
          logout("פג תוקף החיבור שלך למערכת");
          return;
        }

        const isSessionValid = await checkSessionValidity(user);
        if (!isSessionValid) {
          logout("פג תוקף החיבור שלך למערכת");
        }
      } catch (error) {
        console.error("Periodic session check failed:", error);
        
        // For iOS WebView, check grace period before taking action
        if (isIOSWebView()) {
          const lastValidation = getLastValidationTimestamp();
          if (!lastValidation || (Date.now() - lastValidation) > 2 * 60 * 60 * 1000) {
            // More than 2 hours since last validation
            toast.warning("לא ניתן לאמת את החיבור - נא להתחבר מחדש");
            logout("");
          }
        }
      }
    }, checkInterval);

    return () => clearInterval(sessionCheckInterval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  return (
    <AuthContext.Provider value={{ user, loading, error, login, logout, checkSessionValidity }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
