<div class="panel panel-default">
    <!-- Default panel contents -->
    <div class="panel-heading">
        טבלת הפניות
        <small>
            <?php showInfo($total, $limit, $page); ?>
        </small>
    </div>
    
    <!-- Table -->
    <table class="table table-hover">
        <thead>
            <tr>
                <th class="text-center"><?php showOrder('id', '#'); ?></th>
                <th><?php showOrder('from', 'מכתובת'); ?></th>
                <th><?php showOrder('to', 'אל כתובת'); ?></th>
                <th class="text-center"></th>
            </tr>
        </thead>
        <tbody>
                <?php echo form_open_multipart('admin/' . $this->router->fetch_class() . '/put/' . getQS(), array("class" => "ajax")); ?>
                <tr id="row" role="row">
                    <td class="text-center">

                    </td>
                    <td>
                        <input type="text" dir="ltr" class="form-control" name="from">
                    </td>
                    <td>
                        <input type="text" dir="ltr" class="form-control" name="to">
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="submit" class="btn btn-primary">
                                הוסף
                            </button>
                        </div>
                    </td>
                </tr>
                <?php echo form_close(); ?>
            <?php if(isset($objects) && !empty($objects)) { foreach($objects as $row) { ?>
                <?php echo form_open('admin/' . $this->router->fetch_class() . '/update/' . $row->Id() . getQS(), array("class" => "ajax")); ?>
                <tr id="row<?php echo $row->Id(); ?>" role="row">
                    <td class="text-center">
                        <?php echo $row->Id(); ?>
                    </td>
                    <td>
                        <input type="text" dir="ltr" class="form-control" name="from" value="<?php echo urldecode($row->Arg('from')); ?>">
                    </td>
                    <td>
                        <input type="text" dir="ltr" class="form-control" name="to" value="<?php echo urldecode($row->Arg('to')); ?>">
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="submit" class="btn btn-success">
                                שמור
                            </button>
                            <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/' . $this->router->fetch_class() . '/status/' . $row->Id()); ?>">
                                <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                            </button> 
                            <button type="button" data-url="<?php echo base_url('admin/' . $this->router->fetch_class() . '/destroy/' . $row->Id() . getQS()); ?>" class="btn btn-danger delete">
                                מחק
                            </button>
                        </div>
                    </td>
                </tr>
                <?php echo form_close(); ?>
            <?php }} ?>
        </tbody>
    </table>
    
    <div class="row">
        <div class="col-md-12 text-center">
            <?php showPages($total, $limit, $page); ?>
        </div>
    </div>
</div>






