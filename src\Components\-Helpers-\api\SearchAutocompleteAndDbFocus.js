import React, { useState, useEffect, useCallback } from 'react';
import { ReactSearchAutocomplete } from 'react-search-autocomplete';
import getData<PERSON>rom<PERSON><PERSON> from '../../-Helpers-/api/getDataFromApi';

/**
 * SearchAutocompleteAndDbFocus component provides search autocomplete functionality with database integration
 * @param {Object} props - Component props
 * @param {Object} props.dbParams - Database parameters containing functionName and controller
 * @param {Function} props.submit - Callback function called when an item is selected
 * @param {Array} props.keys - Array of keys to search on in the autocomplete
 * @param {string} props.placeholder - Placeholder text for the search input
 * @returns {JSX.Element} Search autocomplete component
 */
const SearchAutocompleteAndDbFocus = ({ dbParams, submit, keys, placeholder }) => {
  const [searchItems, setSearchItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasInitialLoad, setHasInitialLoad] = useState(false);

  /**
   * Filters array to contain only unique items based on a key property
   * @param {Array} items - Array of items to deduplicate
   * @param {string} key - Property name to check for uniqueness
   * @returns {Array} Filtered array with unique items
   */
  const filterUniqueItems = (items, key = 'id') => {
    const seen = new Set();
    return items.filter(item => {
      const value = item[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  };

  /**
   * Makes API call to fetch search items from backend
   * @param {string} url - API endpoint URL
   * @param {string} controller - API controller name
   * @param {Object} objectToSend - Request payload
   * @param {string} setStateName - Name of state to update
   * @param {string} auth - Authorization level
   */
  const sendtoAdmin = useCallback(
    async (url, controller, objectToSend, setStateName = 'data', auth = 'all') => {
      if (loading) return; // Prevent multiple simultaneous requests

      setError(null);
      setLoading(true);

      try {
        const response = await getDataFromApi(url, objectToSend, controller, auth);

        if (response?.options) {
          // Filter out duplicate items before setting state
          const uniqueItems = filterUniqueItems(response.options);
          // Add unique index to each item to ensure unique keys in render
          const indexedItems = uniqueItems.map((item, index) => ({
            ...item,
            _uniqueIndex: index // Add a unique index property
          }));
          setSearchItems(indexedItems);
        } else {
          console.warn('Invalid response format:', response);
          setSearchItems([]);
        }
      } catch (error) {
        console.error('Error fetching search items:', error);
        setError(error.message);
        setSearchItems([]); // Clear items on error for consistent behavior
      } finally {
        setLoading(false);
      }
    },
    [loading]
  );



  // Load items only on first focus
  useEffect(() => {
    if (!hasInitialLoad && dbParams) {
      const { functionName, controller } = dbParams;
      sendtoAdmin(functionName, controller, {}, 'searchItems', 'all');
      setHasInitialLoad(true);
    }
  }, [dbParams, hasInitialLoad, sendtoAdmin]);

  /**
   * Handles selection of an item from search results
   * @param {Object} item - Selected item object
   */
  const handleOnSelect = (item) => {
    console.log('Search item selected:', item);
    submit(item);
  };

  /**
   * Handles focus event on search input
   * Triggers API call to fetch search items
   */
  const handleOnFocus = () => {
    if (!hasInitialLoad) {
      console.log('Search input focused, fetching items with params:', dbParams);
      const { functionName, controller } = dbParams;
      sendtoAdmin(functionName, controller, {}, 'searchItems', 'all');
      setHasInitialLoad(true);
    }
  };


  /**
   * Custom formatting for search result items to ensure unique keys
   * @param {Object} item - The search result item
   * @returns {JSX.Element} Formatted result item
   */
  const formatResult = (item) => {
    // Extract the main display text from the item based on first key
    const displayKey = keys && keys.length > 0 ? keys[0] : 'name';
    const displayText = item[displayKey] || 'Unknown';
    
    return (
      <div 
        key={`custom-result-${item._uniqueIndex}-${item.id}`}
        style={{
          padding: '10px 15px',
          borderBottom: '1px solid #f0f0f0',
          cursor: 'pointer',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          transition: 'background-color 0.2s',
          fontSize: '14px'
        }}
      >
        <div style={{ fontWeight: 'bold', direction: 'rtl' }}>{displayText}</div>
        {item.secondaryInfo && (
          <div style={{ color: '#777', fontSize: '12px', marginRight: '8px', direction: 'rtl' }}>
            {item.secondaryInfo}
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ position: 'relative', width: '100%' }}>
      {loading && !searchItems && (
        <div style={{ position: 'absolute', right: '10px', top: '50%', transform: 'translateY(-50%)', zIndex: 3 }}>
          <div style={{ width: '15px', height: '15px', border: '2px solid #ddd', borderTopColor: '#2684ff', borderRadius: '50%', animation: 'spin 1s linear infinite' }}></div>
          <style>{`
            @keyframes spin {
              to {transform: rotate(360deg);}
            }
          `}</style>
        </div>
      )}
      <ReactSearchAutocomplete
        fuseOptions={{ 
          keys,
          threshold: 0.3,
          distance: 100,
          shouldSort: true
        }}
        placeholder={loading ? 'טוען...' : error ? 'שגיאה בטעינה' : placeholder}
        items={searchItems}
        onFocus={handleOnFocus}
        onSelect={handleOnSelect}
        showClear={true}
        maxResults={200}
        styling={{
          height: '40px',
          border: '1px solid #ddd',
          borderRadius: '4px',
          backgroundColor: '#fff',
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)',
          hoverBackgroundColor: '#f8f8f8',
          color: '#212121',
          fontSize: '14px',
          fontFamily: 'inherit',
          iconColor: '#2684ff',
          lineColor: '#2684ff',
          placeholderColor: '#888',
          clearIconMargin: '3px 8px 0 0',
          searchIconMargin: '0 0 0 8px',
          zIndex: 2,
          direction: 'rtl'
        }}
        autoFocus={false}
        formatResult={formatResult}
        onSearch={(string, results) => {
          if (results.length === 0 && searchItems.length === 0) {
            handleOnFocus();
          }
        }}
      />
      {error && (
        <div style={{ color: 'red', fontSize: '12px', marginTop: '5px', textAlign: 'right' }}>
          {error}
        </div>
      )}
    </div>
  );
};

export default SearchAutocompleteAndDbFocus;
