<div class="" style="padding: 20px;">
    <div class="row">
        <div class="col-md-6">
            <a href="<?php echo base_url('admin/object/gallery_branch/show?return='.  current_url() . '&parent_selected=branch_id&parent_selected_value=' . $branch_id); ?>" class="btn btn-primary btn-block">
                הוספת תמונה חדשה
            </a>
            <hr/>
        </div>
        <?php if(isset($gallery) && $gallery) foreach ($gallery as $row) { ?>
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="form-group">
                        <img id="preview<?php echo $row->Id(); ?>" class="materialboxed" src="<?php echo $row->Img('image'); ?>" alt="preview" style="max-width: 100%;" data-width="0" data-height="0" data-input-name="img"/>
                    </div>
                    <div class="input-group">
                        <div class="input-group-btn">
                            <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/object/gallery_branch/status/' . $row->Id()); ?>">
                                <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                            </button> 
                        </div>
                        <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $row->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/object/gallery_branch/sort/' . $row->Id() . '/'); ?>">
                        <div class="input-group-btn">
                            <a class="btn btn-default pulse-hover" href="<?php echo base_url('admin/object/gallery_branch/show/' . $row->Id() . '?return='.  current_url()); ?>" role="button">עריכה</a>

                            <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/object/gallery_branch/destroy/' . $row->Id() . '/' . getQS()); ?>">
                                מחק
                            </button> 
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php } ?>
    </div>

</div>
