<style>
.panel-body {
    padding: 5px 15px;
}
.row {
    margin-right: -5px;
    margin-left: -5px;
}
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
    
    padding-left: 5px;
    padding-right: 5px;
}
</style>
<div class="row">
    <?php if(isset($fields) && $fields) foreach ($fields as $row) { ?>
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="row">
                <?php 
                $pval = $row->Arr('pval');
                ?>
                <?php $action = isset($pval) && $pval ? 'admin/object/product_field/update/' . $pval->Id() . '/' : 'admin/object/product_field/put/'; ?>
                <?php echo form_open_multipart($action . getQS(), array("class" => "form-inline ajax")); ?>
                    <div class="form-group col-md-4">
                        <select class="form-control select2ajax" style="width: 100%;" name="en_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_field'); ?>" data-ajax--cache="true" data-ajax--id="en_value" data-ajax--text="en_value" data-placeholder="<?php echo $row->Arg('en_name'); ?>" data-allow-clear="true">
                            <?php if(isset($pval) && $pval){?>
                            <option value="<?php echo $pval->Arg('en_value'); ?>" selected><?php echo $pval->Arg('en_value'); ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group col-md-4">
                        <select class="form-control select2ajax" style="width: 100%;" name="th_value" data-tags="true" data-ajax--url="<?php echo base_url('admin/store/search_value_field'); ?>" data-ajax--cache="true" data-ajax--id="th_value" data-ajax--text="th_value" data-placeholder="<?php echo $row->Arg('th_name'); ?>" data-allow-clear="true">
                            <?php if(isset($pval) && $pval){?>
                            <option value="<?php echo $pval->Arg('th_value'); ?>" selected><?php echo $pval->Arg('th_value'); ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <?php if(isset($pval) && $pval) { ?>
                        <div class="input-group">
                            <div class="input-group-btn">
                                <button type="button" class="btn pulse-hover <?php echo $pval->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/object/product_field/status/' . $pval->Id()); ?>">
                                    <?php echo $pval->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                                </button> 
                            </div>
                            <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $pval->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/object/product_field/sort/' . $pval->Id() . '/'); ?>">
                        </div>
                        <?php } ?>
                    </div>
                    <div class="form-group col-md-2">
                        <div class="btn-group" role="group">
                            <button type="submit" class="btn <?php echo isset($pval) && $pval ? "btn-success" : "btn-primary"; ?>"><?php echo isset($pval) && $pval ? "עדכן" : "הוסף"; ?></button>
                            <?php if(isset($pval) && $pval) { ?>
                            <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/object/product_field/destroy/' . $pval->Id() . '/' . getQS()); ?>">
                                מחק
                            </button> 
                            <?php } ?>
                        </div>
                    </div>
                    <input type="hidden" name="product_id" value="<?php echo $product_id; ?>"/>
                    <input type="hidden" name="field_id" value="<?php echo $row->Id(); ?>"/>
                <?php echo form_close(); ?>
                </div>
            </div>
        </div>
    </div>
    <?php } ?>
</div>