<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class ObjectAdmin extends CI_Controller {
    
    private $object;
    
    public function __construct() {
        parent::__construct();
        
        //if is not logged in die  
        if( ! $this->aauth->is_loggedin() ) { 
            redirect(base_url('admin?redirect=' . current_url() . getQS()), 'refresh', 401);
            die('The user is not connected.'); 
        } else if( ! $this->aauth->is_allowed($this->router->fetch_class() . '_' . $this->router->fetch_method())) {
            die('The user not have permission to view the content.'); 
        }
        $this->load->model('msite');
        
        
    }
    
    public function _remap($object, $params = array()) {
        if($object !== 'index') {
            $object_file = $object ? read_file(APPPATH . 'migration/' . $object . '.json') : FALSE;
            $this->object = $object_file ? json_decode($object_file, TRUE) : FALSE;
            
            $method = isset($params[0]) ? $params[0] : FALSE;
            $param = isset($params[1]) ? $params[1] : FALSE;
            
            if($method) {
                $this->$method($param);
            } else {
                die();
            }
            
        } else {
            $param = isset($params[0]) ? $params[0] : FALSE;
            $this->index($param);
        }
    }
    
    public function index($migration = FALSE) {
        $data['migration_file'] = $migration;
        
        echo "sebas: ".$migration;
        
        $migration_file = $migration ? read_file(APPPATH . 'migration/' . $migration . '.json') : FALSE;
        $data['migration'] = $migration_file ? json_decode($migration_file, TRUE) : FALSE;
        $table = $data['migration']['table'];
        
        $data['page'] = $this->input->get('page') ? $this->input->get('page') : 1;
        $data['limit'] = $this->input->get('limit') ? $this->input->get('limit') : 12;
        $data['order'] = $this->input->get('order') ? $this->input->get('order') : 'sort';
        $data['sort'] = $this->input->get('sort') ? $this->input->get('sort') : 'DESC';

        $data['Id'] = $this->input->get('Id');
        if($data['Id']) {
            $data['limit'] = 200;
        }
        //print_r($data['migration']['method']);die();
        
        
        
        $data['total'] = $this->msite->count_all_objects($table);
        $this->msite->limit_objects($data['page'], $data['limit']);
        $this->msite->sort_objects($data['order'], $data['sort']);
        
        if($data['Id'] and ( $data['migration']['method']=='galleryProj' or $data['migration']['method']=='plans' ) ) {
            $where = 'projId='.$data['Id'];
            $this->msite->set_where($where);
        }
        
        $data['objects'] = $this->msite->get_all_objects($table);
        
        $data['view'] = $this->router->fetch_class() . '/index';
        $data['script'] = $this->router->fetch_class() . '/script';
        
        //print_r($data['objects']);
        
        $this->load->view('admin/index', $data);
    }
    
    public function show($object_id = FALSE) {
        $data['migration'] = $this->object;
        $table = $data['migration']['table'];
        
        $data['Id'] = $this->input->get('Id');
        
        $otobusId = $this->input->get('Id');
        if($otobusId) {
            echo $otobusId;
        }
        
        $data['object'] = $object_id ? $this->msite->get_object($table, $object_id) : NULL;
        if(isset($this->object['fields']) && !empty($this->object['fields'])) {
            foreach($this->object['fields'] as $field) {
                if($field['type'] === 'table') {
                    $data['options'][$field['name']] = $this->msite->get_all_objects($field['options']['table']);
                } else if( $field['type'] === 'multipleTable' && !empty ($field['options']['table']) ) {
                    $data['options'][$field['name']] = $this->msite->get_all_objects($field['options']['table']);
                }
            }
        }
        
        if($this->input->get('parent_selected')) {
            $data['parent_selected'][$this->input->get('parent_selected')] = $this->input->get('parent_selected_value');
        }
        
        $data['view'] = $this->router->fetch_class() . '/object';
        $data['script'] = $this->router->fetch_class() . '/script';
        $this->load->view('admin/index', $data);
    }
    
    public function search_in_objects($migration = FALSE) {
        $limit = 100;
        $q = $this->input->get('q');
        $field_to_search = $this->input->get('field');
        $table = $this->input->get('table');
        $data['objects'] = $this->msite->searching($table, $field_to_search, $q, $limit);
        
        return $this->output
                ->set_status_header('200')
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function put() {
        $data = array(
            'lang' => $this->input->post('lang') ? $this->input->post('lang') : NULL,
            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($this->object['table'], 'sort') + 10,
            'status' => $this->input->post('status') && $this->input->post('status') > 0 ? 1 : 0,
            'created_at' => date("Y-m-d H:i:s"),
        );
        
        if(isset($this->object['fields']) && !empty($this->object['fields'])) {
            foreach($this->object['fields'] as $field) {
                switch($field['type']) {
                    case "multiple":
                        $data[$field['name']] = $this->input->post($field['name']) ? implode(",", $this->input->post($field['name'])) : "";
                        break;
                    case "multipleTable":
                        $data[$field['name']] = $this->input->post($field['name']) ? implode("_|_", $this->input->post($field['name'])) : "";
                        break;
                    case "boolean":
                        $data[$field['name']] = $this->input->post($field['name']) ? TRUE : FALSE;
                        break;
                    case "date":
                        $data[$field['name']] = $this->input->post($field['name']) ? changeDateFormat($this->input->post($field['name'])) : NULL;
                        break;
                    case "time":
                        $data[$field['name']] = $this->input->post($field['name']) ? changeDateFormat($this->input->post($field['name'])) : NULL;
                        break;
                    case "datetime":
                        $data[$field['name']] = $this->input->post($field['name']) ? changeDateFormat($this->input->post($field['name'])) : NULL;
                        break;
                    case "image":
                        $config['upload_path']          = apppicIMG;
                        $config['allowed_types']        = 'gif|jpg|png';
                        $config['max_size']             = 0;
                        $config['max_width']            = 0;
                        $config['max_height']           = 0;
                        $config['file_name']            = uniqid();

                        $this->load->library('upload', $config);
                        $this->upload->initialize($config, TRUE);
                        if($this->upload->do_upload($field['name'])) {
                            $filedata = $this->upload->data(); 
                            $data[$field['name']] = $filedata['file_name'];
                            ini_set('memory_limit', '-1');
                            
                            $x_axis = $this->input->post($field['name'] . '_x');
                            $y_axis = $this->input->post($field['name'] . '_y');
                            $_width = $this->input->post($field['name'] . '_w');
                            $_height = $this->input->post($field['name'] . '_h');
                            $quality = 70;
                            
                            $this->load->library('simple_img');
                            $smpimg = $this->simple_img->initialize(apppicIMG . $data[$field['name']]);
                            
                            
                            
                            if($this->input->post($field['name'] . '_crop')) {
                                $smpimg->crop($x_axis, $y_axis, $_width + $x_axis, $_height + $y_axis)
                                    ->save(apppicIMG . $data[$field['name']], $quality);
                            }
                            
                            
                            
                            if (isset($field['options']['width']) && !empty($field['options']['width'])) {
                                
                                //die('sebas');
                                
                                $smpimg ->auto_orient()
                                //->best_fit(3840, 2160)
                                //->best_fit($_width, $_height)
                                ->best_fit($field['options']['width'], $field['options']['height'])
                                ->save(apppicIMG . $data[$field['name']], $quality);
                            } else {
                                $smpimg ->auto_orient()
                                ->best_fit(3840, 2160)
                                //->best_fit(1920, 1080)
                                ->save(apppicIMG . $data[$field['name']], $quality);
                            }
                            
                            
//                            $smpimg ->auto_orient()
//                                    //->best_fit(1920, 1080)
//                                    ->best_fit(3840, 2160)
//                                    ->save(apppicIMG . $data[$field['name']], $quality);
                        }
                        break;
                    case "file":
                        $config['upload_path']          = appFILES;
                        $config['allowed_types']        = 'pdf';
                        $config['file_name']            = uniqid();

                        $this->load->library('upload', $config);
                        $this->upload->initialize($config, TRUE);
                        if($this->upload->do_upload($field['name'])) {
                            $filedata = $this->upload->data(); 
                            $data[$field['name']] = $filedata['file_name'];
                        }
                        break;
                    default :
                        $data[$field['name']] = $this->input->post($field['name']);
                }
            }
        }
        
        if($insert_id = $this->msite->insert_object($this->object['table'], $data)) {
            if(isset($this->object['controller']) && !empty($this->object['controller']) && 
                    isset($this->object['method']) && !empty($this->object['method']) && 
                    isset($this->object['seo_title']) && !empty($this->object['seo_title']) && 
                    isset($this->object['seo_description']) && !empty($this->object['seo_description'])) {
                $data_seo = array(
                    'title' => $this->input->post($this->object['seo_title']),
                    'description' => strip_tags($this->input->post($this->object['seo_description'])),
                    'friendly' => $this->input->post('friendly') ? $this->input->post('friendly') : url_title($this->input->post($this->object['seo_title']) . '_' . $insert_id),
                    'controller' => $this->object['controller'],
                    'method' => $this->object['method'],
                    'param' => $insert_id,
                    'robots' => $this->input->post('robots') ? $this->input->post('robots') : 'index, follow',
                    'image' => ($this->input->post('image') ? $this->input->post('image') : isset($data[$this->object['image']])) ? base_url(picIMG . $data[$this->object['image']]) : NULL,
                    'canonical' => base_url($this->object['controller'] . '/' . $this->object['method'] . '/' . $insert_id),
                );
                if($langs = $this->config->item('available_lang')) {
                    $field_name = $field['name'];
                    foreach ($langs as $lang_key => $lang_value) {
                        $data_seo[$lang_key . '_title'] = $this->input->post($lang_key . '_' . $this->object['seo_title']);
                        $data_seo[$lang_key . '_description'] = strip_tags($this->input->post($lang_key . '_' . $this->object['seo_description']));
                        $data_seo[$lang_key . '_friendly'] = url_title($this->input->post($lang_key . '_' . $this->object['seo_title']) . '_' . $insert_id);
                    }
                }

                $seo_id = $this->msite->insert_object(Msite::TABLE_SEO, $data_seo); 
                $this->msite->update_object($this->object['table'], $insert_id, array('seo_id' => $seo_id));
            }
            
            $data['id'] = $insert_id;
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function update($update_id=FALSE) {
        
        if(empty($update_id)) {
            $update_id = $this->input->get('update_id');
        }
        
        $obj = $this->msite->get_object($this->object['table'], $update_id);
        $data = array(
            'lang' => $this->input->post('lang') ? $this->input->post('lang') : $obj->Arg('lang'),
        );
        
        //print_r($this->object['fields']);die();
        
        if(isset($this->object['fields']) && !empty($this->object['fields'])) {
            foreach($this->object['fields'] as $field) {
                switch($field['type']) {
                    case "multiple":
                        $data[$field['name']] = $this->input->post($field['name']) ? implode(",", $this->input->post($field['name'])) : "";
                        break;
                    case "multipleTable":
                        $data[$field['name']] = $this->input->post($field['name']) ? implode("_|_", $this->input->post($field['name'])) : "";
                        break;
                    case "boolean":
                        $data[$field['name']] = $this->input->post($field['name']) ? TRUE : FALSE;
                        break;
                    case "date":
                        $data[$field['name']] = $this->input->post($field['name']) ? changeDateFormat($this->input->post($field['name'])) : NULL;
                        break;
                    case "time":
                        $data[$field['name']] = $this->input->post($field['name']) ? changeDateFormat($this->input->post($field['name'])) : NULL;
                        break;
                    case "datetime":
                        $data[$field['name']] = $this->input->post($field['name']) ? changeDateFormat($this->input->post($field['name'])) : NULL;
                        break;
                    case "image":
                        $config = array();
                        $config['upload_path']          = apppicIMG;
                        $config['allowed_types']        = 'gif|jpg|png';
                        $config['max_size']             = 0;
                        $config['max_width']            = 0;
                        $config['max_height']           = 0;
                        $config['file_name']            = uniqid();

                        $this->load->library('upload', $config);
                        $this->upload->initialize($config, TRUE);
                        
                        ini_set('memory_limit', '-1');
                            
                        $x_axis = $this->input->post($field['name'] . '_x');
                        $y_axis = $this->input->post($field['name'] . '_y');
                        $_width = $this->input->post($field['name'] . '_w');
                        $_height = $this->input->post($field['name'] . '_h');
                        $quality = 70;

                        if($this->upload->do_upload($field['name'])) {
                            if($obj->Img($field['name'])) {
                                unlink(apppicIMG . $obj->Arg($field['name']));
                            }
                            $filedata = $this->upload->data(); 
                            $data[$field['name']] = $filedata['file_name'];
                            
                            
                            $this->load->library('simple_img');
                            $smpimg = $this->simple_img->initialize(apppicIMG . $data[$field['name']]);
                            
                            
                            if($this->input->post($field['name'] . '_crop')) {
                                
                                //die('sebas');
                                
                                $smpimg->crop($x_axis, $y_axis, $_width + $x_axis, $_height + $y_axis)
                                    ->save(apppicIMG . $data[$field['name']], $quality);
                            }
                            
                            if (isset($field['options']['width']) && !empty($field['options']['width'])) {
                                
                                //die('sebas');
                                
                                $smpimg ->auto_orient()
                                //->best_fit(3840, 2160)
                                //->best_fit($_width, $_height)
                                ->best_fit($field['options']['width'], $field['options']['height'])
                                ->save(apppicIMG . $data[$field['name']], $quality);
                            } else {
                                $smpimg ->auto_orient()
                                ->best_fit(3840, 2160)
                                //->best_fit(1920, 1080)
                                ->save(apppicIMG . $data[$field['name']], $quality);
                            }
                            
                            
                            
                            
                            
                        } else if($this->input->post($field['name'] . '_crop')) {
                            $this->load->library('simple_img');
                            $smpimg = $this->simple_img->initialize(apppicIMG . $obj->Arg($field['name']));
                            $smpimg ->crop($x_axis, $y_axis, $_width + $x_axis, $_height + $y_axis)
                                    ->auto_orient()
                                    //->best_fit(3840, 2160)
                                    ->best_fit(1920, 1080)
                                    ->save(apppicIMG . $obj->Arg($field['name']), $quality);
                            
                        }
                        break;
                    case "file":
                        $config = array();
                        $config['upload_path']          = appFILES;
                        $config['allowed_types']        = 'pdf';
                        $config['file_name']            = uniqid();

                        $this->load->library('upload', $config);
                        $this->upload->initialize($config, TRUE);
                        if($this->upload->do_upload($field['name'])) {
                            if($obj->File($field['name'])) {
                                unlink(appFILES . $obj->Arg($field['name']));
                            }
                            $filedata = $this->upload->data(); 
                            $data[$field['name']] = $filedata['file_name'];
                        }
                        break;
                    default :
                        $data[$field['name']] = $this->input->post($field['name']);
                }
            }
        }
        
        if($this->msite->update_object($this->object['table'], $update_id, $data)) {
            if(isset($this->object['controller']) && !empty($this->object['controller']) && 
                    isset($this->object['method']) && !empty($this->object['method']) && 
                    isset($this->object['seo_title']) && !empty($this->object['seo_title']) && 
                    isset($this->object['seo_description']) && !empty($this->object['seo_description'])) {
                $data_seo = array(
                    'title' => $this->input->post($this->object['seo_title']),
                    'description' => strip_tags($this->input->post($this->object['seo_description'])),
                    'friendly' => $this->input->post('friendly') ? $this->input->post('friendly') : url_title($this->input->post($this->object['seo_title']) . '_' . $update_id),
                    'controller' => $this->object['controller'],
                    'method' => $this->object['method'],
                    'param' => $update_id,
                    'robots' => $this->input->post('robots') ? $this->input->post('robots') : 'index, follow',
                    'image' => ($this->input->post('image') ? $this->input->post('image') : isset($data[$this->object['image']])) ? base_url(picIMG . $data[$this->object['image']]) : NULL,
                    'canonical' => base_url($this->object['controller'] . '/' . $this->object['method'] . '/' . $update_id),
                );
                if($langs = $this->config->item('available_lang')) {
                    $field_name = $field['name'];
                    foreach ($langs as $lang_key => $lang_value) {
                        $data_seo[$lang_key . '_title'] = $this->input->post($lang_key . '_' . $this->object['seo_title']);
                        $data_seo[$lang_key . '_description'] = strip_tags($this->input->post($lang_key . '_' . $this->object['seo_description']));
                        $data_seo[$lang_key . '_friendly'] = url_title($this->input->post($lang_key . '_' . $this->object['seo_title']) . '_' . $update_id);
                    }
                }
                $this->msite->update_object(Msite::TABLE_SEO, $obj->Arg('seo_id'), $data_seo); 
            }
            
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function duplicate($clone_id) {
        $obj = $this->msite->get_object($this->object['table'], $clone_id);
        $data = array(
            'lang' => $obj->Arg('lang'),
            'sort' => $this->input->post('sort') ? $this->input->post('sort') : $this->msite->get_max($this->object['table'], 'sort') + 10,
            'status' => $this->input->post('status') && $this->input->post('status') > 0 ? 1 : 0,
            'created_at' => date("Y-m-d H:i:s"),
        );
        
        if(isset($this->object['fields']) && !empty($this->object['fields'])) {
            foreach($this->object['fields'] as $field) {
                switch($field['type']) {
                    case "image":
                        if($obj->Img($field['name'])) {
                            $new_name = uniqid() . $obj->Arg($field['name']);
                            if ( copy(apppicIMG . $obj->Arg($field['name']), apppicIMG . $new_name) ) {
                                $data[$field['name']] = $new_name;
                            }
                        }
                        break;
                    case "file":
                        if($obj->File($field['name'])) {
                            $new_name = uniqid() . $obj->Arg($field['name']);
                            if ( copy(appFILES . $obj->Arg($field['name']), appFILES . $new_name) ) {
                                $data[$field['name']] = $new_name;
                            }
                        }
                        break;
                    default :
                        $data[$field['name']] = $obj->Arg($field['name']);
                }
            }
        }
        
        if($insert_id = $this->msite->insert_object($this->object['table'], $data)) {
            if(isset($this->object['controller']) && !empty($this->object['controller']) && 
                    isset($this->object['method']) && !empty($this->object['method']) && 
                    isset($this->object['seo_title']) && !empty($this->object['seo_title']) && 
                    isset($this->object['seo_description']) && !empty($this->object['seo_description'])) {
                $data_seo = array(
                    'title' => $obj->Arg($this->object['title']),
                    'description' => strip_tags($obj->Arg($this->object['description'])),
                    'controller' => $this->object['controller'],
                    'method' => $this->object['method'],
                    'param' => $insert_id,
                    'robots' => $this->input->post('robots') ? $this->input->post('robots') : 'index, follow',
                    'friendly' => $this->input->post('friendly') ? $this->input->post('friendly') : url_title($obj->Arg($this->object['title']) . '_' . $insert_id),
                    'image' => ($this->input->post('image') ? $this->input->post('image') : isset($data[$this->object['image']])) ? base_url(picIMG . $data[$this->object['image']]) : NULL,
                    'canonical' => base_url($this->object['controller'] . '/' . $this->object['method'] . '/' . $insert_id),
                );
                if($langs = $this->config->item('available_lang')) {
                    $field_name = $field['name'];
                    foreach ($langs as $lang_key => $lang_value) {
                        $data_seo[$lang_key . '_title'] = $obj->Arg($lang_key . '_' . $this->object['seo_title']);
                        $data_seo[$lang_key . '_description'] = strip_tags($obj->Arg($lang_key . '_' . $this->object['seo_description']));
                        $data_seo[$lang_key . '_friendly'] = url_title($obj->Arg($lang_key . '_' . $this->object['seo_title']) . '_' . $insert_id);
                    }
                }

                $seo_id = $this->msite->insert_object(Msite::TABLE_SEO, $data_seo); 
                $this->msite->update_object($this->object['table'], $insert_id, array('seo_id' => $seo_id));
            }
            
            $data['id'] = $insert_id;
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function destroy($delete_id) {
        $obj = $this->msite->get_object($this->object['table'], $delete_id);
        
        if(isset($this->object['fields']) && !empty($this->object['fields'])) {
            foreach($this->object['fields'] as $field) {
                switch($field['type']) {
                    case "image":
                        if($obj->Img($field['name'])) {
                            unlink(apppicIMG . $obj->Arg($field['name']));
                        }
                        break;
                    case "file":
                        if($obj->File($field['name'])) {
                            unlink(appFILES . $obj->Arg($field['name']));
                        }
                        break;
                    default :
                }
            }
        }
        
        if($obj->Arg('seo_id')) {
            $this->msite->delete_seo($obj->Arg('seo_id'));
        }
        
        if($this->msite->delete_object($this->object['table'], $delete_id)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function status($update_id) {
        $obj = $this->msite->get_object($this->object['table'], $update_id);
        $status = $obj->Arg('status') > 0 ? 0 : 1;
        if($this->msite->update_object($this->object['table'], $update_id, array("status" => $status))) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function sort($update_id) {
        $sort = $this->input->post_get('sort');
        echo $sort;
        if($this->msite->update_object($this->object['table'], $update_id, array("sort" => $sort))) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    
    
    
    
}

