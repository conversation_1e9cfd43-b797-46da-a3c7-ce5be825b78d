<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('token'));
           if( $postCode['token'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    public function getUserData($jPData = FALSE) {
        
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('users'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas') ) { $output['error'] = 'שגיאה'; return $this->output ->set_status_header(403); }
        
        $getPosts = array(
            'idno',
            'SessionKey'
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        
        if( $this->sherutLeumi->checkSessionKey($pagePosts['idno'], $pagePosts['SessionKey']) ) {
            
            $output = 'unauthorized';
            
            return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($output));
            
        };
        
        $data = array(
            'idno' => $pagePosts['idno'],
            'SessionKey' => $pagePosts['SessionKey']
        );
        
        $userData = $this->sherutLeumi->apiPost($url = 'v2/volunteer/info/get', $data );
        
        $data1 = array('IDNumber' => $pagePosts['idno'],'SessionKey' => $pagePosts['SessionKey']);
        $documentsList = $this->sherutLeumi->apiPost($url = 'v1/volunteer/forms/list', $data1 );
        
        $is_tzForm = false;
        if( isset($documentsList['Forms']) && !empty($documentsList['Forms'])) {
            
            foreach ($documentsList['Forms'] as $key => $value) {
                
                if(isset($value['FormID']) && $value['FormID'] == '1') {
                    
                    if( isset($value['Status']) && $value['Status'] == "Exist" ) {
                        
                         $is_tzForm = true;
                        
                    }
                    
                }
                
            }
            
        }
        
        
        $userData['PrvSchoolText'] = $this->sherutLeumi->getNameFromIdApi($userData['prvschool'],'schools');
        
        $userData['BirthDate'] = changeDateFormat($userData['birthdate'], "Ymd", "Y-m-d");
        $userData['BirthDateText'] = changeDateFormat($userData['birthdate'], "Ymd", "d-m-Y");
        //$userData['AllDocumentsList'] = isset($documentsList['Forms']) ? $documentsList['Forms'] : array();
        $userData['is_tzForm'] = $is_tzForm;
        
        
        $currentYear = date("Y");
        $firstYear = $currentYear - 8;
        $lastYear = $currentYear + 1;
        
        
        
        for ($index = $firstYear; $index <= $lastYear; $index++) {
            
            $yearValue = $this->sherutLeumi->showHebrewYear(array('1','1',$index));
            
            $years[] = array(
                //'id' => $index,
                'id' => $yearValue,
                'value' => $yearValue
            );
            
        }
        
        
        $userData['years'] = array_reverse($years);
        $output['userData'] = $userData;
        
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    






    public function editUserData($jPData = FALSE) {
        
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('users'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas') ) { $output['error'] = 'שגיאה'; return $this->output ->set_status_header(403); }
        
        $getPosts = array(
            'idno',
            'SessionKey',
            
            //first-------------------
            'BirthDate',
            'BirthDateText',
            'CityCode',
            'Email',
            'FirstName',
            'LastName',
            'Mobile',
            'PrvSchool',
            'PrvSchoolText',
            'sex',
            //END first-------------------
            
            //seccond ---------------------
            'city',
            'street',
            'appartment',
            'zip',
            'mwnumber',
            // END seccond
            
            //3
            'relativename',
            'relativephone',
            'relativerelation',
            // END 3
            
            //4
            'sh_year1',
            // END 4
            
            //5
            'YearYad',
            'Category'
            
        );
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);
        
        if( $this->sherutLeumi->checkSessionKey($pagePosts['idno'], $pagePosts['SessionKey']) ) {
            
            $output = 'unauthorized';
            
            return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($output));
            
        };
        
        if(!empty($pagePosts['FirstName'])) {
            $action = 'step1_PersonalInformation';
        }
        else if(!empty($pagePosts['city'])) {
            $action = 'step2_Address';
        }
        else if(!empty($pagePosts['relativename'])) {
            $action = 'step3_Emergency';
        }
        else if(!empty($pagePosts['sh_year1'])) {
            $action = 'step4_Studies';
        }
        else if(!empty($pagePosts['YearYad'])) {
            $action = 'step5_Sherut';
        }
        
        else {
            $output['error'] = 'שגיאה';
            return $this->output ->set_status_header(403);
        }
        
        
        
        
        
        $editUserData = $this->sherutLeumi->editUserDataOnApi($pagePosts,$action);
        
        if($editUserData) {
            
            $output['newUserData'] = $editUserData;
            $output['ok'] = "הפרטים נשמרו בהצלחה";
            
        } else {
            
            $output['error'] = "משהו השתבש. נסה  sשוב מאוחר יותר";
            //$output['response'] = 'Error';
            
        }
        
        
        
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function getAvatarImage($jPData = FALSE) {
        
        
        $this->_loaderWS(); //$this->router->fetch_class();
        $getPosts = array('idno');
        
        $pagePosts = $this->msiteWs->getPostFromJson($getPosts);

        $output['base64Pic'] = '';
        
        if( !empty($pagePosts['idno']) ) {
            
            $this->db->select('base64Pic');
            $this->db->from('closeAppSaves');
            $this->db->where('idno', $pagePosts['idno']);
            $this->db->where('base64Pic != ', null);
            $this->db->order_by('id',"DESC");

            $result= $this->db->get();
            $data = $result->row_array();
            
            $output['base64Pic'] = isset($data['base64Pic']) ? $data['base64Pic'] : '';
            
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
}