import React from 'react';
import thum from "./../../../img/sherut-leumi/svg/sherutPlaces/card/thum.svg";
import garin from "./../../../img/sherut-leumi/svg/sherutPlaces/card/garin.svg";
import tekenB from "./../../../img/sherut-leumi/svg/sherutPlaces/card/teken.svg";
import tekenH from "./../../../img/sherut-leumi/svg/sherutPlaces/card/tekenH.svg"; 
import tekenP from "./../../../img/sherut-leumi/svg/sherutPlaces/card/tekenP.svg";

const ShowDataInRow = ({ item }) => {
  return (
    <div className="bg-white rounded-lg p-4 mb-4">
      <header className="mb-3 border-b pb-2">
        <h2 className="text-xl font-bold text-blue-700">{item.MOSADNA || 'שם מקום - ' + item.MOSADNA}</h2>
      </header>
      
      <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {item.City_Value && (
          <li key={`city-${item.ID || item.id || item.City_Value}`} className="flex items-center p-2 bg-blue-100 rounded-md col-span-full">
            <div className="bg-blue-200 p-1.5 rounded-md ml-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-700" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <span className="text-blue-800 font-medium text-lg">עיר: </span>
              <span className="text-blue-700 font-bold">{item.City_Value}</span>
            </div>
          </li>
        )}

        <li key={`thum-${item.id || item.original_ids?.[0] || 'thum'}`} className="flex items-center p-2 bg-blue-50 rounded-md">
          <div className="bg-blue-100 p-1.5 rounded-md mr-2">
            <img src={thum} alt="" className="h-5 w-5" />
          </div>
          <div>
            <span className="text-gray-700 font-medium">תחום: </span>
            <span className="text-blue-700">
              {item?.Thum?.value2 === "0" || !item?.Thum?.value2 ? "" : ` ${item?.Thum?.value2}`}
            </span>
          </div>
        </li>

        <li key={`year-${item.id || item.original_ids?.[0] || item.YEAR || 'year'}`} className="flex items-center p-2 bg-green-50 rounded-md">
          <div className="bg-green-100 p-1.5 rounded-md mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <span className="text-gray-700 font-medium">שנה: </span>
            <span className="text-green-700">{item.YEAR}</span>
          </div>
        </li>

        {parseInt(item.TEKENB) > 0 && (
          <li key={`tekenb-${item.id || item.original_ids?.[0] || item.TEKENB || 'tekenb'}`} className="flex items-center p-2 bg-purple-50 rounded-md">
            <div className="bg-purple-100 p-1.5 rounded-md mr-2">
              <img src={tekenB} alt="" className="h-5 w-5" />
            </div>
            <div>
              <span className="text-gray-700 font-medium">תקן: בית - </span>
              <span className="text-purple-700 font-semibold">{item.TEKENB}</span>{" "}
              <span className="text-purple-600 ml-1 text-sm">פנויים</span>
            </div>
          </li>
        )}

        {parseInt(item.TEKENP) > 0 && (
          <li key={`tekenp-${item.id || item.original_ids?.[0] || item.TEKENP || 'tekenp'}`} className="flex items-center p-2 bg-pink-50 rounded-md">
            <div className="bg-pink-100 p-1.5 rounded-md mr-2">
              <img src={tekenP} alt="" className="h-5 w-5" />
            </div>
            <div>
              <span className="text-gray-700 font-medium">תקן: פנימיה - </span>
              {" "}
              <span className="text-pink-700 font-semibold">{item.TEKENP}</span>
              {" "}
              <span className="text-pink-600 ml-1 text-sm">פנויים</span>
            </div>
          </li>
        )}

        {parseInt(item.TEKENH) > 0 && (
          <li key={`tekenh-${item.id || item.original_ids?.[0] || item.TEKENH || 'tekenh'}`} className="flex items-center p-2 bg-orange-50 rounded-md">
            <div className="bg-orange-100 p-1.5 rounded-md mr-2">
              <img src={tekenH} alt="" className="h-5 w-5" />
            </div>
            <div>
              <span className="text-gray-700 font-medium">תקן: חוץ - </span>
              <span className="text-orange-700 font-semibold">{item.TEKENH}</span>
              {" "}
              <span className="text-orange-600 ml-1 text-sm">פנויים</span>
            </div>
          </li>
        )}

        {item.grain === "1" && (
          <li key={`grain-${item.id || item.original_ids?.[0] || 'grain'}`} className="flex items-center p-2 bg-amber-50 rounded-md">
            <div className="bg-amber-100 p-1.5 rounded-md mr-2">
              <img src={garin} alt="" className="h-5 w-5" />
            </div>
            <div>
              <span className="text-amber-700 font-medium">גרעין</span>
            </div>
          </li>
        )}
      </ul>
    </div>
  );
};

export default ShowDataInRow;
