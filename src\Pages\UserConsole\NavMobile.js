import React, { useState, useEffect } from "react";
import NavMobileMenuContent from "./NavMobileMenuContent";
import m_Open from "../../img/sherut-leumi/svg/m_openMenu.svg";
import { createPortal } from "react-dom";

export default function NavMobile(props) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Close the menu when Escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen]);

  // Prevent scrolling when drawer is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <>
      {window.location.origin !== "https://sherut-leumi-app.vercel.app" && (
        <button 
          className="mobileOpenBtn btn-hover-effect focus:outline-none"
          onClick={() => setIsOpen(true)}
          aria-label="פתח תפריט"
        >
          <img src={m_Open} alt="לפתוח תפריט" className="pulse" />
        </button>
      )}

      {/* Backdrop */}
      {isOpen && createPortal(
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
          onClick={handleClose}
          aria-hidden="true"
        />,
        document.body
      )}

      {/* Drawer */}
      <div 
        className={`fixed inset-y-0 right-0 w-[280px] bg-[#0c213a] text-white z-50 shadow-xl transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        role="dialog"
        aria-modal="true"
        aria-label="תפריט נייד"
      >
        {isOpen && (
          <div className="h-full overflow-y-auto">
            <NavMobileMenuContent 
              allProps={props} 
              onClose={handleClose}
            />
          </div>
        )}
      </div>
    </>
  );
}
