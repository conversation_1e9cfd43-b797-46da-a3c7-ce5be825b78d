import axios from 'axios';
import { toast } from 'react-toastify';
import { getSafeUserData } from "../context/AuthContext";
import { logApiError, ErrorContexts, ErrorLevels } from './errorLogger';
import moment from 'moment-timezone';

// Get environment variables
const environment = process.env.REACT_APP_ENVIRONMENT;
const API_BASE_URL = environment === 'dev' ? process.env.REACT_APP_API_BASE_URL_DEV : process.env.REACT_APP_API_BASE_URL;

// Get user's timezone offset in minutes
export const getUserTimezoneOffset = () => {
  try {
    const israelTZ = 'Asia/Jerusalem';
    const date = new Date();
    
    // Simple calculation for Israel timezone
    const now = new Date();
    const jan = new Date(now.getFullYear(), 0, 1);
    const jul = new Date(now.getFullYear(), 6, 1);
    const isDST = now.getTimezoneOffset() < Math.max(jan.getTimezoneOffset(), jul.getTimezoneOffset());
    
    return isDST ? 180 : 120; // 180 minutes for DST, 120 for standard time
  } catch (error) {
    console.error('Timezone detection error:', error);
    logApiError(error, { context: 'getUserTimezoneOffset' });
    // Default to Israel timezone offset
    return 120; // Default to +2 hours
  }
};

// Process time string to show in original timezone (+2)
export const processTimeWithOriginalZone = (timeStr) => {
  if (!timeStr) return '';
  try {
    // Convert UTC time to Israel time
    const date = new Date(timeStr);
    return date.toLocaleTimeString('he-IL', {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Jerusalem'
    });
  } catch (error) {
    console.error('Time processing error:', error);
    logApiError(error, { context: 'processTimeWithOriginalZone', timeStr });
    return '';
  }
};

// Process Times array to show in original timezone
export const processTimesWithOriginalZone = (times) => {
  return times.map(time => ({
    ...time,
    TimeF: time.TimeF ? processTimeWithOriginalZone(time.TimeF) : '',
    TimeT: time.TimeT ? processTimeWithOriginalZone(time.TimeT) : ''
  }));
};

// Create a common axios config for all API calls
export const createApiConfig = (isStatus = false) => ({
  headers: { 
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  },
  timeout: isStatus ? 30000 : 45000, // 30 seconds for status, 45 for others
  validateStatus: status => status < 500, // Allow non-500 responses to be handled
  retry: isStatus ? 3 : 2,
  retryDelay: (retryCount) => retryCount * (isStatus ? 2000 : 3000)
});

// Handle API errors with improved logging
export const handleApiError = async (error, setResponseData, additionalData = {}) => {
  console.error('API Error:', error);
  setResponseData({});

  // Enhanced error logging
  await logApiError(error, additionalData);

  // Check for specific error conditions
  if (error.response?.status === 401) {
    toast.error('נדרשת התחברות מחדש', { toastId: 'auth-error' });
    return;
  }

  if (!navigator.onLine) {
    toast.error('אין חיבור לאינטרנט', { toastId: 'network-error' });
    return;
  }

  // Check for business errors in the response
  if (error.response?.data?.IsBusinessError === true && error.response?.data?.ErrorMessage) {
    // Business error - show the specific error message
    toast.error(error.response.data.ErrorMessage, { toastId: 'error' });
    return;
  }

  // Show appropriate error message
  const errorMessage = error.response?.data?.ErrorMessage || 'שגיאת מערכת';
  toast.error(errorMessage, { toastId: 'error' });
};

// API functions below

export const getClockApiMaakav = async (url, sendObj, setLoading, setResponseData) => {
  setLoading(true);
  const requestWithTimezone = {
    ...sendObj,
    TimezoneOffset: getUserTimezoneOffset()
  };

  try {
    const { data } = await axios.post(`${API_BASE_URL}${url}`, requestWithTimezone, createApiConfig());

    // Process the response data preserving original timezone
    if (data?.Shib) {
      data.Shib = data.Shib.map(report => ({
        ...report,
        Dates: report.Dates.map(date => ({
          ...date,
          Times: processTimesWithOriginalZone(date.Times)
        }))
      }));
    }
    
    setResponseData(data);
    return data;
  } catch (error) {
    handleApiError(error, setResponseData, {
      url: `${API_BASE_URL}${url}`,
      method: 'POST',
      payload: requestWithTimezone
    });
  } finally {
    setLoading(false);
  }
};

export const getClockApiMaakavStatus = async (url, sendObj, setLoading, setResponseData, setSelectedReportType, setTextAreaReport) => {
  setLoading(true);
  const requestWithTimezone = {
    ...sendObj,
    TimezoneOffset: getUserTimezoneOffset()
  };
  
  let retryCount = 0;
  const maxRetries = 3;
  
  while (retryCount < maxRetries) {
    try {
      const statusConfig = {
        ...createApiConfig(true),
        // Increase timeout for WebView
        timeout: /wv/.test(navigator.userAgent) ? 45000 : 30000
      };
      
      console.log('MaakavStatus Request:', {
        url: `${API_BASE_URL}${url}`,
        payload: requestWithTimezone,
        config: statusConfig,
        retryAttempt: retryCount + 1
      });

      const response = await axios.post(`${API_BASE_URL}${url}`, requestWithTimezone, statusConfig);

      console.log('MaakavStatus Response:', {
        status: response.status,
        data: response.data,
        headers: response.headers
      });

      if (!response.data) {
        throw new Error('לא התקבל מידע מהשרת');
      }

      if (response.data.Result !== 'Success') {
        setResponseData(false);
        throw new Error('שגיאה בקריאת סטטוס');
      }

      setSelectedReportType(response.data?.IsCurrIn ? response.data?.Typ : false);
      setTextAreaReport(response.data?.IsCurrIn ? response.data?.MoreInfo : false);

      // Cache the server response for timezone consistency
      if (response.data?.TimeF || response.data?.TimeT) {
        const serverResponse = {
          Dates: [{
            Times: [{
              TimeF: response.data.TimeF || '',
              TimeT: response.data.TimeT || ''
            }]
          }]
        };
        try {
          localStorage.setItem('lastServerResponse', JSON.stringify(serverResponse));
        } catch (storageError) {
          console.error('Failed to cache server response:', storageError);
          logApiError(storageError, {
            context: 'MaakavStatus.cacheResponse',
            level: ErrorLevels.WARNING
          });
        }
      }

      if (response.data?.IsCurrIn && response.data?.TimeF) {
        try {
          // Parse the ISO timestamp from the response
          const timeStr = response.data.TimeF; // Format: "2025-01-30T09:04:00+00:00"
          console.log('Processing time:', timeStr);
          
          // Create moment object in Israel timezone and set locale to Hebrew
          const israelTZ = 'Asia/Jerusalem';
          moment.locale('he');
          const time = moment(timeStr).tz(israelTZ);
          console.log('Created moment object:', time.format());
          
          // Format time in Israel timezone
          const timeValue = time.format('HH:mm');
          console.log('Formatted time:', timeValue);

          // Get weekday and date in Hebrew
          const weekday = time.format('dddd');
          const day = time.format('DD/MM/YY');
          
          setResponseData({
            api: response.data,
            time: timeValue,
            timeAllValues: {
              hours: time.hours(),
              minutes: time.minutes(),
              utcTime: response.data.TimeF
            },
            weekday: weekday,
            day: day
          });

          console.log('Set response data:', {
            time: timeValue,
            weekday,
            day
          });
        } catch (timeError) {
          console.error('Error processing time data:', timeError);
          logApiError(timeError, { 
            context: 'MaakavStatus.processTime',
            timeStr: response.data.TimeF 
          });
          
          // Use basic time formatting as fallback
          const time = new Date(response.data.TimeF);
          const fallbackTime = time.toLocaleTimeString('he-IL', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
            timeZone: 'Asia/Jerusalem'
          });
          
          setResponseData({
            api: response.data,
            time: fallbackTime,
            timeAllValues: {
              hours: time.getHours(),
              minutes: time.getMinutes(),
              utcTime: response.data.TimeF
            },
            weekday: 'לא זמין',
            day: time.toLocaleDateString('he-IL', { timeZone: 'Asia/Jerusalem' })
          });
        }
      } else {
        setResponseData(false);
      }
      break; // Exit retry loop on success
      
    } catch (error) {
      retryCount++;
      
      // Special handling for timeout in WebView
      if (error.code === 'ECONNABORTED' && /wv/.test(navigator.userAgent)) {
        console.log(`WebView timeout retry ${retryCount}/${maxRetries}`);
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
          continue;
        }
      }
      
      const errorDetails = {
        originalError: error,
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        headers: error.response?.headers,
        requestData: requestWithTimezone,
        networkInfo: {
          online: navigator.onLine,
          connectionType: navigator.connection ? navigator.connection.effectiveType : 'unknown',
          downlink: navigator.connection ? navigator.connection.downlink : 'unknown',
          rtt: navigator.connection ? navigator.connection.rtt : 'unknown'
        },
        retryAttempt: retryCount
      };
      
      // If this was the last retry, handle the error
      if (retryCount === maxRetries) {
        console.error('MaakavStatus Error Details:', errorDetails);
        
        let errorMessage = 'שגיאה בקריאת סטטוס';
        if (!navigator.onLine) {
          errorMessage = 'אין חיבור לאינטרנט, נא לבדוק את החיבור ולנסות שוב';
        } else if (error.code === 'ECONNABORTED') {
          errorMessage = 'השרת לא מגיב כרגע, אנא נסה שוב מאוחר יותר';
        } else if (error.response?.status === 401) {
          errorMessage = 'נדרשת התחברות מחדש';
        } else if (error.response?.status === 404) {
          errorMessage = 'השירות לא זמין כרגע, אנא נסה שוב מאוחר יותר';
        } else if (error.response?.data?.ErrorMessage) {
          errorMessage = error.response.data.ErrorMessage;
        }
        
        setResponseData(false);
        setSelectedReportType(false);
        setTextAreaReport(false);
        
        handleApiError(error, setResponseData, {
          url: `${API_BASE_URL}${url}`,
          method: 'POST',
          payload: requestWithTimezone,
          errorDetails,
          errorMessage,
          context: 'MaakavStatus.finalRetry'
        });
        break;
      }
    }
  }
  
  setLoading(false);
};

export const getClockApiMaakavOut = async (url, sendObj, setLoading, setResponseData, setSelectedReportType, setTextAreaReport, setPercentWorked, setLastExit = false) => {
  setLoading(true);
  const requestWithTimezone = {
    ...sendObj,
    TimezoneOffset: getUserTimezoneOffset()
  };
  
  try {
    const apiConfig = createApiConfig(false);
    
    console.log('MaakavOut Request:', {
      url: `${API_BASE_URL}${url}`,
      payload: requestWithTimezone,
      config: apiConfig
    });

    const response = await axios.post(`${API_BASE_URL}${url}`, requestWithTimezone, apiConfig);

    console.log('MaakavOut Response:', {
      status: response.status,
      data: response.data,
      headers: response.headers
    });

    if (!response.data) {
      throw new Error('לא התקבל מידע מהשרת');
    }

    // Check for business errors first
    if (response.data.IsBusinessError !== undefined) {
      if (response.data.IsBusinessError === true) {
        // Business error - show the specific error message
        toast.error(response.data.ErrorMessage || "שגיאת משתמש");
        setResponseData({});
        return response.data;
      } else if (response.data.ErrorMessage) {
        // System error - show generic message
        toast.error("שגיאת מערכת");
        setResponseData({});
        return response.data;
      }
    }

    if (response.data.Result === 'Error') {
      throw new Error(response.data.ErrorMessage || 'שגיאה ביציאה מהמערכת');
    }

    setResponseData(false);
    setSelectedReportType(false);
    setTextAreaReport(false);
    setPercentWorked(false);
    toast.success('היציאה התקבלה בהצלחה');

    if (setLastExit && response.data?.TimeT) {
      try {
        const time = new Date(response.data.TimeT);
        const timeValue = time.toLocaleTimeString('he-IL', {
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'Asia/Jerusalem'
        });
        setLastExit(timeValue);
      } catch (timeError) {
        console.error('Error formatting exit time:', timeError);
        logApiError(timeError, { 
          context: 'MaakavOut.formatExitTime',
          exitTime: response.data.TimeT 
        });
        
        const time = new Date(response.data.TimeT);
        setLastExit(time.toLocaleTimeString('he-IL', {
          hour: '2-digit',
          minute: '2-digit',
          timeZone: 'Asia/Jerusalem'
        }));
      }
    }

    return response.data;
  } catch (error) {
    const errorDetails = {
      originalError: error,
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      headers: error.response?.headers,
      requestData: requestWithTimezone,
      networkInfo: {
        online: navigator.onLine,
        connectionType: navigator.connection ? navigator.connection.effectiveType : 'unknown',
        downlink: navigator.connection ? navigator.connection.downlink : 'unknown',
        rtt: navigator.connection ? navigator.connection.rtt : 'unknown'
      }
    };
    console.error('MaakavOut Error Details:', errorDetails);
    
    let errorMessage = 'שגיאת מערכת'; // Changed from 'שגיאה ביציאה מהמערכת'
    if (!navigator.onLine) {
      errorMessage = 'אין חיבור לאינטרנט, נא לבדוק את החיבור ולנסות שוב';
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = 'תם הזמן המוקצב לפעולה, נא לנסות שוב';
    } else if (error.response?.status === 401) {
      errorMessage = 'נדרשת התחברות מחדש';
    } else if (error.response?.status === 404) {
      errorMessage = 'השירות לא זמין כרגע, אנא נסה שוב מאוחר יותר';
    } else if (error.response?.data?.IsBusinessError === true && error.response?.data?.ErrorMessage) {
      // Business error from API
      errorMessage = error.response.data.ErrorMessage;
    } else if (error.response?.data?.ErrorMessage) {
      // Keep specific error message for backward compatibility
      errorMessage = error.response.data.ErrorMessage;
    }
    
    handleApiError(error, setResponseData, {
      url: `${API_BASE_URL}${url}`,
      method: 'POST',
      payload: requestWithTimezone,
      errorDetails,
      errorMessage,
      context: 'MaakavOut'
    });
  } finally {
    setLoading(false);
  }
};

export const getClockApiMaakavIn = async (url, sendObj, setLoading, setResponseData, setSelectedReportType, setTextAreaReport) => {
  setLoading(true);
  const requestWithTimezone = {
    ...sendObj,
    TimezoneOffset: getUserTimezoneOffset()
  };
  
  try {
    const apiConfig = createApiConfig(false);
    
    console.log('MaakavIn Request:', {
      url: `${API_BASE_URL}${url}`,
      payload: requestWithTimezone,
      config: apiConfig
    });

    const response = await axios.post(`${API_BASE_URL}${url}`, requestWithTimezone, apiConfig);

    console.log('MaakavIn Response:', {
      status: response.status,
      data: response.data,
      headers: response.headers
    });

    if (!response.data) {
      throw new Error('לא התקבל מידע מהשרת');
    }

    // Check for business errors first
    if (response.data.IsBusinessError !== undefined) {
      if (response.data.IsBusinessError === true) {
        // Business error - show the specific error message
        toast.error(response.data.ErrorMessage || "שגיאת משתמש");
        setResponseData({});
        return response.data;
      } else if (response.data.ErrorMessage) {
        // System error - show generic message
        toast.error("שגיאת מערכת");
        setResponseData({});
        return response.data;
      }
    }

    if (response.data.Result === 'Error') {
      throw new Error(response.data.ErrorMessage || 'שגיאה בכניסה למערכת');
    }

    if (response.data.Result === 'Success') {
      toast.success('הכניסה התקבלה בהצלחה');
      
      const userJ = getSafeUserData();
      const newSendObj = { IDNumber: userJ.IDNO, SessionKey: userJ.SessionKey };
      
      // Use status config for status check
      await getClockApiMaakavStatus('/api/v2/volunteer/MaakavStatus', newSendObj, setLoading,
        setResponseData, setSelectedReportType, setTextAreaReport);
        
      return response.data;
    }

    setResponseData(response.data);
    return response.data;
  } catch (error) {
    const errorDetails = {
      originalError: error,
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      headers: error.response?.headers,
      requestData: requestWithTimezone,
      networkInfo: {
        online: navigator.onLine,
        connectionType: navigator.connection ? navigator.connection.effectiveType : 'unknown',
        downlink: navigator.connection ? navigator.connection.downlink : 'unknown',
        rtt: navigator.connection ? navigator.connection.rtt : 'unknown'
      }
    };
    console.error('MaakavIn Error Details:', errorDetails);
    
    let errorMessage = 'שגיאת מערכת'; // Changed from 'שגיאה בכניסה למערכת'
    if (!navigator.onLine) {
      errorMessage = 'אין חיבור לאינטרנט, נא לבדוק את החיבור ולנסות שוב';
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = 'תם הזמן המוקצב לפעולה, נא לנסות שוב';
    } else if (error.response?.status === 401) {
      errorMessage = 'נדרשת התחברות מחדש';
    } else if (error.response?.status === 404) {
      errorMessage = 'השירות לא זמין כרגע, אנא נסה שוב מאוחר יותר';
    } else if (error.response?.data?.IsBusinessError === true && error.response?.data?.ErrorMessage) {
      // Business error from API
      errorMessage = error.response.data.ErrorMessage;
    } else if (error.response?.data?.ErrorMessage) {
      // Keep specific error message for backward compatibility
      errorMessage = error.response.data.ErrorMessage;
    }
    
    handleApiError(error, setResponseData, {
      url: `${API_BASE_URL}${url}`,
      method: 'POST',
      payload: requestWithTimezone,
      errorDetails,
      errorMessage,
      context: 'MaakavIn'
    });
  } finally {
    setLoading(false);
  }
}; 