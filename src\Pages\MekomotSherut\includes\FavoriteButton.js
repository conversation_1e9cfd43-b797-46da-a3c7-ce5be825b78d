import React, { Component } from 'react'
import favorites from "../../../img/sherut-leumi/svg/sherutPlaces/card/favorites_on.svg";
import { Button } from "@mui/material";
export default class FavoriteButton extends Component {
    render() {

        let activeClass = this.props.is_all ? 'active ' : '';
        let disabledClass = this.props.is_disabled ? '' : ' disabled';

        const showNumber = !this.props.is_all ? `(${this.props.allResults})` : '';

        return (
            <div className="changePage">

                <div className='ButtonsCont'>
                    <Button onClick={() => this.props.showFavoritesOnly(false) } >הצג הכל {showNumber}</Button>
                    <Button disabled={!this.props.is_disabled}  onClick={() => this.props.showFavoritesOnly(true) } >
                        <img src={ favorites } alt='מועדפים' />
                        <span>הצגת מועדפים</span>
                    </Button>
                </div>

            </div>
        )
    }
}
