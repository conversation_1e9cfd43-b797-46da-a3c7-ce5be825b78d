<div class="" style="padding: 20px;">
    <div class="row">
        <?php if(isset($programs) && $programs) foreach ($programs as $row) { ?>
        <div class="col-md-4">
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="form-group">
                        <img id="preview<?php echo $row->Id(); ?>" class="materialboxed" src="<?php echo $row->Img('logo'); ?>" alt="preview" style="max-width: 100%;" data-width="0" data-height="0" data-input-name="img"/>
                    </div>
                    <div class="form-group">
                        <?php echo $row->Arg('he_title'); ?>
                    </div>
                    <div class="input-group">
                        <?php if($row->Id()) { ?>
                        <div class="input-group-btn">
                            <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/object/program_branch/status/' . $row->Id()); ?>">
                                <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                            </button> 
                        </div>
                        <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $row->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/object/program_branch/sort/' . $row->Id() . '/'); ?>">
                        <div class="input-group-btn">
                            <a class="btn btn-default pulse-hover" href="<?php echo base_url('admin/object/program_branch/show/' . $row->Id() . '?return='.  current_url()); ?>" role="button">עריכה</a>

                            <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/object/program_branch/destroy/' . $row->Id() . '/' . getQS()); ?>">
                                מחק
                            </button> 
                        </div>
                        <?php } else { ?>
                            <?php echo form_open_multipart('admin/object/program_branch/put/', array("class" => "ajax")); ?>
                            <input type="hidden" name="branch_id" value="<?php echo $branch_id; ?>"/>
                            <input type="hidden" name="program_id" value="<?php echo $row->Arg('p_id'); ?>"/>
                            <button type="submit" class="btn btn-primary">הוסף</button>
                            <?php echo form_close(); ?>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
        <?php } ?>
    </div>
</div>