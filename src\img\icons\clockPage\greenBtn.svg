<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="314" height="314" viewBox="0 0 314 314">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0ebe7e"/>
      <stop offset="1" stop-color="#1a815b"/>
    </linearGradient>
    <filter id="Ellipse_2" x="0" y="0" width="314" height="314" filterUnits="userSpaceOnUse">
      <feOffset dy="6" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="12.5" result="blur"/>
      <feFlood flood-color="#03291b" flood-opacity="0.478"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_162220" data-name="Group 162220" transform="translate(-57.5 -415.5)">
    <circle id="Ellipse_3" data-name="Ellipse 3" cx="130" cy="130" r="130" transform="translate(85 437)" fill="#1bd591"/>
    <g transform="matrix(1, 0, 0, 1, 57.5, 415.5)" filter="url(#Ellipse_2)">
      <circle id="Ellipse_2-2" data-name="Ellipse 2" cx="119.5" cy="119.5" r="119.5" transform="translate(37.5 31.5)" fill="url(#linear-gradient)"/>
    </g>
  </g>
</svg>
