import React, { useState, useEffect } from 'react';
import { RestUrls } from "../../../Components/-Helpers-/config";
import ShowDataInRowPlaces from './ShowDataInRowPlaces';
import ShowMoreDataPlaces from './ShowMoreDataPlaces';
import GalleryPictures from './GalleryPictures';
import { isMobile } from "react-device-detect";
import { motion } from 'framer-motion';

// Icons
import blueBtnInfo from "../../../img/sherut-leumi/svg/sherutPlaces/card/blueBtnInfo.svg";
import favorites from "../../../img/sherut-leumi/svg/sherutPlaces/card/favorites.svg";
import favorites_on from "../../../img/sherut-leumi/svg/sherutPlaces/card/favorites_on.svg";
import LocationOnIcon from '@mui/icons-material/LocationOn';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';

const SearchResultRow = ({ item, showOpen = false, favoritesPlaces, chageFavorites }) => {
  const [showMoreData, setShowMoreData] = useState(showOpen);
  const [fPlacesArray, setFPlacesArray] = useState(favoritesPlaces);
  const [randomImage, setRandomImage] = useState("");
  
  useEffect(() => {
    // Generate a random number between 1 and 21 (the number of images in randomPic folder)
    const randomNum = Math.floor(Math.random() * 21) + 1;
    setRandomImage(`/randomPic/${randomNum}_converted.png`);
  }, []);

  const addRemoveFavorites = (item) => {
    let updatedFavorites = [...favoritesPlaces];
    
    if (!updatedFavorites.includes(item.id)) {
      updatedFavorites.push(item.id);
      setFPlacesArray(updatedFavorites);
      localStorage.setItem('favoritesPlaces', updatedFavorites.join(','));
    } else {
      const index = updatedFavorites.findIndex(id => id === item.id);
      if (index !== -1) {
        updatedFavorites.splice(index, 1);
        setFPlacesArray(updatedFavorites);
        localStorage.setItem('favoritesPlaces', updatedFavorites.join(','));
      }
    }

    chageFavorites(updatedFavorites);
  };

  const urlPic = `${RestUrls.pagesPictures}/noPic.jpg?v=1`;
  const is_Favorite = fPlacesArray.includes(item.id);
  
  return (
    <motion.div 
      className={`searchRow ${is_Favorite ? 'activeFavorite' : ''}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header with city name */}
      <div className="serviceLocationHeader">
        <div className="cityBadge">
          <LocationOnIcon className="locationIcon" />
          <span>{item?.City_Value || 'לא צוין מיקום'}</span>
        </div>
        {item?.YEAR && (
          <div className="yearBadge">
            <CalendarMonthIcon className="calendarIcon" />
            <span>{item.YEAR}</span>
          </div>
        )}
      </div>

      <div className="serviceLocationCard">
        <div className='grid md:flex'>
          <div className="coltableSearch picCol ">
            {item.pictures?.length > 0 ? (
              <GalleryPictures pictures={item.pictures} />
            ) : (
              <div className="noImageContainer">
                <img src={randomImage} alt="תמונה אקראית" className="randomImage" />
              </div>
            )}
          </div>

          <div className=" dataCol">
            <ShowDataInRowPlaces item={item} />
          </div>

          {!isMobile && (
            <div className="flex justify-center items-center p-2">
              <div className="flex flex-col gap-2"> 
                <button 
                  className="infoButton" 
                  onClick={() => setShowMoreData(!showMoreData)}
                >
                  <img src={blueBtnInfo} alt='לפרטים נוספים' />
                  <span>
                    {!showMoreData ? 'לפרטים נוספים' : 'סגירת פרטים'}
                  </span>
                </button>

                <button 
                  className={`favoriteButton ${is_Favorite ? 'active' : ''}`} 
                  onClick={() => addRemoveFavorites(item)}
                >
                  <img src={is_Favorite ? favorites_on : favorites} alt='מועדפים' />
                  <span>
                    {is_Favorite ? 'הסר ממועדפים' : 'הוסף למועדפים'}
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>

        {isMobile && (
          <div className="mobileButtonsContainer flex justify-center items-center p-2 w-full mt-2 mx-auto">
            <div className="flex flex-col gap-2">
              <button 
                className="infoButton" 
                onClick={() => setShowMoreData(!showMoreData)}
              >
                <img src={blueBtnInfo} alt='לפרטים נוספים' />
                <span>
                  {!showMoreData ? 'לפרטים נוספים' : 'סגירת פרטים'}
                </span>
              </button>

              <button 
                className={`favoriteButton ${is_Favorite ? 'active' : ''}`} 
                onClick={() => addRemoveFavorites(item)}
              >
                <img src={is_Favorite ? favorites_on : favorites} alt='מועדפים' />
                <span>
                  {is_Favorite ? 'הסר ממועדפים' : 'הוסף למועדפים'}
                </span>
              </button>
            </div>
          </div>
        )}

        {showMoreData && (
          <motion.div 
            className="showMoreData"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ShowMoreDataPlaces item={item} isMobile={isMobile} />
          </motion.div>
        )}
      </div>

      <style jsx>{`
        .searchRow {
          margin-bottom: 1.5rem;
          direction: rtl;
          font-family: 'Rubik', sans-serif;
        }
        
        .serviceLocationHeader {
          background-color: #004b8d;
          padding: 0.75rem 1rem;
          border-radius: 8px 8px 0 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: white;
        }
        
        .cityBadge, .yearBadge {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-weight: 500;
        }
        
        .locationIcon, .calendarIcon {
          width: 1rem;
          height: 1rem;
        }
        
        .serviceLocationCard {
          background-color: white;
          border-radius: 0 0 8px 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }
        
        .tableSearchRow {
          display: flex;
          flex-direction: row;
          border-bottom: 1px solid #e5eeff;
        }
        
        .picCol {
          flex: 0 0 220px;
          max-width: 220px;
          overflow: hidden;
        }
        
        .noImageContainer {
          width: 100%;
          height: 220px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f8ff;
          overflow: hidden;
        }
        
        .noImage {
          max-width: 80%;
          max-height: 80%;
          object-fit: contain;
        }
        
        .randomImage {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .dataCol {
          padding: 1rem;
          flex: 1;
        }
        
        .locationName {
          font-size: 1.25rem;
          font-weight: 600;
          color: #004b8d;
          margin-bottom: 0.75rem;
        }
        
        .BtnsCol {
          flex: 0 0 200px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 1rem;
        }
        
        .sherutPlaceButtons {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          width: 100%;
        }
        
        .infoButton, .favoriteButton {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          background-color: #f0f7ff;
          border: 1px solid #004b8d;
          color: #004b8d;
          border-radius: 6px;
          padding: 0.5rem 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          width: 100%;
        }
        
        .infoButton:hover {
          background-color: #e0efff;
        }
        
        .favoriteButton.active {
          background-color: #fff5e0;
          border-color: #ff9800;
          color: #ff9800;
        }
        
        .favoriteButton:hover {
          background-color: #fff9e6;
        }
        
        .mobileButtonsContainer {
          padding: 1rem;
          border-bottom: 1px solid #e5eeff;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
        }
        
        .showMoreData {
          padding: 1.5rem;
          background-color: #f0f7ff;
          border-top: 1px solid #cce0ff;
        }
        
        .activeFavorite {
          border: 2px solid #ff9800;
          border-radius: 8px;
        }
        
        @media (max-width: 768px) {
          .searchRow {
            margin-bottom: 1rem;
          }
          
          .serviceLocationHeader {
            flex-direction: column;
            gap: 0.5rem;
            align-items: flex-start;
          }
          
          .tableSearchRow {
            flex-direction: column;
          }
          
          .picCol {
            max-width: 100%;
            flex: 1;
          }
          
          .noImageContainer {
            height: 180px;
          }
          
          .dataCol {
            padding: 1rem;
          }
          
          .locationName {
            font-size: 1.1rem;
          }
          
          .sherutPlaceButtons {
            flex-direction: row;
            justify-content: space-between;
          }
          
          .infoButton, .favoriteButton {
            flex: 1;
            padding: 0.5rem;
            font-size: 0.9rem;
          }
          
          .mobileButtonsContainer .sherutPlaceButtons {
            width: 100%;
          }
          
          .infoButton img, .favoriteButton img {
            width: 1.2rem;
            height: 1.2rem;
          }
        }
        
        @media (max-width: 480px) {
          .sherutPlaceButtons {
            flex-direction: column;
          }
          
          .infoButton, .favoriteButton {
            margin-bottom: 0.5rem;
          }
        }
      `}</style>
    </motion.div>
  );
};

export default SearchResultRow;
