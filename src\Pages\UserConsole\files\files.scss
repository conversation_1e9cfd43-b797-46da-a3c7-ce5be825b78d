/*
 * files.scss - סגנונות CSS לעמודי ניהול הקבצים
 * 
 * מגדיר סגנונות עבור:
 * - כרטיסי מסמכים
 * - כפתורי העלאה
 * - מודאלים
 * - תצוגות מקדימות של תמונות ומסמכים
 */

.filesPage {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;

  .header-content {
    margin-bottom: 40px;

    h1 {
      font-size: 2.5rem;
      color: #333;
      margin-bottom: 1rem;
      font-family: "fb_bold", "helvetica", "arial" !important;
    }

    p {
      font-size: 1.2rem;
      color: #666;
    }
  }

  .AccordionCont {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 40px;

    .accordion {
      border: none;

      .accordion-item {
        border: none;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }
      }

      .accordion-header {
        button {
          background: #f8f9fa;
          padding: 1.2rem;
          font-family: "fb_bold", "helvetica", "arial" !important;
          font-size: 1.1rem;
          color: #333;

          &:hover {
            background: #f0f0f0;
          }

          &:focus {
            box-shadow: none;
          }
        }
      }
    }
  }

  .docsCont {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    padding: 30px;
  }

  .fileItem {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .blueQuad {
      background-color: #f8f9fa;
      border-radius: 8px;
      transition: all 0.3s ease;

      &.docExist {
        background-color: #e7f5ff;
      }

      &.fullBorder {
        border: 2px solid #1991d0;
      }

      .blueBtn {
        background: #1991d0;
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 6px;
        font-size: 0.9rem;
        transition: all 0.2s ease;

        &:hover {
          background: #0f6998;
          transform: translateY(-1px);
        }

        &.disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

.filesModal {
  .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .Message {
    padding: 30px;

    h2 {
      color: #333;
      margin-bottom: 20px;
    }
  }

  .blueBtn {
    background: #1991d0;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 6px;
    font-size: 1rem;
    transition: all 0.2s ease;

    &:hover:not(.disabled) {
      background: #0f6998;
      transform: translateY(-1px);
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background: #ccc !important;
    }
  }

  .btnCont {
    margin: 20px 0 0 0;
  }

  .textReadAndSign {
    overflow-y: auto;
    height: 300px;
    font-size: 16px;
    padding: 5px 20px;
  }

  .formCont {
    background: #eeeeee;
    width: 100%;
    margin: 20px 0 0 0;
    padding: 0 0 20px 0;

    .form-control {
      background: #eeeeee;
    }

    .blueSignBtn {
      width: 100%;
      border: 1px solid #a9c0de;
      background: #1991d0;
      color: white;
      border-radius: 5px;
      font-size: 17px;
      text-align: center;
      margin: 0px 0 0 0;
      padding: 15px 5px;
      cursor: pointer;

      &:hover {
        background: #0f6998;
        color: white;
      }
    }
  }

  &.SMSCodeModal {
    h2 {
      margin: 0 0 20px 0;
      color: #0f6998;
    }
  }

  &.ishurMahalaModal {
    .inputs {
      padding: 0 5%;
      h4 {
        font-size: 16px;
        color: #b5b5b5;
      }
    }
  }

  &.modalHtml.modalSite .modal-dialog {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .showHtml {
    padding: 10px 20px;
    table {
      tfoot {
        tr {
          td {
            &::before {
              content: "";
              position: relative;
              padding: 10px 0;
              display: block;
              border-bottom: 1px solid black;
              margin: 0 0 10px 0;
            }
            position: relative !important;
            border: none !important;
          }
        }
      }
    }
  }
}

// תיקונים למובייל
@media (max-width: 768px) {
  .filesPage {
    padding: 0 15px;

    .header-content {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .docsCont {
      grid-template-columns: 1fr;
      padding: 20px;
    }
  }
}
