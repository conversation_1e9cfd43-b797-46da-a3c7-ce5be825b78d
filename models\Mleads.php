<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Mleads extends CI_Model {
    
    const TABLE = 'leads';
    
    public function __construct() {
        parent::__construct();
        
    }
    
    public function saveToCrm($params) {
        $values['name'] = $params['name'];
        $values['phone'] = $params['phone'];
        $values['email'] = $params['email'];

        $values['custom1'] = $params['custom1'];
        $values['custom2'] = $params['custom2'];
        $values['itkunim'] = $params['itkunim'];
        
        $values['bannerid'] = $params['bannerid'];
        $values['campaignid'] = $params['campaignid'];
        $values['utm'] = isset($params['utm']) ? $params['utm'] : '';
        
        $values['title'] = '';
        $values['client_name'] = $params['client_name'];
        $values['landpage_name'] = $params['landpage_name'];
        $values['page_link'] = $params['page_link'];
        $values['source'] = isset($params['source']) ? $params['source'] : current_url();
        $values['lead_ip'] = $this->input->ip_address();
        
        if(FALSE) {

        //SAVE THE LEAD TO AK CRM 
        //GET BACK THE TOTAL FOR EACH PAGE
        $endpoint = 'http://ak-digital.net/akleads/site/save_lead_to_crm';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_VERBOSE, TRUE);
        curl_setopt($ch, CURLOPT_HEADER, FALSE);
        // Turn off the server and peer verification (TrustManager Concept).
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        // Set the request as a POST FIELD for curl.
        curl_setopt($ch, CURLOPT_POSTFIELDS, $values);
        $httpResponse = curl_exec($ch);
        curl_close($ch);
        $result = json_decode($httpResponse, TRUE);
        
        return $result;
        }
    }
    
    public function sendEmail($to, $params, $is_HelpRequest = FALSE) {
        
        if(FALSE) {
        $this->load->library("email");
        $config['charset'] = 'UTF-8';
        $config['mailtype'] = 'html';
        $this->email->initialize($config);
        $this->email->from('<EMAIL>', '<EMAIL>');
        $this->email->to($to);
        $this->email->bcc('<EMAIL>,<EMAIL>');
        
        $subject = 'ליד חדש מדף - '.$params['client_name'].' | '.$params['landpage_name'];
        
        $this->email->subject($subject);
        
        if($is_HelpRequest) {
            $this->email->message($this->bodyMailHelpRequest($params));
        } else {
            $this->email->message($this->bodyMail($params));
        }
        
        
        
        
        if(!empty($_FILES)) {
            
            foreach ($_FILES as $keyfile => $file) {
                if(move_uploaded_file($_FILES[$keyfile]['tmp_name'], appFILES . $_FILES[$keyfile]['name'])) {
                    $this->email->attach(appFILES . $_FILES[$keyfile]['name']);
                    unlink(appFILES . $_FILES[$keyfile]['name']);
                }
            }
        }
        return $this->email->send();
        }
    }
    
    private function bodyMail($params) {
        
        if($params['itkunim']=='1') {$params['itkunim']='מעוניין/ת לקבל עדכונים במייל';}
        
         $params['custom2'] = $params['custom2'] == 'site' ? 'אתר החברה' : $params['custom2'];
        
        //table lead style
        $td_style_1="background-color: rgb(235, 235, 235); width: 173px; text-align: center;text-direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;";  

        $td_style_2="background-color: rgb(213, 213, 213); width: 173px; text-align: center;text-direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;";

        $body_mail= "
        <div style='text-align: center;text-direction:rtl; font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;'>
        <br/><br/>
        <a href='http://ak-adv.co.il'><img src='http://ak-digital.net//ak-mobile/mail_img/ak-logos.png' border='0'></a>

        <table align='center' dir='LTR'; border='0' cellpadding='5' cellspacing='2' style='width: 346px;'>
        <tbody>
            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['name']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>שם פרטי ומשפחה</span>
                </td>
            </tr>

            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['phone']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>טלפון</span>
                </td>
            </tr>

            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['email']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>דואר אלקטרוני</span>
                </td>
            </tr>


            <tr>

                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['message']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>הודעה</span>
                </td>

            </tr>

            <tr>

                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['kind']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>נתיב</span>
                </td>

            </tr>
            
            

            <tr>
                <td colspan='2' style='text-align:center'>
                  <a href='http://ak-digital.net/akleads'>
                  <img style='display: inherit;' src='http://ak-digital.net//ak-mobile/mail_img/knisa.gif' border='0'>
                  </a>
                </td>
            </tr>


        </tbody></table><br/><br/>

        </div>";

        return $body_mail;  
    }
    
    
    
    
    private function bodyMailHelpRequest($params) {
        
        if($params['itkunim']=='1') {$params['itkunim']='שליחה אני מאשר/ת את תקנון האתר';}
        
         $params['custom2'] = $params['custom2'] == 'site' ? 'אתר החברה' : $params['custom2'];
        
        //table lead style
        $td_style_1="background-color: rgb(235, 235, 235); width: 173px; text-align: center;text-direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;";  

        $td_style_2="background-color: rgb(213, 213, 213); width: 173px; text-align: center;text-direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;";

        $body_mail= "
        <div style='text-align: center;text-direction:rtl; font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;'>
        <br/><br/>
        <a href='http://ak-adv.co.il'><img src='http://ak-digital.net//ak-mobile/mail_img/ak-logos.png' border='0'></a>

        <table align='center' dir='LTR'; border='0' cellpadding='5' cellspacing='2' style='width: 346px;'>
        <tbody>
            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['name']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>שם פרטי ומשפחה</span>
                </td>
            </tr>

            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['phone']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>טלפון</span>
                </td>
            </tr>

            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['email']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>דואר אלקטרוני</span>
                </td>
            </tr>

            <tr>

                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['place']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>ישוב</span>
                </td>

            </tr>
            
            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['target']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>סוג העזרה המבוקשת</span>
                </td>
            </tr>
            
            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['forWho']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>עבור מי העזרה</span>
                </td>
            </tr>
            
            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['ishur']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>קיבלת את הסכמתו</span>
                </td>
            </tr>
            

            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['nameHelp']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>שם האדם הזקוק לעזרה</span>
                </td>
            </tr>
            
            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['phoneHelp']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>טלפון האדם הזקוק לעזרה</span>
                </td>
            </tr>
            

            <tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['itkunim']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>תקנון</span>
                </td>
            </tr>

            
            <tr>

                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['file']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>קובץ מצורף</span>
                </td>

            </tr>

            <tr>

                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['kind']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>נתיב</span>
                </td>

            </tr>
            
            

            <tr>
                <td colspan='2' style='text-align:center'>
                  <a href='http://ak-digital.net/akleads'>
                  <img style='display: inherit;' src='http://ak-digital.net//ak-mobile/mail_img/knisa.gif' border='0'>
                  </a>
                </td>
            </tr>


        </tbody></table><br/><br/>

        </div>";

        return $body_mail;  
    }
}