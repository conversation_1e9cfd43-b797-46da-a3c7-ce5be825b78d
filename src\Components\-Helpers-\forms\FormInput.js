import React from 'react';
import { Form, FloatingLabel } from 'react-bootstrap';

/**
 * FormInput - A simplified form input component with floating label
 * 
 * @param {Object} props
 * @param {string} props.name - Input field name
 * @param {string} props.type - Input type (text, password, email, etc)
 * @param {string} props.value - Current input value
 * @param {function} props.onChange - Change handler function
 * @param {string} props.label - Input label text
 * @param {string} props.error - Error message to display
 * @param {boolean} props.required - Whether field is required
 * @param {boolean} props.disabled - Whether field is disabled
 * @param {Object} props.validation - Validation rules object
 * @param {string} props.className - Additional CSS classes
 */
const FormInput = React.memo(({
  name,
  type = 'text',
  value,
  onChange,
  label,
  error,
  required = false,
  disabled = false,
  validation = {},
  className = '',
}) => {
  // Handle input changes
  const handleChange = (e) => {
    let newValue = e.target.value;
    
    // Handle Hebrew-only validation
    if (validation.hebrewOnly) {
      newValue = newValue.replace(/[A-Za-z]/gi, '');
    }
    
    onChange(name, newValue);
  };

  return (
    <div className="form-input-wrapper">
      <FloatingLabel controlId={`input-${name}`} label={label}>
        <Form.Control
          type={type}
          name={name}
          value={value || ''}
          onChange={handleChange}
          isInvalid={!!error}
          required={required}
          disabled={disabled}
          placeholder={label}
          className={`form-input ${className}`}
        />
        {error && (
          <Form.Control.Feedback type="invalid">
            {error}
          </Form.Control.Feedback>
        )}
      </FloatingLabel>
    </div>
  );
});

FormInput.displayName = 'FormInput';

export default FormInput;
