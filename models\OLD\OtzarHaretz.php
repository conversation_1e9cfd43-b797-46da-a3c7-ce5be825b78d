<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>tzarHaretz extends CI_Model {

    public function __construct() {
        parent::__construct();
        
    }
    
    public function getSuppliers4Search() {
        
        //$this->db->select('*');
        $this->db->select('id,name');
        $this->db->from('suppliers');
        $this->db->where('status',1);
        $result = $this->db->get();
        $allSuppliers = $result->result_array();
        
        
        if( !empty($allSuppliers) ) {
            
            $allData = $allSuppliers;
            
        } else $allData = array();
        
        
        return $allData;
        
    }
    
    
    public function getUserMoney($params=false,$monthLimit = false) {
        
        if($params['userId']) {
            
            //$this->db->select('*');
            $this->db->select('id,TotalPrice,OrderId');
            $this->db->from('leadsLandpage');
            $this->db->where('id', $params['userId']);
            $result= $this->db->get();
            $client = $result->row_array();
            
            $allMoney = $this->OtzarHaretz->getAllBuysClient($client);
            
            if( $allMoney > 0 ) {
                
                    $monthCount = $this->checkMonthCount($startDay = '2021-09-01 00:00:00');
                    $monyMonth = ((float)$allMoney / 13);

                    if(!$monthLimit) {
                        $allMoney = $monthCount * $monyMonth;
                    }
                    
                    
                    if($monthCount > 13) {

                        return 0; // END

                    }
                
                $this->db->select('money');
                $this->db->from('transactions');
                $this->db->where('userId', $params['userId']);
                $result= $this->db->get();
                $transactions = $result->result_array();
                
                
                if(!empty($transactions)) {
                    
                    foreach ($transactions as $value) {
                        if( !empty((float)$value['money']) ) {
                            $allMoney = $allMoney - (float)$value['money'];
                        }
                    }
                }
                
                
                return number_format((float)$allMoney, 2, '.', '');
            }
            
            return 0;
            
        } else {
            
            return 0;
            
        }
        
    }
    
    public function checkMonthCount($startDay) {
        
        $now = date("Y-m-d H:i:s");
        
        $ts1 = strtotime($startDay);
        $ts2 = strtotime($now);

        $year1 = date('Y', $ts1);
        $year2 = date('Y', $ts2);

        $month1 = date('m', $ts1);
        $month2 = date('m', $ts2);

        $diff = (($year2 - $year1) * 12) + ($month2 - $month1);
        
        return $diff +1;
        
    }
    

    public function getSupplierMoney($params=false) {
        
        if($params['userId']) {
            
            //echo "userId: ".$params['userId'];
            
//            //$this->db->select('*');
//            $this->db->select('id,TotalPrice');
//            $this->db->from('leadsLandpage');
//            $this->db->where('id', $params['userId']);
//            $result= $this->db->get();
//            $money = $result->row_array();
//            
//            $allMoney = 0;
            
            if( true ) {
                
                $allMoney = array (
                    'today' => 0,
                    'thisMonth' => 0
                );
                
                $this->db->select('money,userId,created_at,userId');
                $this->db->from('transactions');
                $this->db->where('supplierId', $params['userId'] );
                $result= $this->db->get();
                $money = $result->result_array();
                
                $now = date("Y-m-d H:i:s");
                $today = changeDateFormat($now, 'Y-m-d', 'Y-m-d');
                $thisMonth = changeDateFormat($now, 'Y-m', 'Y-m');
                
                $addToday = 0;
                $addMonth = 0;
                    
                $clientIdToday = array();
                $clientIdMonth = array();
                    
                
                if( !empty($money) ) {
                    
                    
                        
                    foreach ($money as $value) {
                        
                        if( !empty((float)$value['money']) ) {
                            
                            
                            
                            $todayValue = changeDateFormat($value['created_at'], 'Y-m-d', 'Y-m-d');
                            $thisMonthValue = changeDateFormat($value['created_at'], 'Y-m', 'Y-m');
                            
                            $valueMoney = (float)$value['money'];
                            
                            if($today == $todayValue) {
                                $clientIdToday[] = $value['userId'];
                                $addToday = $addToday + $valueMoney;
                            }
                            
                            if($thisMonth == $thisMonthValue) {
                                $clientIdMonth[] = $value['userId'];
                                $addMonth = $addMonth + $valueMoney;
                            }
                            
                        }
                    }
                    
                    $clientIdToday = array_unique($clientIdToday);
                    $clientIdMonth = array_unique($clientIdMonth);
                    
                }
                
                
                $allMoney = array (
                    'today' => number_format((float)$addToday, 2, '.', ''),
                    'thisMonth' => number_format((float)$addMonth, 2, '.', ''),
                    'uniqueClientsToday' => count($clientIdToday),
                    'uniqueClientsMonth' => count($clientIdMonth)
                );
                
                
                return  $allMoney;
                
            }
            
            return 0;
            
        } else {
            
            return 0;
            
        }
        
    }
    
    
    public function sendSMS($phone = FALSE, $message = FALSE, $from = 'Otzar Haretz', $param = FALSE) {
        
        //$from = '0723932946'; // FOR SMS SYSTEM
        
        if(!empty($phone) && !empty($message)) {
            
            $checkPhone = $this->returnNoCellsPhone($phone); 
            
            if(empty($checkPhone)) {
                
                if($param == 'multi') {

                $phonenumbers = explode(',', $phone);
                if(!empty($phonenumbers)) {
                    foreach ($phonenumbers as $value) {
                        $phones[] =  preg_replace('/[^0-9]/', '', $value);
                    }
                }
                $phone = implode(',', $phones);

            } else {
                $phone = preg_replace('/[^0-9]/', '', $phone);
            }


            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL,"http://api.multisend.co.il/MultiSendAPI/sendsms");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS,
                        "user=otzarharez&password=Aa12345&from=".$from."&recipient=".$phone."&message=".$message);

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $server_output = curl_exec ($ch);

            curl_close ($ch);

            return $server_output;
                
                
                
            }
        }
    }
    
    
    
    public function send_email($to_emails, $subject,$html_body) {
        
        //https://codeigniter.com/userguide3/libraries/email.html
        
//        $to_emails = $this->input->post('to_emails');
//        $from = $this->input->post('from_email');
//        $subject = $this->input->post('subject');
//        $html_body = $this->input->post('html_body');
        
        $pass = 'Z&.~0.6I{xOO';
        //<EMAIL>
        
        $this->load->library("email");
        
        $config = Array(
            'protocol' => 'mail',
            'smtp_host' => 'in-v3.mailjet.com',
            'smtp_port' => 587,
            'smtp_user' => 'fe313beca5ed02d82af856c9a94e0e6b',
            'smtp_pass' => '53edd86632c33b0ef4ceaae3eeb1dfe5',
            'mailtype'  => 'html', 
            'charset'   => 'UTF-8'
        );
        
        $this->email->initialize($config);
        $this->email->from('<EMAIL>', 'אוצר הארץ');
        $this->email->to($to_emails);
        

        $this->email->subject($subject);
        $this->email->message($html_body);
        $response['success'] = $this->email->send();
        
        return $response['success'];
//        $this->output->set_status_header('200');
//            
//        return $this->output
//                ->set_content_type('application/json')
//                ->set_output(json_encode($response));
    }
    
    
    
    
    public function getSupplierReport($params=false) {
        
        if($params['userId']) {
            
            //echo "userId: ".$params['userId'];
            
//            //$this->db->select('*');
//            $this->db->select('id,TotalPrice');
//            $this->db->from('leadsLandpage');
//            $this->db->where('id', $params['userId']);
//            $result= $this->db->get();
//            $money = $result->row_array();
//            
//            $allMoney = 0;
            
            if( true ) {
                
                $this->db->select('id,created_at,money,userId,token,cashierId');
                $this->db->from('transactions');
                $this->db->order_by('created_at', 'DESC');
                $this->db->where('status', 1 );
                
                if(isset($params['supplierId']) && !empty($params['supplierId'])) {
                    
                    $this->db->where('supplierId', $params['supplierId'] );
                    
                } else {
                    
                    $this->db->where('supplierId', $params['userId'] );
                    
                }
                
                $today = date("Y-m-d 00:00:00");
                $startDate = isset($params['startDate']) && !empty($params['startDate']) ? changeDateFormat($params['startDate'].' 0:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;
                $endDate = isset($params['endDate']) && !empty($params['endDate']) ? changeDateFormat($params['endDate'].' 24:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;
                
                $flag = array();
                
                if($startDate) {
                    $this->db->where('created_at >=', $startDate );
                }
                
                if($endDate) {
                    $this->db->where('created_at <=', $endDate );
                } 
                
                if( !$startDate && !$endDate ) {
                    
                    $this->db->where('created_at >=', $today );
                }
                        
                
                $result= $this->db->get();
                $transactions = $result->result_array();
                
                if( !empty($transactions) ) {
                    
                    
                    foreach ($transactions as $transaction) {
                        
                        $this->db->select('firstName,lastName');
                        $this->db->from('leadsLandpage');
                        $this->db->where('id', $transaction['userId'] );
                        
                        $result= $this->db->get();
                        $clientDb = $result->row_array();
                        
                        $nameDB = $clientDb['firstName'].' '.$clientDb['lastName'];
                        
                        $name = character_limiter($nameDB, 10,'...');
                        
                        $client = array(
                            'name' => $name
                        );
                        
                        $transaction['money'] = (float)$transaction['money'];
                        
                        
                        $this->db->select('name,supplierId');
                        $this->db->from('cashiersSuppliers');
                        $this->db->where('id', $transaction['cashierId'] );
                        $result= $this->db->get();
                        $cashier = $result->row_array();
                        
                        
                        
                        
                        
                        $return[] = array(
                            'trans' => $transaction,
                            'cashier' => $cashier,
                            'client' => $client,
                            'dates' => 'start: '.$startDate.' end: '.$endDate,
                            'flag' => $flag
                        );
                        
                    }
                    
                    return $return;
                    
                }
                
                return  array();
                
            }
            
            return array();
            
        } else {
            
            return array();
            
        }
        
    }
    
    public function GetSystemHomeData() {
        
        
        $this->db->select('id');
        $this->db->from('leadsLandpage');
        $result= $this->db->get();
        $money = $result->num_rows();
        
        
        $this->db->select('id');
        $this->db->from('suppliers');
        $result= $this->db->get();
        $suppliers = $result->num_rows();
        
        
        $return = array (
            'number1' => 0,
            'number2' => $money,
            'number3' => $suppliers
        );
        
        return $return;
        
    }
    
    
    public function getAllBuysClient($client = false) {
    
        
        if( !empty($client) && isset($client['OrderId']) && isset($client['TotalPrice']) ) {

            
            $allMoney = (float)$client['TotalPrice'];
            
            $this->db->select('TotalPrice,OrderId');
            $this->db->from('buys');
            $this->db->where('userId', $client['id']);
            $result= $this->db->get();
            
            $money = $result->result_array();
            
            if(!empty($money)) {
                
                
                foreach ($money as $value) {
                    
                    if( $client['OrderId'] !== $value['OrderId'] ) {
                        
                        
                        $allMoney = $allMoney + (float)$value['TotalPrice'];
                        
                    }
                    
                    
                }
                
                
            }
            
            
        }
        
        return (float)$allMoney;
        
    }
    
    
    public function orderByFavoriteSuppliers($suppliers, $jsonPosts) {
        
        
        $this->db->select('supplierId');
        $this->db->from('favoritesSuppliers');
        $this->db->where('userId', $jsonPosts['userId']);
        $this->db->where('status', 1);
        $result= $this->db->get();
        $favoritesSuppliers = $result->result_array();
        
        
        
        if(!empty($favoritesSuppliers)) {
            
            // 3 - 34
            $favoritesSup = array();

            foreach ($suppliers as $key => $supplier) {

                foreach ($favoritesSuppliers as $favorites) {
                    
                    if($favorites['supplierId'] == $supplier['id']) {
                        
                        $supplier['favorite'] = 1;
                        $favoritesSup[] = $supplier;
                        unset($suppliers[$key]);

                    } 
                }

            }
            
            
            $allSuppliers = array_merge($favoritesSup,$suppliers);
            
            return $allSuppliers;

//            echo "<pre>";
//                echo count($originalSup).' - '.count($allSuppliers).'</br></br>';
//                print_r($allSuppliers);
//            die();
            
        } else {
            
            return $suppliers;
            
        }
        
        

    }
    
    
    
    public function cleanPhone($phone) {
        
        
        $filter = str_replace("-", "", $phone);
        $filter = str_replace(" ", "", $filter);
        $filter = str_replace("+972", "0", $filter);
        
        return $filter;
        
        
    }
    
    public function returnNoCellsPhone($phone) {
        
        if( strlen($phone) >= 9 && strlen($phone) <= 13 ) { // 13 -> 972
            
            $contentPhone =  preg_replace('/[^0-9]/', '', $phone);
                    
            $phoneStart3 = substr($contentPhone, 0,3);
            $phoneStart2 = substr($contentPhone, 0,2);
            
            if(
                $phoneStart2=='02' ||
                $phoneStart2=='03' ||
                $phoneStart2=='04' ||
                $phoneStart2=='08' ||
                ($phoneStart2=='09' && !$phoneStart3=='972' ) ||
                $phoneStart3=='072' ||
                $phoneStart3=='073' ||
                $phoneStart3=='074' ||
                $phoneStart3=='076' ||
                $phoneStart3=='077' ||
                $phoneStart3=='078') {

                    return $phone;
                    
            } else {
                return NULL;
            }
                    
            
        } return '1';
        
    }
                
    
    public function getBitlyUrl($long_url) {
        
        $apiv4 = 'https://api-ssl.bitly.com/v4/bitlinks';
        $genericAccessToken = '45d736689fd9e0855dfdfe1c29c708498c5130ac';

        $data = array(
            'long_url' => $long_url
        );
        $payload = json_encode($data);

        $header = array(
            'Authorization: Bearer ' . $genericAccessToken,
            'Content-Type: application/json',
            'Content-Length: ' . strlen($payload)
        );

        $ch = curl_init($apiv4);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($ch);
        $resultToJson = json_decode($result);

        if (isset($resultToJson->link)) {
            return $resultToJson->link;
        }
        else {
            return false;
        }
    }
    
    
    
    public function CheckLen($string,$stringLenght,$whereComplete,$characterComplete) {
    
        // $whereComplete = 'start' : 'end'
        
        $string =  substr($string,0,$stringLenght);
        
        $initialStringLength = mb_strlen($string,'utf8');
        
        if( $initialStringLength < $stringLenght ) {
            
            
            $return = $string;
            
            $totalToAdd = $stringLenght - $initialStringLength;
            $add = $characterComplete;

            for ($index = 1; $index < $totalToAdd; $index++) {

                $add .= $characterComplete;

            }
            
            if($whereComplete == 'start') {
                $return = $add.$string;
            } else {
                $return = $string.$add;
            }
            
            return $return;
            
            
        } 
        
        else {
            
            return $string;
            
        }
        
        
        
    }
    
    
    public function CheckUsernameSupplierExist($userName) {
        
        
        if(empty($userName)) {
            
            return rand(11111,99999);
            
        } else {
            
            $this->db->select('id');
            $this->db->from('suppliers');
            $this->db->where('username', $userName);
            $result= $this->db->get();
            $username = $result->row_array();

            if(!empty($username)) {

                $futureId = $this->msite->get_max('suppliers', 'id') + 1;

                return $userName.'.'.$futureId;

            } else {

                return $userName;

            }
            
        }
        
        
    }
    
    
    public function CheckUsernameClientExist($userName='') {
        
        
        if(empty($userName)) {
            
            return rand(11111,99999);
            
        } else {
            
            $this->db->select('id');
            $this->db->from('leadsLandpage');
            $this->db->where('user', $userName);
            $result= $this->db->get();
            $username = $result->row_array();

            if(!empty($username)) {

                $futureId = $this->msite->get_max('leadsLandpage', 'id') + 1;

                return $userName.'.'.$futureId;

            } else {

                return $userName;

            }
            
        }
        
        
    }
    
    
    public function checkTotalPrice($jsonPosts) {
        
        $this->db->select('TotalPrice,OrderId');
        $this->db->from('leadsLandpage');
        $this->db->where('id', $jsonPosts['rowId']);
        $result= $this->db->get();
        $userExistData = $result->row_array();
        
        $this->db->select('TotalPrice');
        $this->db->from('buys');
        $this->db->where('userId', $jsonPosts['rowId']);
        $this->db->where('OrderId !=',$userExistData['OrderId']);
        $result= $this->db->get();
        $moreBuys = $result->result_array();
        
        if(!empty($moreBuys)) {
            
            foreach ($moreBuys as $buy) {
                $userExistData['TotalPrice'] = $userExistData['TotalPrice'] + $buy['TotalPrice'];
            }
            
        }
        
        
        if( ($userExistData['TotalPrice'] != $jsonPosts['totalPrice']) && $jsonPosts['totalPrice'] > 0) {
            
            if( (float)$jsonPosts['totalPrice'] > 5000) {
                $jsonPosts['totalPrice'] = 5000;
            }
            
            $data = array(
                'status' => 1,
                'created_at' => date("Y-m-d H:i:s"),
                'userId' => $jsonPosts['userId'],
                'clientId' => $jsonPosts['rowId'],
                'fromValue' => $userExistData['TotalPrice'],
                'toValue' => $jsonPosts['totalPrice']
            );

            $insert = $this->db->insert('changedBuyLog', $data); 
            $insert_id = $this->db->insert_id();
            
            if($insert_id) {
                
                $this->db->where('userId', $jsonPosts['rowId']);
                $delette = $this->db->delete('buys');
                
                return true; 
            } else {
                return false;
            }
            
        } else {
            return false; 
        }
        
    }
    
    
    
}


