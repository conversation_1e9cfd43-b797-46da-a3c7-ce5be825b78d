import React, { Component } from "react";
import { animateScroll as scroll } from "react-scroll"; /* https://www.npmjs.com/package/react-scroll */
import { getSafeUserData } from "../../context/AuthContext";
import { NavLink, useLocation } from "react-router-dom";
import BulletsMenu from "./BulletsMenu";

import logo from "../../img/sherut-leumi/logoNavRightNew.svg";
import xMenu from "../../img/sherut-leumi/svg/xMenu.svg";
import AvatarMenuPic from "./closeApp/AvatarMenuPic";

import { RestUrls } from "../../Components/-Helpers-/config";

import ShowSocialNetworks from "./ShowSocialNetworks";

//import { getAllUrlParams } from "./../../Components/-Helpers-/UrlParameters";

export default class NavMobileMenuContent extends Component {
  constructor(props) {
    super(props);

    let user = getSafeUserData() || {};

    this.state = {
      thisUser: null,
    };
    console.log(user);

    this.state = {
      loading: false,
      bounceClass: "",
      sayeretFlag: 0,
      sayarotNumber: user.Sayarot.length,
      thisUser: null,
    };
  }
  componentDidMount() {
    const storedUser = getSafeUserData();
    const { Category, InService } =
      storedUser || { Category: "null", InService: "null" };
    this.setState({
      thisUser: {
        Category,
        InService,
      },
    });
    console.log(this.state.thisUser, "this.state.thisUser.InServic");
  }
  
  handleCloseMenu = () => {
    if (this.props.onClose) {
      this.props.onClose();
    }
  }
  
  render() {
    const props = this.props.allProps;

    //const host = window.location.host;
    //const isDevUser = (host.includes("localhost:") || host.includes(".vercel.app") || host.includes("**********:") ) ? true : false ;

    let user = getSafeUserData() || {};
    const isUser =
      localStorage.getItem("userData") &&
      localStorage.getItem("userData").length > 0
        ? true
        : false;

    let bgDesktop = RestUrls.img;
    let currentPage = props.page;

    //for hide items
    //const urlQuery = getAllUrlParams(window.location.href);

    const navLinkClasses = "w-full block border-b border-gray-700 no-underline py-4 px-5 min-h-[52px] transition-all duration-200 flex items-center";
    const navLinkActiveClasses = "font-bold bg-[#1991d0]";
    
    // בדיקה האם העמוד הנוכחי תואם לנתיב
    const isCurrentPage = (pageName) => {
      return currentPage === pageName;
    };

    return (
      <div className="flex flex-col h-full bg-[#0c213a] text-white">
        <header className="flex items-center justify-between p-4 border-b border-gray-700">
          <img 
            src={xMenu} 
            alt="סגירה" 
            className="w-6 h-6 cursor-pointer filter invert" 
            onClick={this.handleCloseMenu}
          />

          <a
            className="mx-auto"
            href="https://sherut-leumi.co.il/"
            target="_self"
            rel="noopener noreferrer"
          >
            <img src={logo} alt="לוגו האגודה להתנדבות" className="h-10" />
          </a>
        </header>

        {isUser ? (
          <div className="flex flex-col items-center py-4 border-b border-gray-700">
            <AvatarMenuPic user={user} className="mb-2" />

            <div className="text-center">
              {user.Sex === "2" ? (
                <h3 className="text-lg font-medium text-white">ברוכה הבאה {user.FirstName + " " + user.LastName}</h3>
              ) : (
                <h3 className="text-lg font-medium text-white">ברוך הבא {user.FirstName + " " + user.LastName ?? ""}</h3>
              )}
            </div>
          </div>
        ) : (
          false
        )}

        <nav className="flex-1 overflow-y-auto">
          <NavLink
            className={({ isActive }) => 
              isCurrentPage("userData") 
                ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
            }
            role="menuitem"
            onClick={() => {
              scroll.scrollToTop({ duration: 0 });
              this.handleCloseMenu();
            }}
            to="/userConsole/data"
          >
            <BulletsMenu name="userData" />
            <span className="mr-2">פרטים אישיים</span>
          </NavLink>
          {this.state.thisUser && this.state.thisUser.InService === true && (
            <NavLink
              className={({ isActive }) => 
                isCurrentPage("clockInOutIndex")
                  ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                  : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
              }
              role="menuitem"
              onClick={() => {
                scroll.scrollToTop({ duration: 0 });
                this.handleCloseMenu();
              }}
              to="/userConsole/clockInOut"
            >
              <BulletsMenu name="clockInOutIndex" />
              <span className="mr-2">נוכחות</span>
            </NavLink>
          )}

          <NavLink
            className={({ isActive }) => 
              isCurrentPage("training")
                ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
            }
            role="menuitem"
            onClick={() => {
              scroll.scrollToTop({ duration: 0 });
              this.handleCloseMenu();
            }}
            to="/userConsole/training"
          >
            <BulletsMenu name="userData" />
            <span className="mr-2">הכשרות</span>
          </NavLink>

          <NavLink
            className={({ isActive }) => 
              isCurrentPage("files")
                ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
            }
            role="menuitem"
            onClick={() => {
              scroll.scrollToTop({ duration: 0 });
              this.handleCloseMenu();
            }}
            to="/userConsole/files"
          >
            <BulletsMenu name="files" />
            <span className="mr-2">מסמכים</span>
          </NavLink>
          
          {(this.state.thisUser &&
            this.state.thisUser.InService === false &&
            this.state.thisUser.Category === "1") ||
          (this.state.thisUser &&
            this.state.thisUser.InService === false &&
            this.state.thisUser.Category === "2" && (
              <a 
                role="menuitem" 
                href="/sherutPlaces" 
                target="_self"
                onClick={this.handleCloseMenu}
                className={`${navLinkClasses} text-white hover:bg-[#1b3d65]`}
              >
                <BulletsMenu name="sherutPlaces" />
                <span className="mr-2">מקומות שרות</span>
              </a>
            ))}
          <NavLink
            className={({ isActive }) => 
              isCurrentPage("userIndex") || isCurrentPage("userSearch")
                ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
            }
            role="menuitem"
            onClick={() => {
              scroll.scrollToTop({ duration: 0 });
              this.handleCloseMenu();
            }}
            to="/userConsole"
          >
            <BulletsMenu
              name="bulletLev"
              sayarotNumber={this.state.sayarotNumber}
            />
            <span className="mr-2">סיירות</span>
          </NavLink>

        { process.env.REACT_APP_ENVIRONMENT === "dev" && (
            <NavLink
            className={({ isActive }) => 
              isCurrentPage("digitalCard")
                ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
            }
            role="menuitem"
            onClick={() => {
              scroll.scrollToTop({ duration: 0 });
              this.handleCloseMenu();
            }}
            to="/userConsole/digitalCard"
          >
            <BulletsMenu name="digitalCard" />
            <span className="mr-2">כרטיס דיגיטלי</span>
          </NavLink>
        )}
          <NavLink
            className={({ isActive }) => 
              isCurrentPage("editAvatar")
                ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
            }
            role="menuitem"
            onClick={() => {
              scroll.scrollToTop({ duration: 0 });
              this.handleCloseMenu();
            }}
            to="/userConsole/editAvatar"
          >
            <BulletsMenu name="userData" />
            <span className="mr-2">עריכת אווטאר</span>
          </NavLink>

          <NavLink
            className={({ isActive }) => 
              isCurrentPage("contactUs")
                ? `${navLinkClasses} ${navLinkActiveClasses} text-white` 
                : `${navLinkClasses} text-white hover:bg-[#1b3d65]`
            }
            role="menuitem"
            onClick={() => {
              scroll.scrollToTop({ duration: 0 });
              this.handleCloseMenu();
            }}
            to="/userConsole/contactUs"
          >
            <BulletsMenu name="contact" />
            <span className="mr-2">צור קשר</span>
          </NavLink>

          <a
            className={`${navLinkClasses} text-white hover:bg-[#1b3d65]`}
            href="/"
            target="_self"
            onClick={() => {
              localStorage.clear();
              this.handleCloseMenu();
            }}
          >
            <BulletsMenu name="logOff" />
            <span className="mr-2">להתנתק</span>
          </a>
        </nav>

        <div className="mt-auto p-4 border-t border-gray-700">
          <ShowSocialNetworks />

          <a
            href="https://waveproject.co.il/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs font-bold text-gray-400 text-center block mt-3 hover:text-gray-200"
          >
            Powered by WaveProject
          </a>
        </div>
      </div>
    );
  }
}
