<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>iteWs extends CI_Model {
    
    const TABLE_PAGES = 'pages';
    const TABLE_OBJECTS = 'objects';
    const TABLE_SEO = 'seo';
    const TABLE_GALLERY = 'gallery';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function get_settings($lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_OBJECTS);
        $this->db->where('page_id', 0);
        if($lang) {
            $this->db->group_start();
            $this->db->where('lang', $lang);
            $this->db->or_where('lang', NULL);
            $this->db->group_end();
        }
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                $data[$row['keyword']] = $row['value'];//new Obj($row);
            }
            
            $data['base_url'] = base_url();
            
            return $data;
        }
        return NULL;
    }
    
    public function get_pagesWs($pagesObjects) {
        
        foreach ($pagesObjects as $key => $value) {
            
            $seo1 = $value->Arr('seo'); $seo = $seo1->Arr();
            $seo_data = array (
                'friendly' => $seo['friendly'],
                'description'=> $seo['description'],
                'method' => $seo['method'],
                'canonical' => str_replace("sorokafoundation.ak-digital.co.il/api", "www.sorokafoundation.co.il",  base_url()).$seo['friendly'],
                'controller' => $value->Arg('controller'),
            );
            
            $pagesArray[] = array (
                'id' => $value->Arg('id'),
                'status'  => $value->Arg('status'),
                'title' => $value->Arg('title'),
                
                'seo' => $seo_data
            );
            
        }
        
        return $pagesArray;
        
    }
    
    
    
    
    
    public function objects_to_Array($Objects,$shortSeo = FALSE) {
        
        if(!empty($Objects)) {
            foreach ($Objects as $key => $value) {

                $seo1 = $value->Arr('seo'); $seo = $seo1->Arr();
                
                if($shortSeo) {
                    $seo_data = array (
                        'friendly' => $seo['friendly'],
                        //'method' => $seo['method'],
                        //'canonical' => $seo['canonical'],
                        //'controller' => $value->Arg('controller'),
                        //'title'=> $seo['title'],
                        //'description'=> $seo['description'],
                    );
                }
                
                else {
                    $seo_data = array (
                        'friendly' => $seo['friendly'],
                        'method' => $seo['method'],
                        'canonical' => $seo['canonical'],
                        'controller' => $value->Arg('controller'),
                        'title'=> $seo['title'],
                        'description'=> $seo['description'],
                    );
                }

                $data = $value->Arr();
                
                unset($data['created_at']);
                unset($data['updated_at']);
                unset($data['status']);
                unset($data['sort']);
                unset($data['seo_id']);
                unset($data['seo']);
                unset($data['lang']);

                $Array[] = array (
                    'data' => $data,
                    'seo' => $seo_data
                );

            }
        }
        else {
             $Array[] = array();
        }
        
        return $Array;
        
    }
    
    
    public function objects_to_ArrayNoseo($Objects) {
        
        if(!empty($Objects)) {
            foreach ($Objects as $key => $value) {
                $data[$key] = $value->Arr();
                
                unset($data[$key]['created_at']);
                unset($data[$key]['updated_at']);
                unset($data[$key]['lang']);
                unset($data[$key]['seo']);
                unset($data[$key]['seo_id']);
                
                unset($data[$key]['status']);
                unset($data[$key]['sort']);
            }
        }
        
        return $data;
        
    }
    
    public function objects_to_ArrayNoseoData($Objects) {
        
        $data = false;
        
        if(!empty($Objects)) {
            foreach ($Objects as $key => $value) {
                $data[$key]['data'] = $value->Arr();
                
                unset($data[$key]['data']['created_at']);
                unset($data[$key]['data']['updated_at']);
                unset($data[$key]['data']['lang']);
                unset($data[$key]['data']['seo']);
                unset($data[$key]['data']['seo_id']);
                
                unset($data[$key]['data']['status']);
                unset($data[$key]['data']['sort']);
            }
        }
        
        return $data;
        
    }
    
    
    
    public function object_to_Array($value) {
            
        $seo1 = $value->Arr('seo'); $seo = $seo1->Arr();
        $seo_data = array (
            'friendly' => $seo['friendly'],
            'method' => $seo['method'],
            'canonical' => $seo['canonical'],
            'title'=> $seo['title'],
            'description'=> $seo['description'],
            'controller' => $value->Arg('controller'),
        );

        $data = $value->Arr();

        $Array = array (
            'data' => $data,
            'seo' => $seo_data
        );
        
        return $Array;
        
    }
    
    
    
    public function ProjetsLow($Objects) {
        
        foreach ($Objects as $key => $value) {
            
            $seo1 = $value->Arr('seo'); $seo = $seo1->Arr();
            $seo_data = array (
                'friendly' => $seo['friendly'],
                'method' => $seo['method'],
                'canonical' => $seo['canonical'],
                'controller' => $value->Arg('controller'),
            );

            //$data = $value->Arr();
            
            $data = array (
                'id' => $value->Arg('id'),
                'name' => $value->Arg('name'),
                'statusProj' => $value->Arg('statusProj'),
                'city' => $value->Arg('city'),
                'smallPic' => $value->Arg('smallPic')
            );
            
            
            $Array[] = array (
                'data' => $data,
                'seo' => $seo_data
            );
            
        }
        
        return $Array;
        
    }   
    
    
    
    public function getPostFromJson($postNames=array()) {
        
        if(!empty($postNames)) {
            
            $postValues = array();

            
            $contentType = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';

            if ($contentType === "application/json") {
                //Receive the RAW post data.
                $content = trim(file_get_contents("php://input"));
                $decoded = json_decode($content, true);

                //If json_decode failed, the JSON is invalid.
                if(!is_array($decoded)) {
                    return "NO ARRAY";
                } else {
                    foreach ($postNames as $postName) {
                        $postValues[$postName] = isset($decoded[$postName]) ? $decoded[$postName] : '';
                    }
                    return $postValues;
                }
            }
            
            else {
               return FALSE;
           }
           
        } else {
            return FALSE;
        }
        
    }
    
    public function checkUserCredentials($pageAutoriced = NULL,$jsonPosts = NULL,$usersCode) {
        
        if(!empty($jsonPosts)) {
            $tokenCheck = JWT::decode($jsonPosts['token'],$usersCode,true);
            $autorizeFlag = FALSE;
            
            
            foreach ($pageAutoriced as $key => $value) {
                
                if(     ($jsonPosts['userId'] == $tokenCheck) AND
                        ($jsonPosts['userCredential'] == $value OR $value == 'all')) {
                    $autorizeFlag = TRUE;
                }
            }
            
            if (!$autorizeFlag) {
                return 'unauthorized';
                //die('unauthorized');
            };
        }
        
        else {
             return 'unauthorized';
        }
    }
    
    
    public function checkPageAuth($pageAutoriced,$auth) {
        
        $flagOk = false;
        
        if  (
                ( isset($pageAutoriced) && !empty($pageAutoriced) ) &&
                ( isset($auth) && !empty($auth) ) 
            ) {
            
            foreach ($pageAutoriced as $value) {
                
                if( $value == $auth or $pageAutoriced == 'all' ) {
                        return true;
                }
            }
        }
        
        if(!$flagOk) {
            return false;
        }
        
    }
    
    
    public function cleanPhone($phone) {
        
        
        $filter = str_replace("-", "", $phone);
        $filter = str_replace(" ", "", $filter);
        $filter = str_replace("+972", "0", $filter);
        
        return $filter;
        
        
    }
    
    
    
    
    
    
    public function get_page_with_objectsArray($page_id, $lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('id', $page_id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();

            
            $this->db->select('*');
            $this->db->from(self::TABLE_OBJECTS);
            $this->db->where('page_id', $page_id);
            if($lang) {
                $this->db->group_start();
                $this->db->where('lang', $lang);
                $this->db->or_where('lang', NULL);
                $this->db->group_end();
            }
            $result1 = $this->db->get();
            if($result1->num_rows() > 0) {
                foreach($result1->result_array() as $row1) {
                    $row[$row1['keyword']] = $row1['value'];
                }
            }
            
            unset($row['parent_id']);
            unset($row['seo_id']);
            unset($row['lang']);
            unset($row['created_at']);
            unset($row['updated_at']);
            unset($row['status']);
            unset($row['sort']);
            unset($row['he_title']);
            unset($row['en_title']);
            unset($row['ru_title']);
            
            return $row;
        }
        return NULL;
    }
    
    public function getParamPages() {
        
               
        $return = array (
            'categories' => $this->getLowSeoPages('categories'),
            //'success' => $this->getLowSeoPages('success'),
            //'galleries' => $this->getLowSeoPages('galleries'),
            //'articles' => $this->getLowSeoPages('articles'),
            //'events' => $this->getLowSeoPages('events')
        );
        
        return $return;
        //print_r($donations);
    }
    
    
    
    public function getLowSeoPages($table) {
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $array1 = $this->msite->get_all_objects($table);
        $results = $this->msiteWs->objects_to_Array($array1);
        
        $resultsOut = array();
        
        if(!empty($results) && isset($results[0]['seo'])) {
            
            foreach ($results as $value) {
                
                $seo = array (
                    'friendly' => $value['seo']['friendly'],
                    'method' => $value['seo']['method'],
                    'title' => $value['seo']['title'],
                    'description' => $value['seo']['description']
                );
                
                $resultsOut[] = array (
                    'id' => $value['data']['id'],
                    'seo' => $seo
                );
                
            }
        };
                    
        
        return $resultsOut;
        
    }
    
    
    public function getLowSeoPage($table,$id) {
        
        $array1 = $this->msiteWs->get_object($table, $id);
        $results = $this->msiteWs->object_to_Array($array1);
        
        
        
        if(!empty($results['seo'])) {
            
            $seo = array (
                    'friendly' => $results['seo']['friendly'],
                    'method' => $results['seo']['method'],
                    'title' => $results['seo']['title'],
                    'description' => $results['seo']['description']
            );

            $resultsOut = array (
                'id' => $results['data']['id'],
                'seo' => $seo
            );
        };
                    
        
        return $resultsOut;
        
    }
    
    
    
    
    
    
    
    
    
    
    public function get_all_pages($parent_id = 0, $lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('parent_id', $parent_id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if($lang) {
                    foreach($row as $key => $val) {
                        $idx = str_replace($lang . "_", "", $key);
                        $row[$idx] = $val;
                        
                    }
                }
                $row['childs'] = $this->get_all_pages($row['id'], $lang);
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id'], $lang);
                }
                
                //$data[] = new Obj($row);
                $page_key = $row['controller'] . '_' . $row['method'];
                $data[$page_key] = $row;
            }
            return $data;
        }
        return NULL;
    }
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    public function select($fields) {
        $this->db->select($fields);
    }
    
    public function set_where($where) {
        $this->db->where($where);
    }
    
    public function set_where_like($field_to_search, $q, $sides = 'both') {
        $this->db->group_start();
        if(is_array($field_to_search)) {
            if(!empty($field_to_search)) {
                foreach($field_to_search as $key => $field) {
                    if($key == 0) {
                        $this->db->like($field, $q, $sides);
                    } else {
                        $this->db->or_like($field, $q, $sides);
                    }
                }
            }
        } else {
            $this->db->like($field_to_search, $q, $sides);
        }
        $this->db->group_end();
    }
    
    public function join($table, $on, $inner) {
        $this->db->join($table, $on, $inner);
    }
    
    public function group($field) {
        $this->db->group_by($field);
    }
    
    public function get_max($table, $col) {
        $this->db->select_max($col);
        $result = $this->db->get($table); 
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return $row[$col];
        }
        return FALSE;
    }
    
    
    public function get_page_with_objects($page_id, $lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('id', $page_id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
//            $row['childs'] = $this->get_all_pages($row['id']);
//            if(isset($row['seo_id'])) {
//                $row['seo'] = $this->get_seo($row['seo_id'], $lang);
//            }
            
            $this->db->select('*');
            $this->db->from(self::TABLE_OBJECTS);
            $this->db->where('page_id', $page_id);
            if($lang) {
                $this->db->group_start();
                $this->db->where('lang', $lang);
                $this->db->or_where('lang', NULL);
                $this->db->group_end();
            }
            $result1 = $this->db->get();
            if($result1->num_rows() > 0) {
                foreach($result1->result_array() as $row1) {
                    $row[$row1['keyword']] = $row1['value'];
                }
            }
            return new Obj($row);
        }
        return NULL;
    }
    
    public function get_page($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            $row['childs'] = $this->get_all_pages($row['id']);
            if(isset($row['seo_id'])) {
                $row['seo'] = $this->get_seo($row['seo_id']);
            }
            return new Obj($row);
        }
        return NULL;
    }
    
    public function get_page_childs($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('parent_id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id']);
                }
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_page_parent($parent_id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('id', $parent_id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            if(isset($row['seo_id'])) {
                $row['seo'] = $this->get_seo($row['seo_id']);
            }
            return new Obj($row);
        }
        return NULL;
    }
    
    
    
    public function insert_page($params) {
        $this->db->insert(self::TABLE_PAGES, $params); 
        return $this->db->insert_id();
    }
    
    public function update_page($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_PAGES, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_page($id) {
        $obj = $this->get_page($id);
        $this->delete_seo($obj->Arg('seo_id'));
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_PAGES);
        return $this->db->affected_rows();
    }
    
    
    
    public function get_page_object($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_OBJECTS);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return new Obj($row);
        }
        return NULL;
    }
    
    public function get_all_page_objects($page_id = FALSE, $type = FALSE) {
        $this->db->select('*');
        $this->db->from(self::TABLE_OBJECTS);
        $this->db->where('page_id', $page_id);
        if($type) {
            $this->db->where('type', $type);
        }
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                //$data[$row['keyword']] = new Obj($row);
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function insert_page_object($params) {
        $this->db->insert(self::TABLE_OBJECTS, $params); 
        return $this->db->insert_id();
    }
    
    public function update_page_object($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_OBJECTS, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_page_object($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_OBJECTS);
        return $this->db->affected_rows();
    }
    
    
    
    
    
    
    
    public function searching($table, $field_to_search, $q, $limit = 100) {
        $this->db->select('*');
        $this->db->from($table);
        $this->db->like($field_to_search, $q, 'both');
        $this->db->limit($limit);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            return $result->result_array();
        }
        return NULL;
    }
    
    public function limit_objects($page = 1, $limit = 100) {
        $this->db->limit($limit, $limit * ($page - 1));
    }
    
    public function sort_objects($order = "id", $sort = "DESC") {
        $this->db->order_by($order, $sort);
    }
    
    public function count_all_objects($table) {
        $this->db->select('*');
        $this->db->from($table);
        return $this->db->count_all_results();
    }
    
    public function distinct_all_objects($table, $field, $other_fields = NULL, $lang = NULL) {
        if($other_fields) {
            $this->db->select('DISTINCT('.$table . '.'.$field.'), ' . $other_fields);
        } else {
            $this->db->select('DISTINCT('.$table . '.'.$field.')');
        }
        $this->db->from($table);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id'], $lang);
                }
                if($lang) {
                    foreach($row as $key => $val) {
                        $idx = str_replace($lang . "_", "", $key);
                        $row[$idx] = $val;
                    }
                }
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_all_objects($table, $lang = NULL, $filtersArray = FALSE) {
        
        
        if($filtersArray) {
            
            foreach ($filtersArray as $value) {
                $this->db->select($table . '.'.$value);
            }
        } else {
            $this->db->select($table . '.*');
        }
        
        $this->db->from($table);
        $result = $this->db->get();
        
        
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id'], $lang);
                }
                if($lang) {
                    foreach($row as $key => $val) {
                        $idx = str_replace($lang . "_", "", $key);
                        $row[$idx] = $val;
                    }
                }
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_object($table, $id, $lang = NULL) {
        $this->db->select('*');
        $this->db->from($table);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            if(isset($row['seo_id'])) {
                $row['seo'] = $this->get_seo($row['seo_id'], $lang);
            }
            if($lang) {
                foreach($row as $key => $val) {
                    $idx = str_replace($lang . "_", "", $key);
                    $row[$idx] = $val;
                }
            }
            return new Obj($row);
        }
        return FALSE;
    }
    
    public function insert_object($table, $params) {
        $fields = $this->db->field_data($table);
        if($fields) {
            $data = array();
            foreach ($fields as $field) {
                if(isset($params[$field->name])) {
                    $data[$field->name] = $params[$field->name];
                }
            }
            $this->db->insert($table, $data); 
            return $this->db->insert_id();
        }
        return FALSE;
    }
    
    public function update_object($table, $id, $params) {
        $fields = $this->db->field_data($table);
        if($fields) {
            $data = array();
            foreach ($fields as $field) {
                if(isset($params[$field->name])) {
                    $data[$field->name] = $params[$field->name];
                }
            }
            $this->db->where('id', $id);
            $this->db->update($table, $data); 
            return $this->db->affected_rows();
        }
        return FALSE;
    }
    
    public function delete_object($table, $id) {
        $obj = $this->get_object($table, $id);
        $this->delete_seo($obj->Arg('seo_id'));
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }
    
    
    
    
    
    public function get_all_gallery($keyword) {
        $this->db->select('*');
        $this->db->from(self::TABLE_GALLERY);
        $this->db->where('keyword', $keyword);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_gallery($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_GALLERY);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return new Obj($row);
        }
        return FALSE;
    }
    
    public function insert_gallery($params) {
        $this->db->insert(self::TABLE_GALLERY, $params); 
        return $this->db->insert_id();
    }
    
    public function update_gallery($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_GALLERY, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_gallery($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_GALLERY);
        return $this->db->affected_rows();
    }
    
    
    
    
    
    
    
    public function get_seo_id($controller, $method, $param) {
        $this->db->select("*");
        $this->db->from(self::TABLE_SEO);
        $this->db->where('controller', $controller);
        $this->db->where('method', $method);
        $this->db->group_start();
        if(!empty($param)) {
            $this->db->or_where('param', $param);
        }
        else {
            $this->db->or_where('param', NULL);
            $this->db->or_where('param', '');
            $this->db->or_where('param', 0);
        }
        $this->db->group_end();
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return $row['id'];
        }
        return FALSE;
    }
    
    public function get_seo($id, $lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_SEO);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            //print_r($id);die();
            if($lang) {
                foreach($row as $key => $val) {
                    $idx = str_replace($lang . "_", "", $key);
                    $row[$idx] = $val;
                }
            }
            
            return new Obj($row);
        }
        return FALSE;
    }
    
    public function insert_seo($params) {
        $this->db->insert(self::TABLE_SEO, $params); 
        return $this->db->insert_id();
    }
    
    public function update_seo($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_SEO, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_seo($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_SEO);
        return $this->db->affected_rows();
    }
    
    public function sitemap_search($q, $page = 1, $limit = 50) {
        $pager = $limit * ($page - 1);

        $title = 'title';
        $description = 'description';
        $friendly = 'friendly';
        $query = "
            (SELECT *, MATCH($friendly, $title, $description) AGAINST('*$q*' IN BOOLEAN MODE) AS score 
            FROM seo 
            WHERE MATCH($friendly, $title, $description) AGAINST('*$q*' IN BOOLEAN MODE)) 
            ORDER BY score DESC LIMIT ".$limit." OFFSET ".$pager."
        ";
        $result = $this->db->query($query);
        if ($result->num_rows() > 0) {
            return $result->result_array();
        }
        return false;
    }
    
    public function sitemap_search_total($q) {

        $title = 'title';
        $description = 'description';
        $friendly = 'friendly';
        $query = "
            (SELECT COUNT(id) AS total 
            FROM seo 
            WHERE MATCH($friendly, $title, $description) AGAINST('*$q*' IN BOOLEAN MODE)) 
        ";
        $result = $this->db->query($query);
        if ($result->num_rows() > 0) {
            $row = $result->row_array();
            return isset($row['total']) ? $row['total'] : 0;
        }
        return 0;
    }
    
    public function update_site_map() {
        $freq = "always";
        $priority = "1";
        $_SITEMAP = dirname(APPPATH) . '/sitemap.xml';
        $pf = fopen ($_SITEMAP, "w");
        if ($pf) {
            fwrite ($pf, "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" .
                 "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"\n" .
                 "        xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" .
                 "        xsi:schemaLocation=\"http://www.sitemaps.org/schemas/sitemap/0.9\n" .
                 "        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd\">\n" .
                 "  <url>\n" .
                 "    <loc>".base_url()."/</loc>\n" .
                 "    <changefreq>daily</changefreq>\n" .
                 "  </url>\n");
            
        }
        $this->db->select('*');
        $this->db->from(self::TABLE_SEO);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            foreach($result->result_array() as $row) {
                if($row['robots'] !== 'index, follow') {
                    continue;
                }

                $url = $row['canonical'];
                fwrite ($pf, "  <url>\n" .
                    "    <loc>" . $url ."</loc>\n" .
                    "    <changefreq>$freq</changefreq>\n" .
                    "    <priority>$priority</priority>\n" .
                    "  </url>\n");
            }
        }
        
        fwrite ($pf, "</urlset>\n");
        fclose ($pf);
        return TRUE;
    }
    
    public function search_id_in_object($array_objects,$id, $return) {
        foreach ($array_objects as $value) {
            if($value->Arg('id')==$id) {
                foreach ($return as $value2) {
                  $result[$value2] =  $value->Arg($value2);
                }
                return $result;
            }
        }
    }
    
    
    
    public function getSearchItems() {
        //$this->msite->limit_objects(1, 6);
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('busType');
        
        foreach ($bustypeDB as $key => $value) {
            $bustype[] = array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title'),
                    'title_en' => $value->Arg('title_en')
                );
        }
        
        $SearchItems['busType'] = $bustype;
        // ---------------------------------------------------------------
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('BusManufacturer');
        
        foreach ($bustypeDB as $key => $value) {
            $BusManufacturer[] =  array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title')
                );
        }
        
        $SearchItems['BusManufacturer'] = $BusManufacturer;
        // ---------------------------------------------------------------
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('gearType');
        
        foreach ($bustypeDB as $key => $value) {
            $BusgearType[] =  array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title'),
                    'en_title' => $value->Arg('en_title')
                );
        }
        
        $SearchItems['gearType'] = $BusgearType;
        // ---------------------------------------------------------------
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('engineType');
        
        foreach ($bustypeDB as $key => $value) {
            $BusengineType[] =  array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title'),
                    'en_title' => $value->Arg('en_title')
                );
        }
        
        $SearchItems['engineType'] = $BusengineType;
        // ---------------------------------------------------------------
        
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $busesDB = $this->distinct_all_objects('buses','title');
        
        
        foreach ($busesDB as $key => $value) {
            $buses[] = array (
                    'title' => $value->Arg('title')
                );
        }
        
        $SearchItems['buses'] = $buses;
        // ---------------------------------------------------------------
        
        
        return $SearchItems;
        
        
        //$this->data['gallery'] = $this->msite->get_all_objects('gallery');
    }
    
    
    public function showTitleFromId ($array,$searchField,$id,$ResultField) {
        
        $return = FALSE;
        
        foreach ($array as $key => $value) {
            if($value[$searchField]==$id) {
                $return = $value[$ResultField];
            }
        }
        
        return $return;
        
        
        
    }
    

    public function get_all_buses($excludeId=NULL,$SearchItems=NULL, $lang=NULL) {
        
        
        $table = self::TABLE_BUSES;
        
        $this->db->from($table);
        $this->db->select('*');
        
        if($excludeId) {
            $this->db->where("id !='$excludeId'");
        }
        
        
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id']);
                }
                
                
                if(!empty($SearchItems)) {
                    $row['gearTypeName'] = '';
                    foreach ($SearchItems['gearType'] as $value) {
                        if($value['id']== $row['gear']) {
                            
                            if($lang == 'he') {
                                $row['gearTypeName'] = $value['title'];
                            }
                            else {
                                $row['gearTypeName'] = $value[$lang.'_title'];
                            }
                            
                            
                        }
                    };
                }
                
                
                $row['gallery'] = $this->get_galleryBus($row['id']);
                
                if(!empty($row['gallery'])) {
                    
                    $flag = FALSE;
                    
                    foreach ($row['gallery'] as $key => $picture) {
                        
                        //print_r($picture);
                        
                        if(empty($picture['video']) AND !$flag) {
                            $row['mainpic'] = base_url().picIMG.$picture['image'];
                            $flag = TRUE;
                        } 
                    }
                    //die();
                }
                
                else {
                    $row['mainpic'] = base_url().picIMG.'noOtobusPic.png';
                }
                
                
                $data[] = new Obj($row);

            }
            
            
            
            return $data;
        }
        return NULL;
    }
    
    
    public function get_galleryBus($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_BUSESPICTURES);
        $this->db->where('busId', $id);
        $this->db->where('status=1');
        $this->db->order_by('sort', 'DESC');
        
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->result_array();
            
//            if($id==4) {
//                print_r($row);die('sebas');
//            }
            
            return $row;
            
            //return new Obj($row);
        }
        return FALSE;
    }
    
    
    public function run_form($lang = NULL) {
        
        $this->errorMessage = false;
        
        $this->load->library('form_validation');

        if($lang == 'he') {
            $this->config->set_item('language', 'hebrew');
            $this->form_validation->set_rules('busType', 'סוג הרכב', 'trim|required');
            $this->form_validation->set_rules('km', 'ק״מ', 'trim|numeric|required');
            $this->form_validation->set_rules('BusManufacturer', 'יצרן', 'trim|required');
            $this->form_validation->set_rules('engine', 'נפח מנוע', 'trim|required|numeric');
            $this->form_validation->set_rules('title', 'דגם', 'trim|required');
            $this->form_validation->set_rules('engineType', 'סוג מנוע', 'trim|required');
            $this->form_validation->set_rules('year', 'מודל', 'trim|required');
            $this->form_validation->set_rules('gearType', 'תיבת הילוכים', 'trim|required');
            $this->form_validation->set_rules('yad', 'יד', 'trim|required');
            $this->form_validation->set_rules('price', 'מחיר', 'trim|required|numeric');
            $this->form_validation->set_rules('contactName', 'שם', 'trim|required');
            $this->form_validation->set_rules('contactMail', 'דוא״ל', 'trim|required|valid_email');
            $this->form_validation->set_rules('contactPhone', 'טלפון', 'trim|required|min_length[9]');
            $this->form_validation->set_rules('video', 'סרטון', 'trim|valid_url');
            
        }
        else {
            $this->config->set_item('language', 'english');
            
            $this->form_validation->set_rules('busType', 'Type of vehicle', 'trim|required');
            $this->form_validation->set_rules('km', 'Km', 'trim|numeric|required');
            $this->form_validation->set_rules('BusManufacturer', 'Bus Manufacturer', 'trim|required');
            $this->form_validation->set_rules('engine', 'Engine Capacity', 'trim|required|numeric');
            $this->form_validation->set_rules('title', 'model', 'trim|required');
            $this->form_validation->set_rules('engineType', 'Engine Type', 'trim|required');
            $this->form_validation->set_rules('year', 'Year', 'trim|required');
            $this->form_validation->set_rules('gearType', 'Gear Type', 'trim|required');
            $this->form_validation->set_rules('yad', 'Owner', 'trim|required');
            $this->form_validation->set_rules('price', 'Price', 'trim|required|numeric');
            $this->form_validation->set_rules('contactName', 'Contact Name', 'trim|required');
            $this->form_validation->set_rules('contactMail', 'Contact Mail', 'trim|required|valid_email');
            $this->form_validation->set_rules('contactPhone', 'Contact Phone', 'trim|required|min_length[9]');
            $this->form_validation->set_rules('video', 'Video', 'trim|valid_url');
        }
        
        
        
        //https://www.codeigniter.com/userguide3/libraries/form_validation.html
        
        //$this->form_validation->set_rules('upload', 'תמונה1', 'trim|required');

        
        if($this->form_validation->run() == FALSE) {
            if($this->input->post('action') == 'publish') {
                $errors = current($this->form_validation->error_array());
                $this->errorMessage = trim(preg_replace('/\s\s+/', ' ', strip_tags ($errors)));
            }
            
            $this->session->set_flashdata('BusInsertId', FALSE);
            
            //echo "NO POST - NOT SENT";
        } else {
            
            //die('OK');
            
            
            //$uniq = uniqid();
            $insertId = FALSE;
            
            $post = array(
                'created_at' => date("Y-m-d H:i:s"),
                'status' => 0,
                'km' => $this->input->post('km', true),
                'manufacturer' => $this->input->post('BusManufacturer', true),
                'engine' => $this->input->post('engine', true),
                'title' => $this->input->post('title', true),
                'engineType' => $this->input->post('engineType', true),
                'year' => $this->input->post('year', true),
                'gear' => $this->input->post('gearType', true),
                'yad' => $this->input->post('yad', true),
                'price' => $this->input->post('price', true),
                'tv' => $this->input->post('tv', true),
                'handicapped' =>$this->input->post('handicapped', true),
                'abs' => $this->input->post('abs', true),
                'text' => $this->input->post('text', true),
                'tradeIn' => $this->input->post('tradeIn', true),
                'contactName' => $this->input->post('contactName', true),
                'contactMail' => $this->input->post('contactMail', true),
                'contactPhone' => $this->input->post('contactPhone', true),
                'contactText' => $this->input->post('contactText', true)
            );
            
            $insertId = FALSE;
            
            if(empty($this->session->flashdata('BusInsertId'))) {
                $insertId = $this->insertBus($post);
                
                if(!empty($this->input->post('video', true))) {
                    
                    $youtube = $this->input->post('video', true);
                    
                    $post = array(
                        'created_at' => date("Y-m-d H:i:s"),
                        'video' => $youtube,
                        'status' => 1,
                        'busId' => $insertId
                    );

                    $YoutubeInsert = $this->insertBusPictures($post);
                }
                
                $this->session->set_flashdata('BusInsertId', $insertId);
            }
            else {
                $insertId = $this->session->flashdata('BusInsertId');
                $this->updateBus($insertId, $post);
                $this->session->set_flashdata('BusInsertId', $insertId);
            }
            
            if($insertId) {
                $pictures = $this->uploadBusPicutes($insertId);
          
                if(!empty($pictures['error'])) {
                    $this->errorMessage = $pictures['error'];
                }
                
                $return['id'] = $insertId;
                $return['files'] = $pictures['files'];
                $return['done'] = 'YES';
                $return['errors'] = $pictures['error'];
                
                return $return;
                
        } // Insert ID
            
            return false;  // NO Insert ID
        }
        return false;
    }
    
    
    public function uploadBusPicutes($insertId) {
    
    $filename = '';

    $config['upload_path'] = picIMG;
    $config['allowed_types'] = 'jpg|png|jpeg';
    $config['max_size'] = 4048; // Usually 2 MB
    
    
    // $config['max_width'] = 1024; // (in pixels) 
    // $config['max_height'] = 768;
    // $config['min_width'] = 
    // $config['min_height']
    // https://www.codeigniter.com/userguide3/libraries/file_uploading.html
    // $config['file_name'] = uniqid();

    $this->load->library('upload');
    $this->upload->initialize($config); 

    $this->load->library('MY_Upload');
    
    
    
    if(isset($_FILES['userfile']['name'][0]) and !empty($_FILES['userfile']['name'][0]) ) {
        $files = $this->upload->do_multi_upload('userfile', TRUE);
    } else {
        $files = FALSE;
        $noFiles = TRUE;
    }

    $error = FALSE;
    
    if(!$files) {
        if(isset($noFiles)) {
            $error = 'נא לבחור תמונות';
        } else {
            $error = strip_tags($this->upload->display_errors());
        }
        
    } else {

            foreach ($files as $value) {

                $post = array(
                    'created_at' => date("Y-m-d H:i:s"),
                    'image' => $value['file_name'],
                    'status' => 1,
                    'busId' => $insertId
                );

                $LastpictureinsertId = $this->insertBusPictures($post);
                $imageLibrary = $this->ImageLibrary($value['file_name']);

                if(isset($imageLibrary['error'])) {
                    $error = $imageLibrary['error'];
                }

                elseif(isset($imageLibrary['rotate'])) {
                    echo 'image: '.$imageLibrary['rotate'];
                }
            }
        }
        
        $returns = array (
            
            'files' => $files,
            'error' => $error
        );
        
        
        return $returns;
    }
    
    
     public function ImageLibrary($file_name) {
         
         
         
        $_width = '850_w';
        //$_height = $this->input->post($field['name'] . '_h');
        $quality = 80;

        ini_set('memory_limit', '-1');
        
        $this->load->library('simple_img');
        $smpimg = $this->simple_img->initialize(picIMG.$file_name);

//        if($this->input->post($field['name'] . '_crop')) {
//            $smpimg->crop($x_axis, $y_axis, $_width + $x_axis, $_height + $y_axis)
//                ->save(apppicIMG . $data[$field['name']], $quality);
//        }

        $smpimg ->auto_orient()
                ->best_fit(1920, 1080)
                ->save(picIMG.$file_name, $quality);
    }
    
    
    public function insertBus($params) {
        
        
        $params_seo = array (
            'controller' => 'welcome',
            'method' => 'index',
            'title' => $params['title'],
            'robots' => 'index, follow',
            'description' => $params['text']
        );
        $seoId = $this->insert_seo($params_seo);
        
        
        $params['seo_id'] = $seoId;
        $this->db->insert('buses', $params); 
        
        $busInsertId = $this->db->insert_id();
        
        $update_seo = array (
            'param' => $busInsertId,
            'friendly' => url_title($params['title']) . '_' . $busInsertId,
            'title' => $params['title'],
            'description' => $params['text'],
            'he_friendly' => $busInsertId,
            'en_friendly' => $busInsertId,
            'canonical' => base_url().'welcome/index/'.$busInsertId
        );
        
        $update_seoId = $this->update_seo($seoId, $update_seo);
        
        return $busInsertId;
    }
    
    public function updateBus($id, $params) {
        $this->db->where('id', $id);
        $this->db->update('buses', $params); 
        
        
        return $this->db->affected_rows();
    }
    
    
    public function insertBusPictures($params) {
        
        $this->db->insert('busesPictures', $params); 
        return $this->db->insert_id();
    }
    
    public function getbusGallery($busId) {
        
        
        $this->db->select('*');
        $this->db->from('busesPictures');
        $this->db->where('busId', $busId);
        $this->db->where("status='1'");
        $this->db->order_by("sort", "DESC");
        $result = $this->db->get();
        $allGallery = $result->result_array();
        
        
        //print_r($allGallery);die();
        $video = '';
        $pictures = array ();
            
        if(!empty($allGallery)) {
            
            foreach ($allGallery as $key => $picture) {

                if(empty($picture['video'])) {
                    $pictures[] = base_url().picIMG.$picture['image'];
                }
                
                elseif ($picture['video']) {
                    $video = $picture['video'];
                }
            }
            
            $return = array (
                'pictures' => $pictures,
                'video' => $video
            );
            
            return $return;
            
        }

        else {
            $row['mainpic'] = 
            $return = array (
                'pictures' => array(base_url().picIMG.'noOtobusPic.png'),
                'video' => ''
            );
            
            return $return;
        }
        
    }
    
    
    
    
    public function getBus($busId, $lang=NULL) {
        
        $this->db->select('*');
        $this->db->from('buses');
        $this->db->where('id', $busId);
        
        $result = $this->db->get();
                    
        if($result->num_rows() > 0) {
            
            $row = $result->row_array();
            
            $SearchItems = $this->getSearchItems();
            
            if($lang=='he') {
                $prefix = NULL;
            } else {
                $prefix = $lang.'_';
            }
            
            $row['bustype'] = $this->showTitleFromId ($SearchItems['busType'],'id',$row['bustype'],'title');
            $row['gear'] = $this->showTitleFromId ($SearchItems['gearType'],'id',$row['gear'], $prefix.'title');
            $row['engineType'] = $this->showTitleFromId ($SearchItems['engineType'],'id',$row['engineType'], $prefix.'title');
            $row['manufacturer'] = $this->showTitleFromId ($SearchItems['BusManufacturer'],'id',$row['manufacturer'],'title');
            
            $row['km'] = number_format($row['km']);
            $row['price'] = number_format($row['price']);
            
            if(isset($row['seo_id'])) {
                $row['seo'] = $this->get_seo($row['seo_id'], $lang);
            }
            if($lang) {
                foreach($row as $key => $val) {
                    $idx = str_replace($lang . "_", "", $key);
                    $row[$idx] = $val;
                }
            }
            return new Obj($row);
        }
        return FALSE;
        
    }
    
    
    public function getRowTablesforSelect($form) {
        
        if(!empty($form)) {
            
            $table = array();
            
            foreach ($form as $value) {
                
                if($value['type'] == 'table') {
                    
                    $this->msite->sort_objects("name", "ASC");
                    //$rows = $this->msite->get_all_objects($value['options']['table']);
                    $rows = $this->msite->get_all_objects($value['options']['table'], NULL, array('id','name'));
                    $rowsData= $this->objects_to_ArrayNoseoData($rows);
                    
                    $table[$value['name']] = $rowsData;
                    
                    if($value['name'] == 'ForeignWorkerName') {
                        
                        $item1 = array(
                            'id' => '0',
                            'name' => '---'.'ללא שיבוץ'.'---'
                        );
                    
                        $item = array(
                            'data' => $item1
                        );
                    
                        // MOVE LELO SHIVUTZ TO TOP
                        array_unshift($table[$value['name']] , $item);
                        
                        //print_r($table); die();
                        $this->db->select('ForeignWorkerName');
                        $this->db->from('first_placement');
                        $this->db->where('status', 1);
                        $result= $this->db->get();
                        $placements = $result->result_array();
                        
                        $placementsFreeWorkers = array();
                        
                        
                        
                        if(!empty($placements) && !empty($table['ForeignWorkerName'])) {
//                            
                            foreach ($table['ForeignWorkerName'] as $valueTable) {
//                                
                                $notBusy = true;
                                
                                //print_r($placements);die();
//                                
                                foreach ($placements as $placement) {
                                    if($valueTable['data']['id'] == $placement['ForeignWorkerName'] ) {
                                        $notBusy = false;
                                    }
                                };
                                
                                if($notBusy) {
                                        $placementsFreeWorkers[] = $valueTable;
                                };
                            }
                            
                        }
                        
                    }
                
                }
            }
            
            if(isset($placementsFreeWorkers) && !empty(($placementsFreeWorkers))) {
                
                $table['placementsFreeWorkers'] = $placementsFreeWorkers;
                
            }
            
            
            
            return $table;
        } else {
            return false;
        }
    }
    
    
    
    public function sortArray($array,$keyName,$order = SORT_ASC, $is_integer = false) {
        
        if(!empty($array)) {
            
            foreach ($array as $key => $row)
            {
                if($is_integer) {
                    $value[$key] = (int)$row[$keyName];  //sort by RegStart
                } else {
                    $value[$key] = $row[$keyName];
                }
                
            }

            array_multisort($value, $order, $array);
            
            return $array;
            
        } else return array();

    }
    
    
    
    
}