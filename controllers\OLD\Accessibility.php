<?php
defined('BASEPATH') OR exit('No direct script access allowed');



class Accessibility extends CI_Controller {
    
    public function index() {
        $accessibility = 
                $this->session->has_userdata('accessibility') ? 
                $this->session->userdata('accessibility') : 
                array('zoom' => 0, 'accessibility' => false, 'display' => false, 'links' => false, 'texts' => false);
        
        if($this->input->get('accessibility') === 'zoom') {
            if($this->input->get('action') > 0) {
                $accessibility['zoom'] = $accessibility['zoom'] < 3 ? $accessibility['zoom'] + 1 : $accessibility['zoom'];
            } else {
                $accessibility['zoom'] = $accessibility['zoom'] > 0 ? $accessibility['zoom'] - 1 : $accessibility['zoom'];
            }
        } else if($this->input->get('accessibility') === 'links') {
            $accessibility['links'] = ! $accessibility['links'];
        } else if($this->input->get('accessibility') === 'font') {
            $accessibility['font'] = ! $accessibility['font'];
        } else if($this->input->get('accessibility') === 'display') {
            $accessibility['display'] = ! $accessibility['display'];
        }
        
        $this->session->set_userdata('accessibility', $accessibility);
        return $accessibility;
    }
    
}
