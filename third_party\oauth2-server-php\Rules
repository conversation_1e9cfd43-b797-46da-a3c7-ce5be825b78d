#!/usr/bin/env ruby

compile '/**/*.html' do
  layout '/default.*'
end

# This is an example rule that matches Markdown (.md) files, and filters them
# using the :kramdown filter. It is commented out by default, because kramdown
# is not bundled with Nanoc or Ruby.
#
compile '/**/*.md' do
  filter :fenced_code_block
  filter :kramdown, :toc_levels => [2]
  filter :colorize_syntax,
    :colorizers => {:javascript => :pygmentsrb}
  layout '/default.*'
end

# compile '/**/*.css' do
# end
# compile '/**/*.js' do
# end
# compile '/**/*.png' do
# end

route '/**/*.{html,md}' do
  if item.identifier =~ '/index.*'
    '/index.html'
  else
    item.identifier.without_ext + '/index.html'
  end
end

compile '/**/*' do
  write item.identifier.to_s
end

layout '/**/*', :erb
