<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Msite extends CI_Model {
    
    const TABLE_PAGES = 'pages';
    const TABLE_OBJECTS = 'objects';
    const TABLE_SEO = 'seo';
    const TABLE_GALLERY = 'gallery';
    
    const TABLE_BUSES = 'buses';
    const TABLE_BUSESPICTURES = 'busesPictures';
    
    const TABLE_PROJECTS = 'projects';
    
    const TABLE_filter_places = 'filter_places';
    const TABLE_filter_money = 'filter_money';
    const TABLE_filter_time = 'filter_time';
    const TABLE_filter_target = 'filter_target';
    
    const TABLE_users = 'users';
    
    public function get_all_projects( $fullData= FALSE, $moreProjects = FALSE, $lang = NULL) {
        
        $table = self::TABLE_PROJECTS;
        
        $table1 = self::TABLE_filter_places;
        $table2 = self::TABLE_filter_money;
        $table3 = self::TABLE_filter_time;
        $table4 = self::TABLE_users;
        $table5 = self::TABLE_filter_target;
        
        if(!$fullData) {
        
            $select_table = array(
                $table . '.id',
                $table . '.city',
                $table . '.seo_id',
                $table . '.user_id as user_name',
                $table . '.is_vip as user_is_vip',
                $table . '.title',
                $table . '.flag',
                $table . '.description',
                $table . '.volunteers',
                $table . '.timeProj',
                $table . '.dateProj',
                $table . '.image',
                $table . '.video'
                );
        
            $this->db->select($select_table);
        } else {
            
            $this->db->select($table . '.*');
        }
        
        $this->db->from($table);
        
        $this->db->join($table1, $table1 . ".id = ".$table.".place", "LEFT");
        $this->db->join($table2, $table2 . ".id = ".$table.".money", "LEFT");
        $this->db->join($table3, $table3 . ".id = ".$table.".timeFilter", "LEFT");
        //$this->db->join($table4, $table4 . ".id = ".$table.".user_id", "LEFT");
        $this->db->join($table5, $table5 . ".id = ".$table.".whoFilter", "LEFT");
        
        
//        
//        $this->db->join($table2, $table2 . ".id = ".$table.".campaign_id", "LEFT");
//        
        $select_table1 = array($table1 . '.name as filter_placeName');
        $select_table2 = array($table2 . '.name as filter_money');
        $select_table3 = array($table3 . '.name as filter_time');
        //$select_table4 = array($table4 . '.name as user_name',$table4 . '.is_vip as user_is_vip');
        $select_table5 = array($table5 . '.name as filter_target');

        
        $this->db->select($select_table1);
        $this->db->select($select_table2);
        $this->db->select($select_table3);
        //$this->db->select($select_table4);
        $this->db->select($select_table5);
//        
        
        if(!$moreProjects) {
            $this->limit_objects(1,50);
        }
        
        
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                
//                if( empty($row['image']) && empty($row['big_image'] ) ) {
//                    $row['big_image'] = rand(1,2).'b.jpg';
//                };
                
                
                if(empty($row['image'])) {
                    $row['image'] = rand(1,2).'.png';
                };
                
                
                if(isset($row['seo_id'])) {
                    
                    $row['seo'] = $this->get_seo($row['seo_id'], $lang);
                    
                }
                if($lang) {
                    foreach($row as $key => $val) {
                        $idx = str_replace($lang . "_", "", $key);
                        $row[$idx] = $val;
                    }
                }
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    
    public function __construct() {
        parent::__construct();
    }
    
    public function get_settings($lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_OBJECTS);
        $this->db->where('page_id', 0);
        if($lang) {
            $this->db->group_start();
            $this->db->where('lang', $lang);
            $this->db->or_where('lang', NULL);
            $this->db->group_end();
        }
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                $data[$row['keyword']] = $row['value'];//new Obj($row);
            }
            return new Obj($data);
        }
        return NULL;
    }
    
    public function select($fields) {
        $this->db->select($fields);
    }
    
    public function set_where($where) {
        $this->db->where($where);
    }
    
    public function set_where_like($field_to_search, $q, $sides = 'both') {
        $this->db->group_start();
        if(is_array($field_to_search)) {
            if(!empty($field_to_search)) {
                foreach($field_to_search as $key => $field) {
                    if($key == 0) {
                        $this->db->like($field, $q, $sides);
                    } else {
                        $this->db->or_like($field, $q, $sides);
                    }
                }
            }
        } else {
            $this->db->like($field_to_search, $q, $sides);
        }
        $this->db->group_end();
    }
    
    public function join($table, $on, $inner) {
        $this->db->join($table, $on, $inner);
    }
    
    public function group($field) {
        $this->db->group_by($field);
    }
    
    public function get_max($table, $col) {
        $this->db->select_max($col);
        $result = $this->db->get($table); 
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return $row[$col];
        }
        return FALSE;
    }
    
    
    public function get_page_with_objects($page_id, $lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('id', $page_id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
//            $row['childs'] = $this->get_all_pages($row['id']);
//            if(isset($row['seo_id'])) {
//                $row['seo'] = $this->get_seo($row['seo_id'], $lang);
//            }
            
            $this->db->select('*');
            $this->db->from(self::TABLE_OBJECTS);
            $this->db->where('page_id', $page_id);
            if($lang) {
                $this->db->group_start();
                $this->db->where('lang', $lang);
                $this->db->or_where('lang', NULL);
                $this->db->group_end();
            }
            $result1 = $this->db->get();
            if($result1->num_rows() > 0) {
                foreach($result1->result_array() as $row1) {
                    $row[$row1['keyword']] = $row1['value'];
                }
            }
            return new Obj($row);
        }
        return NULL;
    }
    
    public function get_page($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            $row['childs'] = $this->get_all_pages($row['id']);
            if(isset($row['seo_id'])) {
                $row['seo'] = $this->get_seo($row['seo_id']);
            }
            return new Obj($row);
        }
        return NULL;
    }
    
    public function get_page_childs($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('parent_id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id']);
                }
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_page_parent($parent_id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        $this->db->where('id', $parent_id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            if(isset($row['seo_id'])) {
                $row['seo'] = $this->get_seo($row['seo_id']);
            }
            return new Obj($row);
        }
        return NULL;
    }
    
    public function get_all_pages($parent_id = 0, $lang = NULL, $controller = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_PAGES);
        if(!empty($controller)) {
            $this->db->where('controller', $controller);
        }
        $this->db->where('parent_id', $parent_id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if($lang) {
                    foreach($row as $key => $val) {
                        $idx = str_replace($lang . "_", "", $key);
                        $row[$idx] = $val;
                        
                    }
                }
                $row['childs'] = $this->get_all_pages($row['id'], $lang);
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id'], $lang);
                }
                
                //$data[] = new Obj($row);
                $page_key = $row['controller'] . '_' . $row['method'];
                $data[$page_key] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function insert_page($params) {
        $this->db->insert(self::TABLE_PAGES, $params); 
        return $this->db->insert_id();
    }
    
    public function update_page($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_PAGES, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_page($id) {
        $obj = $this->get_page($id);
        $this->delete_seo($obj->Arg('seo_id'));
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_PAGES);
        return $this->db->affected_rows();
    }
    
    
    
    public function get_page_object($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_OBJECTS);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return new Obj($row);
        }
        return NULL;
    }
    
    public function get_all_page_objects($page_id = FALSE, $type = FALSE) {
        $this->db->select('*');
        $this->db->from(self::TABLE_OBJECTS);
        $this->db->where('page_id', $page_id);
        if($type) {
            $this->db->where('type', $type);
        }
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                //$data[$row['keyword']] = new Obj($row);
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function insert_page_object($params) {
        $this->db->insert(self::TABLE_OBJECTS, $params); 
        return $this->db->insert_id();
    }
    
    public function update_page_object($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_OBJECTS, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_page_object($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_OBJECTS);
        return $this->db->affected_rows();
    }
    
    
    
    
    
    
    
    public function searching($table, $field_to_search, $q, $limit = 100) {
        $this->db->select('*');
        $this->db->from($table);
        $this->db->like($field_to_search, $q, 'both');
        $this->db->limit($limit);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            return $result->result_array();
        }
        return NULL;
    }
    
    public function limit_objects($page = 1, $limit = 100) {
        $this->db->limit($limit, $limit * ($page - 1));
    }
    
    public function sort_objects($order = "id", $sort = "DESC") {
        $this->db->order_by($order, $sort);
    }
    
    public function count_all_objects($table) {
        $this->db->select('*');
        $this->db->from($table);
        return $this->db->count_all_results();
    }
    
    public function distinct_all_objects($table, $field, $other_fields = NULL, $lang = NULL) {
        if($other_fields) {
            $this->db->select('DISTINCT('.$table . '.'.$field.'), ' . $other_fields);
        } else {
            $this->db->select('DISTINCT('.$table . '.'.$field.')');
        }
        $this->db->from($table);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id'], $lang);
                }
                if($lang) {
                    foreach($row as $key => $val) {
                        $idx = str_replace($lang . "_", "", $key);
                        $row[$idx] = $val;
                    }
                }
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_all_objects($table, $lang = NULL, $filtersArray = FALSE) {
        
        if($filtersArray) {
            
            foreach ($filtersArray as $value) {
                $this->db->select($table . '.'.$value);
            }
            
        } else {
            $this->db->select($table . '.*');
        }
        
        
        $this->db->from($table);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id'], $lang);
                }
                if($lang) {
                    foreach($row as $key => $val) {
                        $idx = str_replace($lang . "_", "", $key);
                        $row[$idx] = $val;
                    }
                }
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_object($table, $id, $lang = NULL) {
        $this->db->select('*');
        $this->db->from($table);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            if(isset($row['seo_id'])) {
                $row['seo'] = $this->get_seo($row['seo_id'], $lang);
            }
            if($lang) {
                foreach($row as $key => $val) {
                    $idx = str_replace($lang . "_", "", $key);
                    $row[$idx] = $val;
                }
            }
            return new Obj($row);
        }
        return FALSE;
    }
    
    public function insert_object($table, $params) {
        $fields = $this->db->field_data($table);
        if($fields) {
            $data = array();
            foreach ($fields as $field) {
                if(isset($params[$field->name])) {
                    $data[$field->name] = $params[$field->name];
                }
            }
            $this->db->insert($table, $data); 
            return $this->db->insert_id();
        }
        return FALSE;
    }
    
    public function update_object($table, $id, $params) {
        $fields = $this->db->field_data($table);
        if($fields) {
            $data = array();
            foreach ($fields as $field) {
                if(isset($params[$field->name])) {
                    $data[$field->name] = $params[$field->name];
                }
            }
            $this->db->where('id', $id);
            $this->db->update($table, $data); 
            return $this->db->affected_rows();
        }
        return FALSE;
    }
    
    public function delete_object($table, $id) {
        $obj = $this->get_object($table, $id);
        $this->delete_seo($obj->Arg('seo_id'));
        $this->db->where('id', $id);
        $this->db->delete($table);
        return $this->db->affected_rows();
    }
    
    
    
    
    
    public function get_all_gallery($keyword) {
        $this->db->select('*');
        $this->db->from(self::TABLE_GALLERY);
        $this->db->where('keyword', $keyword);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                $data[] = new Obj($row);
            }
            return $data;
        }
        return NULL;
    }
    
    public function get_gallery($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_GALLERY);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return new Obj($row);
        }
        return FALSE;
    }
    
    public function insert_gallery($params) {
        $this->db->insert(self::TABLE_GALLERY, $params); 
        return $this->db->insert_id();
    }
    
    public function update_gallery($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_GALLERY, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_gallery($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_GALLERY);
        return $this->db->affected_rows();
    }
    
    
    
    
    
    
    
    public function get_seo_id($controller, $method, $param) {
        $this->db->select("*");
        $this->db->from(self::TABLE_SEO);
        $this->db->where('controller', $controller);
        $this->db->where('method', $method);
        $this->db->group_start();
        if(!empty($param)) {
            $this->db->or_where('param', $param);
        }
        else {
            $this->db->or_where('param', NULL);
            $this->db->or_where('param', '');
            $this->db->or_where('param', 0);
        }
        $this->db->group_end();
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return $row['id'];
        }
        return FALSE;
    }
    
    public function get_seo($id, $lang = NULL) {
        $this->db->select('*');
        $this->db->from(self::TABLE_SEO);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            //print_r($id);die();
            if($lang) {
                foreach($row as $key => $val) {
                    $idx = str_replace($lang . "_", "", $key);
                    $row[$idx] = $val;
                }
            }
            
            return new Obj($row);
        }
        return FALSE;
    }
    
    
    
    
    
    public function insert_seo($params) {
        $this->db->insert(self::TABLE_SEO, $params); 
        return $this->db->insert_id();
    }
    
    public function update_seo($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_SEO, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete_seo($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE_SEO);
        return $this->db->affected_rows();
    }
    
    public function sitemap_search($q, $page = 1, $limit = 50) {
        $pager = $limit * ($page - 1);

        $title = 'title';
        $description = 'description';
        $friendly = 'friendly';
        $query = "
            (SELECT *, MATCH($friendly, $title, $description) AGAINST('*$q*' IN BOOLEAN MODE) AS score 
            FROM seo 
            WHERE MATCH($friendly, $title, $description) AGAINST('*$q*' IN BOOLEAN MODE)) 
            ORDER BY score DESC LIMIT ".$limit." OFFSET ".$pager."
        ";
        $result = $this->db->query($query);
        if ($result->num_rows() > 0) {
            return $result->result_array();
        }
        return false;
    }
    
    public function sitemap_search_total($q) {

        $title = 'title';
        $description = 'description';
        $friendly = 'friendly';
        $query = "
            (SELECT COUNT(id) AS total 
            FROM seo 
            WHERE MATCH($friendly, $title, $description) AGAINST('*$q*' IN BOOLEAN MODE)) 
        ";
        $result = $this->db->query($query);
        if ($result->num_rows() > 0) {
            $row = $result->row_array();
            return isset($row['total']) ? $row['total'] : 0;
        }
        return 0;
    }
    
    public function update_site_map() {
        $freq = "always";
        $priority = "1";
        $_SITEMAP = dirname(APPPATH) . '/sitemap.xml';
        $pf = fopen ($_SITEMAP, "w");
        if ($pf) {
            fwrite ($pf, "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" .
                 "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"\n" .
                 "        xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" .
                 "        xsi:schemaLocation=\"http://www.sitemaps.org/schemas/sitemap/0.9\n" .
                 "        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd\">\n" .
                 "  <url>\n" .
                 "    <loc>".base_url()."/</loc>\n" .
                 "    <changefreq>daily</changefreq>\n" .
                 "  </url>\n");
            
        }
        $this->db->select('*');
        $this->db->from(self::TABLE_SEO);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            foreach($result->result_array() as $row) {
                if($row['robots'] !== 'index, follow') {
                    continue;
                }

                $url = $row['canonical'];
                fwrite ($pf, "  <url>\n" .
                    "    <loc>" . $url ."</loc>\n" .
                    "    <changefreq>$freq</changefreq>\n" .
                    "    <priority>$priority</priority>\n" .
                    "  </url>\n");
            }
        }
        
        fwrite ($pf, "</urlset>\n");
        fclose ($pf);
        return TRUE;
    }
    
    public function search_id_in_object($array_objects,$id, $return) {
        foreach ($array_objects as $value) {
            if($value->Arg('id')==$id) {
                foreach ($return as $value2) {
                  $result[$value2] =  $value->Arg($value2);
                }
                return $result;
            }
        }
    }
    
    
    
    public function getSearchItems() {
        //$this->msite->limit_objects(1, 6);
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('busType');
        
        foreach ($bustypeDB as $key => $value) {
            $bustype[] = array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title'),
                    'title_en' => $value->Arg('title_en')
                );
        }
        
        $SearchItems['busType'] = $bustype;
        // ---------------------------------------------------------------
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('BusManufacturer');
        
        foreach ($bustypeDB as $key => $value) {
            $BusManufacturer[] =  array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title')
                );
        }
        
        $SearchItems['BusManufacturer'] = $BusManufacturer;
        // ---------------------------------------------------------------
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('gearType');
        
        foreach ($bustypeDB as $key => $value) {
            $BusgearType[] =  array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title'),
                    'en_title' => $value->Arg('en_title')
                );
        }
        
        $SearchItems['gearType'] = $BusgearType;
        // ---------------------------------------------------------------
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $bustypeDB = $this->get_all_objects('engineType');
        
        foreach ($bustypeDB as $key => $value) {
            $BusengineType[] =  array (
                    'id' => $value->Arg('id'),
                    'title' => $value->Arg('title'),
                    'en_title' => $value->Arg('en_title')
                );
        }
        
        $SearchItems['engineType'] = $BusengineType;
        // ---------------------------------------------------------------
        
        
        // ---------------------------------------------------------------
        $this->set_where("status='1'");
        $this->sort_objects("sort", "DESC");
        $busesDB = $this->distinct_all_objects('buses','title');
        
        
        foreach ($busesDB as $key => $value) {
            $buses[] = array (
                    'title' => $value->Arg('title')
                );
        }
        
        $SearchItems['buses'] = $buses;
        // ---------------------------------------------------------------
        
        
        return $SearchItems;
        
        
        //$this->data['gallery'] = $this->msite->get_all_objects('gallery');
    }
    
    
    public function showTitleFromId ($array,$searchField,$id,$ResultField) {
        
        $return = FALSE;
        
        foreach ($array as $key => $value) {
            if($value[$searchField]==$id) {
                $return = $value[$ResultField];
            }
        }
        
        return $return;
        
        
        
    }
    

    public function get_all_buses($excludeId=NULL,$SearchItems=NULL, $lang=NULL) {
        
        
        $table = self::TABLE_BUSES;
        
        $this->db->from($table);
        $this->db->select('*');
        
        if($excludeId) {
            $this->db->where("id !='$excludeId'");
        }
        
        
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row) {
                if(isset($row['seo_id'])) {
                    $row['seo'] = $this->get_seo($row['seo_id']);
                }
                
                
                if(!empty($SearchItems)) {
                    $row['gearTypeName'] = '';
                    foreach ($SearchItems['gearType'] as $value) {
                        if($value['id']== $row['gear']) {
                            
                            if($lang == 'he') {
                                $row['gearTypeName'] = $value['title'];
                            }
                            else {
                                $row['gearTypeName'] = $value[$lang.'_title'];
                            }
                            
                            
                        }
                    };
                }
                
                
                $row['gallery'] = $this->get_galleryBus($row['id']);
                
                if(!empty($row['gallery'])) {
                    
                    $flag = FALSE;
                    
                    foreach ($row['gallery'] as $key => $picture) {
                        
                        //print_r($picture);
                        
                        if(empty($picture['video']) AND !$flag) {
                            $row['mainpic'] = base_url().picIMG.$picture['image'];
                            $flag = TRUE;
                        } 
                    }
                    //die();
                }
                
                else {
                    $row['mainpic'] = base_url().picIMG.'noOtobusPic.png';
                }
                
                
                $data[] = new Obj($row);

            }
            
            
            
            return $data;
        }
        return NULL;
    }
    
    
    public function get_galleryBus($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_BUSESPICTURES);
        $this->db->where('busId', $id);
        $this->db->where('status=1');
        $this->db->order_by('sort', 'DESC');
        
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->result_array();
            
//            if($id==4) {
//                print_r($row);die('sebas');
//            }
            
            return $row;
            
            //return new Obj($row);
        }
        return FALSE;
    }
    
    
    public function run_form() {
        
        $this->errorMessage = false;
        
        $this->load->library('form_validation');

        
            $this->config->set_item('language', 'hebrew');
            //$this->form_validation->set_rules('name', 'שם קובץ', 'trim|required');
//            $this->form_validation->set_rules('km', 'ק״מ', 'trim|numeric|required');
//            $this->form_validation->set_rules('BusManufacturer', 'יצרן', 'trim|required');
//            $this->form_validation->set_rules('engine', 'נפח מנוע', 'trim|required|numeric');
//            $this->form_validation->set_rules('title', 'דגם', 'trim|required');
//            $this->form_validation->set_rules('engineType', 'סוג מנוע', 'trim|required');
//            $this->form_validation->set_rules('year', 'מודל', 'trim|required');
//            $this->form_validation->set_rules('gearType', 'תיבת הילוכים', 'trim|required');
//            $this->form_validation->set_rules('yad', 'יד', 'trim|required');
//            $this->form_validation->set_rules('price', 'מחיר', 'trim|required|numeric');
//            $this->form_validation->set_rules('contactName', 'שם', 'trim|required');
//            $this->form_validation->set_rules('contactMail', 'דוא״ל', 'trim|required|valid_email');
//            $this->form_validation->set_rules('contactPhone', 'טלפון', 'trim|required|min_length[9]');
//            $this->form_validation->set_rules('video', 'סרטון', 'trim|valid_url');
            
       
        
        //https://www.codeigniter.com/userguide3/libraries/form_validation.html
        //$this->form_validation->set_rules('upload', 'תמונה1', 'trim|required');

        
        if($this->form_validation->run() == FALSE) {
            if($this->input->post('action') == 'publish') {
                $errors = current($this->form_validation->error_array());
                $this->errorMessage = trim(preg_replace('/\s\s+/', ' ', strip_tags ($errors)));
            }
            
            $this->session->set_flashdata('FileInsertId', FALSE);
            
            //echo "NO POST - NOT SENT";
        } else {
            
            //die('OK');
            
            
            //$uniq = uniqid();
            $insertId = FALSE;
            
            $post = array(
                'created_at' => date("Y-m-d H:i:s"),
                'status' => 0,
                'name' => 'My name is Sebas',
//                'manufacturer' => $this->input->post('BusManufacturer', true),
//                'engine' => $this->input->post('engine', true),
//                'title' => $this->input->post('title', true),
//                'engineType' => $this->input->post('engineType', true),
//                'year' => $this->input->post('year', true),
//                'gear' => $this->input->post('gearType', true),
//                'yad' => $this->input->post('yad', true),
//                'price' => $this->input->post('price', true),
//                'tv' => $this->input->post('tv', true),
//                'handicapped' =>$this->input->post('handicapped', true),
//                'abs' => $this->input->post('abs', true),
//                'text' => $this->input->post('text', true),
//                'tradeIn' => $this->input->post('tradeIn', true),
//                'contactName' => $this->input->post('contactName', true),
//                'contactMail' => $this->input->post('contactMail', true),
//                'contactPhone' => $this->input->post('contactPhone', true),
//                'contactText' => $this->input->post('contactText', true)
            );
            
            $insertId = FALSE;
            
            if(empty($this->session->flashdata('FileInsertId'))) {
                
                
                $insertId = $this->insertBus($post);
                

                
                $this->session->set_flashdata('FileInsertId', $insertId);
            }
            else {
                $insertId = $this->session->flashdata('BusInsertId');
                $this->updateBus($insertId, $post);
                $this->session->set_flashdata('BusInsertId', $insertId);
            }
            
            if($insertId) {
                $pictures = $this->uploadBusPicutes($insertId);
          
                if(!empty($pictures['error'])) {
                    $this->errorMessage = $pictures['error'];
                }
                
                $return['id'] = $insertId;
                $return['files'] = $pictures['files'];
                $return['done'] = 'YES';
                $return['errors'] = $pictures['error'];
                
                return $return;
                
        } // Insert ID
            
            return false;  // NO Insert ID
        }
        return false;
    }
    
    
    public function uploadProjectPicutes($insertId) {
    
    //$filename = '';
    $fileOnlyname = $insertId.'_'.uniqid();
    
    $config['upload_path'] = picIMG;
    $config['allowed_types'] = 'jpg|png|jpeg';
    $config['max_size'] = 4048; // Usually 2 MB
    $config['file_name'] = $fileOnlyname;
    
    // $config['max_width'] = 1024; // (in pixels) 
    // $config['max_height'] = 768;
    // $config['min_width'] = 
    // $config['min_height']
    // https://www.codeigniter.com/userguide3/libraries/file_uploading.html
    
    
    

    $this->load->library('upload');
    $this->upload->initialize($config); 

    $this->load->library('MY_Upload');
    
    
    
    if(isset($_FILES['userfile']['name']) and !empty($_FILES['userfile']['name']) ) {
        
        //print_r($_FILES['userfile']);die('se');
        $path = $_FILES['userfile']['name'];
        $ext = '.'.pathinfo($path, PATHINFO_EXTENSION);
        
        $filename = $fileOnlyname.$ext;
                        
        //$files = $this->upload->do_multi_upload('userfile', TRUE);
        $files = $this->upload->do_upload('userfile', TRUE);
        //print_r($files);die('seb1');
    } else {
        $files = FALSE;
        $noFiles = TRUE;
    }

    $error = FALSE;
    
    if(!$files) {
        if(isset($noFiles)) {
            $error = 'נא לבחור תמונות';
        } else {
            $error = strip_tags($this->upload->display_errors());
        }
        
    } else {

            
            $post = array(
                'created_at' => date("Y-m-d H:i:s"),
                'image' => $filename,
                'status' => 1,
                'projId' => $insertId
            );

            $LastpictureinsertId = $this->insertProjPictures($post);

            //echo '<br/>filename: '.$filename;
            
            $imageLibrary = $this->ImageLibrary($filename);

            if(isset($imageLibrary['error'])) {
                $error = $imageLibrary['error'];
            }

            elseif(isset($imageLibrary['rotate'])) {
                echo 'image: '.$imageLibrary['rotate'];
            }
            
        }
        
        $returns = array (
            
            'files' => $files,
            'error' => $error
        );
        
        
        return $returns;
    }
    
    
     public function ImageLibrary($file_name) {
         
         
         
        $_width = '330_w';
        //$_height = $this->input->post($field['name'] . '_h');
        $quality = 80;

        ini_set('memory_limit', '-1');
        
        $this->load->library('simple_img');
        $smpimg = $this->simple_img->initialize(picIMG.$file_name);
        

//        if($this->input->post($field['name'] . '_crop')) {
//            $smpimg->crop($x_axis, $y_axis, $_width + $x_axis, $_height + $y_axis)
//                ->save(apppicIMG . $data[$field['name']], $quality);
//        }

        $smpimg ->auto_orient()
                //->crop($x_axis, $y_axis, $_width + $x_axis, $_height + $y_axis)
                ->best_fit(600, 600)
                ->save(picIMG.$file_name, $quality);
    }
    
    

    
    public function insertProjPictures($params) {
        
//        $params = array(
//                   'created_at' => date("Y-m-d H:i:s"),
//                   'image' => $value['file_name'],
//                   'status' => 1,
//                   'projId' => $insertId
//               );
        
        if(isset($params['projId']) && isset($params['image'])) {
            $return = $this->update_object('projects', $params['projId'], array('image' => $params['image']));
        } else {
            $return = false;
        }
        
        
        return $return;
    }
    
    
    
    
    
    
}





