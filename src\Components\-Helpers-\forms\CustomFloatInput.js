import React, { useState, useRef, useCallback, useEffect } from "react";
import TextField from "@mui/material/TextField";
import MenuItem from "@mui/material/MenuItem";
import InputAdornment from "@mui/material/InputAdornment";
import IconButton from "@mui/material/IconButton";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import SearchAutocompleteAndDbFocus from "./../../-Helpers-/api/SearchAutocompleteAndDbFocus";
import { FormValidator } from "./validation/FormValidator";
import { ValidationMessage } from "./validation/ValidationMessage";
import { ValidateForm } from "./validation/wholeFormValidation";
import { toast } from "react-toastify";

// Input styles with RTL support
const rtlInputStyles = {
  direction: 'rtl',
  textAlign: 'right',
};

/**
 * CustomFloatInput component that provides various input types with floating labels and validation
 */
const CustomFloatInput = React.memo((props) => {
  const {
    value,
    selectOptions,
    noEnglish,
    validationRules,
    name,
    checkInputs,
    checked,
    dbParams,
    typeInput,
    cssClass,
    zIndexClass = "",
    disabled,
    placeholder,
    updateValue
  } = props;
  const environment = process.env.REACT_APP_ENVIRONMENT;

  const [isError, setIsError] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const myInputRef = useRef();
  useEffect(() => {
    if (environment === "dev") {
      console.log("environment", environment);
    }
  }, [environment]);

  const rules = {
    inputValue: validationRules
  };

  const handleChange = useCallback((event) => {
    let newValue = event.target.value;

    if (noEnglish) {
      if (event.target.value !== newValue.replace(/[A-Za-z]/gi, "")) {
        toast.error("יש להקליד אותיות בעברית", {
          toastId: "customId",
        });
      }
      newValue = newValue.replace(/[A-Za-z]/gi, "");
    }

    // Special handling for phone numbers when validationRules.phone is true
    if (validationRules && validationRules.phone && name.toLowerCase().includes('mobile')) {
      // Allow digits, dashes, plus signs, and parentheses for phone numbers
      if (typeInput === "text") {
        // Format the phone number with dashes for readability
        const digitsOnly = newValue.replace(/\D/g, '');
        
        // Format as XXX-XXXXXXX if it starts with 05
        if (digitsOnly.startsWith('05') && digitsOnly.length >= 3) {
          if (digitsOnly.length <= 6) {
            // Format as 05X-XXX
            newValue = `${digitsOnly.slice(0, 3)}-${digitsOnly.slice(3)}`;
          } else {
            // Format as 05X-XXX-XXXX
            newValue = `${digitsOnly.slice(0, 3)}-${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6, 10)}`;
          }
        } 
        // Handle international format
        else if (digitsOnly.startsWith('972') && digitsOnly.length > 3) {
          newValue = `+${digitsOnly.slice(0, 3)}-${digitsOnly.slice(3)}`;
        }
        // If it doesn't match known patterns, keep original input but limit length
        else if (digitsOnly.length > 10) {
          newValue = digitsOnly.slice(0, 10);
        }
      } else if (typeInput === "number") {
        // Only keep digits for the actual value if typeInput is number
        newValue = newValue.replace(/\D/g, '');
      }
    }

    updateValue({ [name]: newValue });
  }, [name, noEnglish, updateValue, validationRules, typeInput]);

  const isOK = useCallback((validationResult) => {
    const isValid = validationResult.inputValue === undefined || 
                   (validationRules.required ? value && value.length > 0 : true);
    setIsError(!isValid);
    return isValid;
  }, [validationRules, value]);

  useEffect(() => {
    if (checkInputs) {
      isOK({ inputValue: value });
      checked();
    }
  }, [checkInputs, isOK, checked, value]);

  const handleChangeSelect = useCallback((event) => {
    const newValue = event.target.value;
    updateValue({ [name]: newValue });
  }, [name, updateValue]);


  const handleSearchSelected = useCallback((item) => {
    const field = dbParams.valueField;
    const newValue = item[field];
    setIsError(false);
    updateValue({ [name]: newValue });
  }, [dbParams, name, updateValue]);

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(!showPassword);
  }, [showPassword]);

  return (
    <div className={`input inputType${typeInput} ${zIndexClass} rtl-input`} dir="rtl" style={rtlInputStyles}>
      {(typeInput === "text" || typeInput === "password" || typeInput === "number") && (
        <FormValidator
          data={{ inputValue: value }}
          rules={rules}
          isOK={isOK}
          validateForm={ValidateForm}
        >
          <div className="form-floating">
            <TextField
              id={name}
              name="inputValue"
              label={placeholder}
              variant="outlined"
              fullWidth
              value={value || ""}
              onChange={handleChange}
              disabled={disabled}
              error={isError}
              type={typeInput === "password" ? (showPassword ? "text" : "password") : typeInput}
              className={`customFloatInput rtl-mui-input ${cssClass}`}
              inputProps={{
                dir: "rtl",
                style: rtlInputStyles
              }}
              InputProps={{
                endAdornment: typeInput === "password" ? (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={togglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ) : null,
                dir: "rtl",
                style: rtlInputStyles
              }}
              helperText={isError ? <ValidationMessage field="inputValue" /> : null}
            />
          </div>
        </FormValidator>
      )}

      {typeInput === "searchDB" && (
        <FormValidator
          data={{ inputValue: value }}
          rules={rules}
          isOK={isOK}
          validateForm={ValidateForm}
        >
          <div className={isError ? "customInputSearch errorClass" : "customInputSearch"}>
            <SearchAutocompleteAndDbFocus
              keys={["name"]}
              dbParams={{
                functionName: dbParams.function,
                controller: dbParams.controller,
              }}
              placeholder={placeholder}
              submit={handleSearchSelected}
              DBItems={environment === "dev" ? "app/getCities4SelectDev" : "app/getCities4Select"}
              disabled={disabled}
            />
          </div>
          {isError && <div className="mui-error-text"><ValidationMessage field="inputValue" /></div>}
        </FormValidator>
      )}

      {typeInput === "select" && (
        <FormValidator
          data={{ inputValue: value }}
          rules={rules}
          isOK={isOK}
          validateForm={ValidateForm}
        >
          <TextField
            select
            id={name}
            name="inputValue"
            label={placeholder}
            variant="outlined"
            fullWidth
            value={value || ""}
            onChange={handleChangeSelect}
            disabled={disabled}
            error={isError}
            className={`customSelectInput rtl-mui-input ${cssClass}`}
            inputProps={{
              dir: "rtl",
              style: rtlInputStyles
            }}
            InputProps={{
              dir: "rtl", 
              style: rtlInputStyles
            }}
            helperText={isError ? <ValidationMessage field="inputValue" /> : null}
          >
            {(selectOptions || []).map((item) => (
              <MenuItem value={item.id} key={item.id} style={rtlInputStyles}>
                {item.value}
              </MenuItem>
            ))}
          </TextField>
        </FormValidator>
      )}

      {typeInput === "date" && (
        <FormValidator
          data={{ inputValue: value }}
          rules={rules}
          isOK={isOK}
          validateForm={ValidateForm}
        >
          <TextField
            id={name}
            name="inputValue"
            label={placeholder}
            variant="outlined"
            fullWidth
            type="date"
            value={value || ""}
            onChange={handleChange}
            disabled={disabled}
            error={isError}
            className={`customFloatInput rtl-mui-input ${cssClass}`}
            inputProps={{
              dir: "rtl",
              style: rtlInputStyles
            }}
            InputProps={{
              dir: "rtl",
              style: rtlInputStyles
            }}
            inputRef={myInputRef}
            InputLabelProps={{
              shrink: true,
            }}
            helperText={isError ? <ValidationMessage field="inputValue" /> : null}
          />
        </FormValidator>
      )}
    </div>
  );
});

CustomFloatInput.displayName = 'CustomFloatInput';

export default CustomFloatInput;
