<div class="" style="padding: 20px;">
    <div class="row">
        <div class="col-md-12">
            <a href="<?php echo base_url('admin/object/section_branch/show?return='.  current_url() . '&parent_selected=branch_id&parent_selected_value=' . $branch_id); ?>" class="btn btn-primary btn-block">
                הוספת מקטע חדש
            </a>
            <hr/>
        </div>
        
        <?php if(isset($sections) && !empty($sections)) foreach ($sections as $row){ ?>
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="row">
                        <div class="form-group col-md-8">
                            <?php echo $row->Arg('he_name'); ?>
                        </div>
                        <div class="form-group col-md-2">
                            <div class="input-group">
                                <div class="input-group-btn">
                                    <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/object/section_branch/status/' . $row->Id()); ?>">
                                        <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                                    </button> 
                                </div>
                                <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $row->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/object/section_branch/sort/' . $row->Id() . '/'); ?>">
                            </div>
                        </div>
                        <div class="form-group col-md-2">
                            <div class="btn-group" role="group">
                                <a class="btn btn-default pulse-hover" href="<?php echo base_url('admin/object/section_branch/show/' . $row->Id() . '?return='.  current_url()); ?>" role="button">עריכה</a>

                                <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/object/section_branch/destroy/' . $row->Id() . '/' . getQS()); ?>">
                                    מחק
                                </button> 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel-body">
                    <?php echo $row->Text('he_text'); ?>
                </div>
            </div>
        </div>
        <?php } ?>
    </div>
</div>
