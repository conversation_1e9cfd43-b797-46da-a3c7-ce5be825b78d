<?php defined('BASEPATH') OR exit('No direct script access allowed');

class First extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+asd';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+asdseb-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
        
        
//        if( !$this->input->get('code') or $this->input->get('code') != md5($this->data['code']) ) {
//            die('error');
//        }
        
    }
    
    public function SiteDataItems ($param = FALSE) {
        $this->_loaderWS($param);
        
        $output['pages'] =  $this->msiteWs->get_pagesWs($this->data['pages']);
        $output['settings'] = $this->data['settings'];
        
        $output['contactData'] = $this->msiteWs->get_page_with_objectsArray(16,$this->data['current_language']);
        
        //GET PAGES WITH ID!!!
        $output['paramPages'] = $this->msiteWs->getParamPages();
        
        $csrf = array(
            'name' => $this->security->get_csrf_token_name(),
            'hash' => $this->security->get_csrf_hash()
        );
        
        $output['csrf'] = $csrf;
            
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $events = $this->msite->get_all_objects('events');
//        $output['events'] = $this->msiteWs->objects_to_ArrayNoseo($events);
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    public function index($param = FALSE) {
        $this->_loaderWS($param);
        
        
        
        //https://github.com/emreakay/CodeIgniter-Aauth
        
        //Groups  > Crear primero los grupos
        //$this->aauth->create_group('AdminUsers');
        
        //AddUser
        $username= 'sebasUser';
        $password='sebas';
        
        
        //$AddUser = $this->aauth->create_user('<EMAIL>', $password,$username);
        //$this->aauth->add_member($AddUser, 'AdminUsers');
        
        //Check PASS: TRUE OR FALSE?
        //$this->aauth->login(set_value('username'), set_value('password'))
        
        $output['checkUserYes'] = $this->aauth->login($username, $password);
        $output['error1'] = $this->aauth->get_errors_array();
        $output['user'] = (array)$this->aauth->get_user();
        
        $output['is_group_admin'] = $this->aauth->is_member( 'AdminUsers', $this->aauth->get_user('id') );
        
        
        
        $output['checkUserNo'] = $this->aauth->login('developers', 'titi');
        $output['error2'] = $this->aauth->get_errors_array();
        
        $username= 'developers';
        $password='waveprojects!';
        $output['checkUserYes2'] = $this->aauth->login($username, $password);
        $output['is_group_admin2'] = $this->aauth->is_member( 'AdminUsers', $this->aauth->get_user('id') );
        $output['is_group_admin3'] = $this->aauth->is_member( 'Admin', $this->aauth->get_user('id') );
        
        //$output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    public function checkUser ($param = FALSE) {
          
        $this->_loaderWS($param);
        
        
        //helper_name::encode(data, secure_key);
        
//        echo $token = JWT::encode('sebas', 'ani');
//        echo "<br><br/>";
//        echo JWT::decode($token,'ani',true);
        
        
                
        //https://stackoverflow.com/questions/40903355/how-to-verify-jwt-token-in-codeigniter-3-x-for-every-request-from-client
        
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('username','password'));
        
        if( !empty($jsonPosts['username']) && !empty($jsonPosts['password']) ) {
            
                if($this->aauth->login($jsonPosts['username'], $jsonPosts['password'])) {
                    
                    $user = $this->aauth->get_user();
                    $is_group_admin = $this->aauth->is_member( 'AdminUsers', $user->id );
                    $is_group_adminOnly = $this->aauth->is_member( 'Admin', $user->id );
                            
                    $output['data'] = array(
                        'id' => $user->id,
                        'username' => $user->username,
                        'banned' => $user->banned,
                        'is_group_admin' => $is_group_admin,
                        'is_group_adminOnly' => $is_group_adminOnly,
                        'is_logged_in' => $this->aauth->is_loggedin(),
                        'token' => JWT::encode($user->id, $this->data['code'])
                    );
                    
                } else {
                    $output['error'] =  $this->aauth->get_errors_array();
                }
                
                
                
        } else {
            //set_status_header(500);
            $output['error'] = 'error NO-DATA';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    
    public function UserLogout ($param = FALSE) {
        
        $this->_loaderWS($param);
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId'));
        
        
        if( !empty($jsonPosts['userId']) ) {
            
                //$output =  $userid;
                $this->aauth->logout();
                $output = $this->aauth->is_loggedin();
                
        } else {
            //set_status_header(500);
            $output = 'error NO-DATA';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function userCredentials ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','userName','token'));
        
        //print_r($jsonPosts);die();
        
        if(!empty($jsonPosts)) {
            
            $userID = JWT::decode($jsonPosts['token'],$this->data['code'],true);
            
        $output = $jsonPosts['userId'].' - '.$jsonPosts['userName']. ' - '. $userID;
                
        } else {
           
            $output = 'error NO-DATA';
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
}
    