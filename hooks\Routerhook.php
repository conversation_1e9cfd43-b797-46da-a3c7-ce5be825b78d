<?php

class Router_Hook {
    /**
     * Loads routes from database.
     *
     * @access public
     * @params array : hostname, username, password, database, db_prefix
     * @return void
     */
    
    
    function dynamic_route($params) {
        global $routing;
        global $available_lang;
        switch($params['db']['dbdriver']) {
                case 'mysqli':
                    $connection = mysqli_connect($params['db']['hostname'], $params['db']['username'], $params['db']['password']);
                    if(!mysqli_connect_errno()) {
			mysqli_select_db($connection, $params['db']['database']);
                        mysqli_query($connection, 'SET NAMES "utf8" COLLATE "utf8_general_ci"');

                        $request_uri = $_SERVER['REQUEST_URI'];
                        if (strpos($request_uri, 'admin/') === false) {
                            $r_uri = strpos($request_uri, '?') ? substr($request_uri, 0, strpos($request_uri, '?')) : $request_uri;
                            $r_uri = trim($r_uri, '/');
                            $segments = explode("/", $r_uri); 
                            $r_segment = end($segments);
                            $f_url = trim(urldecode($r_segment));
                            $q = mysqli_real_escape_string($connection, $f_url);
                            $lang = isset($_COOKIE['current_language']) && isset($available_lang[$_COOKIE['current_language']]) ? $_COOKIE['current_language'] . '_' : '';
                            $query = "SELECT * FROM `seo` WHERE `friendly` = '$q' OR `".$lang."friendly` = '$q'";
                            $result = mysqli_query($connection, $query);

                            if($result && $row = mysqli_fetch_assoc($result)) {
                                $routing = $row;
                                $routing['friendly'] = $row[$lang . 'friendly'];
                            }
                        }
                        
                    }
                    break;
                default:
            }
        
        
    }
    
    
    function redirect301($params) {
        
        switch($params['db']['dbdriver']) {
                case 'mysqli':
                    $connection = mysqli_connect($params['db']['hostname'], $params['db']['username'], $params['db']['password']);
                    if(!mysqli_connect_errno()) {
			mysqli_select_db($connection, $params['db']['database']);
			mysqli_query($connection, 'SET NAMES "utf8" COLLATE "utf8_general_ci"');
                        
                        if(isset($_SERVER['HTTPS'])){
                            $protocol = ($_SERVER['HTTPS'] && $_SERVER['HTTPS'] != "off") ? "https" : "http";
                        } else{
                            $protocol = 'http';
                        }
                        $actual_link = $protocol . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
                        $cleanurl = preg_replace('#^www\.(.+\.)#i', '$1', "$_SERVER[HTTP_HOST]") . "$_SERVER[REQUEST_URI]";
                        $cleanurl = trim(urldecode($cleanurl));
                        $actual_link = trim(urldecode($actual_link));
                        if($actual_link == 'http://www.perot.co.il/' || 
                                $actual_link == 'https://www.perot.co.il/' || 
                                $actual_link == 'http://perot.co.il/' || 
                                $actual_link == 'https://perot.co.il/') {
                            return;
                        }
                        $query = "SELECT * FROM redirect301 WHERE `from` = '$actual_link' OR `from` LIKE '%$cleanurl'";
                        //echo $query;
                        $result = mysqli_query($connection, $query);
                        if(mysqli_num_rows($result) > 0 && $row = mysqli_fetch_assoc($result)) {
                            // Permanent redirection
                            $to_url = strpos($row['to'], 'https') !== false ? $row['to'] : str_replace("http", "https", $row['to']);
                            header("HTTP/1.1 301 Moved Permanently");
                            header("Location: $to_url", true, 301);
                            exit();
                        }
                        
                    }
                    break;
                default:
            }
        
        
    }
}