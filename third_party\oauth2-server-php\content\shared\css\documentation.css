/*------------------------------------------------------------------------------
     Global Documentation Styles
------------------------------------------------------------------------------*/

html {
  height:100%;
}

body {
  font: 13px helvetica,arial,freesans,clean,sans-serif;
  line-height: 1.4em;
  background-color: #fff;
  color: #393939;
  margin: 0px;
  padding: 0px;
  height: 100%;
}

p {
  margin: 1em 0;
}

h1 {
  font-size: 20px;
  border-bottom: 1px solid #cccccc;
  padding: .5em 0;
  margin: 2em 0 1em;
}

h1:first-child {
  margin: 0 0 1em;
}

h2 {
  font-size: 16px;
  color: #333;
  margin: 2em auto 1em;
}

  body.api .content h2 {
    background: transparent url(../images/crud-sprite.png) left 2px no-repeat;
    padding-left: 22px;
    margin-bottom: 0.5em;
    margin-top: 1em;
  }

  body.api .change h2.title {
    background: none;
    padding-left: 0;
    font-size:24px;
  }

h2 span.step {
  color: #666;
}

h3 {
  font-size: 14px;
  color: #333;
  margin: 1.5em 0 .5em;
}

h5 {
  font-size: 13px;
}

h6 {
  font-size: 13px;
  color: #666;
}

a {
  color: #4183C4;
  text-decoration: none;
}

a:hover,
a:active {
  text-decoration:underline;
}

blockquote {
  margin:0 -5px;
  padding: 0px 20px;
  border-left: 4px solid #DDD;
  color: #777;
}

ul,
ol {
  margin: 0px;
  padding: 0px;
}

dt {
  font-weight: bold;
}

dd {
  padding-left: 1em;
  margin-bottom: 1em;
}

dd + dd {
  margin-bottom: 0;
}

span.attention,
p.attention {
  color: #e98400;
  font-style: italic;
}

a img {
  border: 0px;
}

/*------------------------------------------------------------------------------
     Header Styles
------------------------------------------------------------------------------*/

#header-wrapper {
  margin-bottom: 0;
  clear: both;
  height: 91px;
  background: white url(../images/background-v2.png) 0 0 repeat-x;
}

#header {
  margin: 0 auto;
  width: 920px;
}

#header a.logo {
  float: left;
  margin-top: 15px;
  display: inline-block;
}

#header ul.nav {
  float: right;
  padding: 8px 3px 8px 2px;
  font-weight: bold;
  text-shadow: white 1px 1px 0px;
  font-size: 12px;
  margin-top: 18px;
  background: #f5f5f5;
  filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#fcfcfc', endColorstr='#ececec');
  background:-webkit-gradient(linear, 0% 0%, 0% 100%, from(#fcfcfc), to(#e8e8e8));
  background:-moz-linear-gradient(270deg, #fcfcfc, #ececec);
  border-color:#eee;
  border:1px solid #e9e9e9;
  border-bottom-color:#f5f5f5;
  -webkit-border-radius:5px;
  -moz-border-radius:5px;
  border-radius:5px;
  -webkit-box-shadow:0 1px 1px rgba(0,0,0,0.2);
  -moz-box-shadow:0 1px 1px rgba(0,0,0,0.2);
  box-shadow:0 1px 1px rgba(0,0,0,0.2);
}

#header ul.nav li:first-child {
  background: transparent;
}

#header ul.nav li {
  float: left;
  margin: 0;
  padding: 0px 11px 0px 13px;
  list-style-type: none;
  background: url(../images/nav-rule.png) no-repeat 0px 50%;
  line-height: 1.4em;
}

#header a {
  outline: none;
  text-decoration: none;
}

/*------------------------------------------------------------------------------
     Sidebar
------------------------------------------------------------------------------*/

div.sidebar-shell {
  position: relative;
  float: right;
  margin: 35px 0 0;
}

div.sidebar-module {
  padding: 3px;
  background: #EEE;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  display: block;
  width: 222px;
  margin-bottom: 20px;
  font-size: 12px;
}

div.sidebar-module > ul {
  background: #fafafb;
  border: solid #CACACA;
  border-width: 1px 1px 0px 1px;
  margin: 0px;
}

div.sidebar-module > p {
  background: #fafafb;
  border: solid #CACACA;
  border-width: 1px;
  padding: 8px 10px;
  margin: 0px;
  display: block;
  line-height: 1.4em;
}

div.sidebar-module li {
  list-style-type: none;
}

div.sidebar-module > ul > li {
  border-bottom: 1px solid #CACACA;
  text-decoration: none;
}

div.sidebar-module > ul > li:hover {
  text-decoration: none;
}


div.sidebar-module > ul h3 {
  margin: 0px;
  color: #666;
  text-shadow: 1px 1px 0px #fff;
  border-bottom: 1px solid #CACACA;
  font-size: 14px;
  background-color: #e1e1e1;
  background-image: -moz-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -ms-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -o-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e1e1e1));
  background-image: -webkit-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: linear-gradient(top, #f1f1f1, #e1e1e1);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#f1f1f1', EndColorStr='#e1e1e1');
}

div.sidebar-module > ul li h3:hover,
div.sidebar-module > ul h3.disable {
  background-color: #e1e1e1;
  background-image: -moz-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: -ms-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: -o-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#e1e1e1), to(#d1d1d1));
  background-image: -webkit-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: linear-gradient(top, #e1e1e1, #d1d1d1);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#e1e1e1', EndColorStr='#d1d1d1');
}


div.sidebar-module > ul h3 a,
div.sidebar-module > ul h3.disable span {
  padding: 8px 0px 8px 10px;
  color: #666;
  display: block;
  text-decoration: none;
}

div.sidebar-module > ul h3.disable span {
  padding-left: 20px;
  background-image: url(../images/active-arrow.png);
  background-position: left center;
  background-repeat: no-repeat;
  cursor: default;
}
div.sidebar-module > ul h3:hover a {
  text-decoration: none;
}

div.sidebar-module ul ul,
div.sidebar-module .spacer {
  display: block;
  padding-bottom: 2px;
  background-color: #FAFAFB;
}

div.sidebar-module ul ul li {
  border-top: 1px solid #fff;
  border-bottom: 1px solid #e9ecee;
  font-weight: bold;
  color: #666;
}

div.sidebar-module ul ul li:hover,
div.sidebar-module li.disable {
  border-top: 1px solid #fafafb;
  border-bottom: 1px solid #e5e8ea;
  background-color: #f0f0f3;
}

div.sidebar-module li.disable {
  background-image: url(../images/active-arrow.png);
  background-position: left center;
  background-repeat: no-repeat;
}

div.sidebar-module ul ul li a,
div.sidebar-module ul ul li span {
  padding: 6px 0px 6px 10px;
  display: block;
  text-decoration: none;
}

div.sidebar-module ul ul li span {
  padding-left: 20px;
  cursor: default;
}

/* @end */

/*****************************************************************************/
/*
/* Footer
/*
/*****************************************************************************/



#footer {
  position: relative;
  bottom:0;
  font-size:13px;
  color: #636363;
  margin: 45px 0 0 0;
}

#footer a:hover {
  text-decoration: underline;
}

#footer li {
  list-style: none;
}

.footer_inner {
  width:960px;
  position: relative;
  margin: 0 auto;
}

#footer .upper_footer {
  min-height: 160px;
  overflow: hidden;
  background: url(../images/bg_footer_top.png) #f8f8f8 repeat-x;
}

#footer #blacktocat {
  height:130px;
  width:164px;
  float:left;
  background: url(../images/blacktocat.png) top left no-repeat;
  text-indent: -5000px;
  margin: 15px 20px 0 0;
}

#footer #blacktocat_ie {
  height:130px;
  width:164px;
  float:left;
  background: url(../images/blacktocat.png) no-repeat;
  text-indent: -5000px;
  margin: 15px 20px 0 0;
}

#footer .upper_footer ul.footer_nav {
  position: relative;
  float: left;
  width: 164px;
  margin: 20px 10px;
}

#footer .upper_footer ul.footer_nav h4 {
  margin: 0 0 5px 0;
  padding-bottom: 5px;
  border-bottom: thin solid #e1e1e1;
}

#footer .lower_footer {
  position: relative;
  background:url(../images/bg_footer_bottom.png) #fff repeat-x;
  overflow: hidden;
  clear:both;
}

#footer .lower_footer .home {
  display: block;
  position: absolute;
  background: url(../images/footer-logo.png) top left no-repeat;
  width: 100px;
  height: 50px;
  text-indent: -5000px;
}

#footer .lower_footer .home_ie {
  display: block;
  position: absolute;
  background: url(../images/footer-logo.png) top left no-repeat;
  width: 100px;
  height: 50px;
  text-indent: -5000px;
}

#footer .lower_footer #legal {
  float: left;
  width: 500px;
  height: 50px;
  line-height: 8px;
  margin: 25px 0 0 17px;
}

#footer .lower_footer #legal #legal_links {
  margin-left: 177px;
}

#footer .lower_footer div ul {
  float: left;
  text-indent: none;
  display:inline;
  margin-top: 15px;

}

#footer .lower_footer div ul li {
  display:inline;
  float: left;
  margin:  0 10px 0 0;
}

#footer .lower_footer div p {
  display:inline;
  float:left;
  clear: both;
  margin: 10px 0 0 177px;
}

#footer .lower_footer .sponsor {
  width: 295px;
  float: right;
  margin-top: 35px;
  padding-bottom: 25px;
}

#footer .lower_footer .sponsor .logo {
 float:left;
 margin: 0 10px 0 0;
}

#footer .lower_footer .sponsor a {
  color: #000;
}

/* end */

/*------------------------------------------------------------------------------
    Not Footer
------------------------------------------------------------------------------*/
#wrapper {
  clear: left;
  padding: 20px 25px;
  overflow:hidden;
  height: auto;
  width: 920px;
  margin: -20px auto 0;
  background: url(../images/background-white.png) 0 0 no-repeat;
}

.content {
  width: 670px;
  position: relative;
  float: left;
  color: #393939;
  z-index: 2;
}

.content dl {
  margin-left: 10px;
}

.content dt {
  color: #666;
}

.content ol {
  margin-left: 1.5em;
}

.content ul {
  list-style-type: disc;
  margin: 0 1.5em
}

.content dd ul {
  margin-top: 0;
}

.content li {
  line-height: 1.7em;
}

.content img {
  max-width: 100%;
  border: 1px solid #dddddd;
  -webkit-box-shadow: 1px 1px 3px #ddd;
  -moz-box-shadow: 1px 1px 3px #ddd;
  box-shadow: 1px 1px 3px #ddd;
}


.content .description {
  margin-left: 20px;
}

.content .verseblock-content {
  padding: 3px;
}

.content .verseblock-content,
.content .sectionbody .dlist dt,
.content p > tt,
.content dl code,
.content ul code,
p code {
  font: 12px Monaco,"Courier New","DejaVu Sans Mono","Bitstream Vera Sans Mono",monospace;
  color: #52595d;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  padding: 0px 3px;
  display: inline-block;
}

.content .sectionbody .dlist dt {
  margin-top: 10px;
}

.content .verseblock-content {
  padding: 3px;
}

.content .intro {
  color: #868686;
}

.change {
  padding-bottom: 1em;
}

  .change .meta {
    font-size: 16px;
    padding-bottom: 1em;
  }

    .change .who_when .author {
      color: #eee;
    }

    .change .who_when .published {
      color: #ccc;
    }

/* @end */

/*------------------------------------------------------------------------------
     Pre/Code Styles
------------------------------------------------------------------------------*/

code {white-space: nowrap;}

pre {
  border: 1px solid #cacaca;
  line-height: 1.2em;
  font: 12px Monaco,"Courier New","DejaVu Sans Mono","Bitstream Vera Sans Mono",monospace;
  padding: 10px;
  overflow:auto;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  background-color: #FAFAFB;
  color: #393939;
  margin: 0px;
}

ul + pre {
  margin-top: 1em;
}

pre code {white-space: pre;}

pre span.comment {color: #aaa;}

pre.headers {
  margin-bottom: 0;
  border-bottom-width: 0;
  -webkit-border-radius: 3px 3px 0 0;
  -moz-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0;
  color: #666;
  background-color: #f1f1f1;
  background-image: -moz-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -ms-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -o-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e1e1e1));
  background-image: -webkit-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: linear-gradient(top, #f1f1f1, #e1e1e1);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#f1f1f1', EndColorStr='#e1e1e1');
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
}

pre.no-response {
  -webkit-border-radius: 3px 3px;
  -moz-border-radius: 3px 3px;
  border-radius: 3px 3px;
  border-bottom: 1px solid #CACACA;
}

pre.headers + pre.highlight {
  -webkit-border-radius: 0 0 3px 3px;
  -moz-border-radius: 0 0 3px 3px;
  border-radius: 0 0 3px 3px;
}

pre.highlight {
  -webkit-border-radius:3px;
  -moz-border-radius:3px;
  border-radius:3px;
  background-color: #FAFAFB;
}

pre.terminal {
  background-color: #444;
  color: #fff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 2px solid #DEDEDE;
  position: relative;
  padding: 10px;
  text-shadow: none;
  background-image: none;
  filter: none;
}

pre.terminal em {
  color: #f9fe64;
}

span.codeline {
  display: block;
  position: relative;
}

span.codeline:hover {
  background-color: #292929;
  margin: 0px;
  padding-left: 3px;
  margin-left: -3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  color: #666666;
}

span.codeline span {
  display: inline-block;
  font-size: 10px;
  color: #fff;
  padding: 0 0.3em 0.05em;
  position: absolute;
  right: 0px;
  top: 0px;
  text-indent: -9999px;
  background-image: url(../images/qmark.png);
  background-repeat: no-repeat;
  background-position: 1px 3px;
  max-width: 8px;
  min-width: 8px;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  cursor: default;
}

span.codeline span:hover {
  display: inline-block;
  text-indent: 0px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background: #000;
  border: 1px solid #292929;
  max-width: 1000px;
}

span.codeline:hover em {
  color: #666666;
}

pre.bootcamp {
  white-space: normal;
  margin-left: -10px;
  background-image: none;
}

span.bash-output {
  color: #63e463;
  display: block;
  position: relative;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

/* end */

/*------------------------------------------------------------------------------
    More Info Expander
------------------------------------------------------------------------------*/

.more-info {
  margin: 10px 0;
  position: relative;
}
.more-info > h4 {
  background-image: url('../images/dropdown_sprites.jpg');
  background-repeat: no-repeat;
  padding: .25em 0 .25em 25px;
  cursor: pointer;
  color: #4183C4;
  font-weight: normal;
}
.more-info h4.compressed {
  background-position: 0 0;
}
.more-info:hover h4.compressed {
  background-position: 0 -23px;
}
.more-info h4.expanded {
  background-position: 0 -46px;
}
.more-info:hover h4.expanded {
  background-position: 0 -69px;
}

.more-info .more-content {
  display: none;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background-color: #FFFFFF;
  border: 3px solid #DDDDDD;
  padding: 1em 2em;
  -webkit-box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  margin: 15px 0 30px;
}

.more-info .more-content h4 {
  margin-top: 1em;
}

.more-info .more-content pre {
  margin-left: 0px;
}

/****************************/
/*       List Module        */
/****************************/

.list-module h2 {
  border: solid #cacaca;
  border-width: 1px;
  border-radius: 3px 3px 0px 0px;
  -moz-border-radius: 3px 3px 0px 0px;
  -webkit-border-bottom-right-radius: 0px;
  -webkit-border-bottom-left-radius: 0px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  padding: 6px 10px;
  background-color: #f1f1f1;
  background-image: -moz-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -ms-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -o-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e1e1e1));
  background-image: -webkit-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: linear-gradient(top, #f1f1f1, #e1e1e1);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#f1f1f1', EndColorStr='#e1e1e1');
  color: #666;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
  font-size: 16px;
  line-height: 22px;
  margin: 0px;
}

.list-module .list-body {
  border: solid #cacaca;
  border-width: 0px 1px 1px 1px;
  border-radius: 0px 0px 3px 3px;
  -moz-border-radius: 0px 0px 3px 3px;
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-bottom-left-radius: 3px;
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  background-color: #fafafb;
  color: #666;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
}

.list-module .list-body .icon {
  display: block;
  height: 28px;
  width: 28px;
  position: absolute;
  top: 10px;
  left: 10px;
  background: transparent url(images/popular_guide_sprites.png) 0 0 no-repeat;
}

.list-module a {
  border-top: 1px solid #fff;
  border-bottom: 1px solid #e9ecee;
  padding: 6px 10px;
  position: relative;
  display: block;
}

.list-module a:hover {
  border-top: 1px solid #fafafb;
  border-bottom: 1px solid #e5e8ea;
  background-color: #f0f0f3;
  text-decoration: none;
}

.list-module a h3 {
  color: #4183C4;
}

.list-module a:hover h3 {
  text-decoration: underline;
}

.list-module ul {
  list-style-type: none;
  margin: 0px;
}

.list-module h3 {
  margin: 0px;
  font-size: 13px;
}

.list-module .list-body a p {
  color: #666;
  margin: 0px;
}

/* @end */

/****************************/
/*  Expandable List Module  */
/****************************/

div.expandable > ul h3 {
  display: table;
  width: 100%;
}

div.expandable > ul h3 > a {
  display: table-cell;
  background-color: #e1e1e1;
  background-image: -moz-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -ms-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -o-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e1e1e1));
  background-image: -webkit-linear-gradient(top, #f1f1f1, #e1e1e1);
  background-image: linear-gradient(top, #f1f1f1, #e1e1e1);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#f1f1f1', EndColorStr='#e1e1e1');
}

div.expandable > ul h3 > a:hover {
  background-color: #e1e1e1;
  background-image: -moz-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: -ms-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: -o-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#e1e1e1), to(#d1d1d1));
  background-image: -webkit-linear-gradient(top, #e1e1e1, #d1d1d1);
  background-image: linear-gradient(top, #e1e1e1, #d1d1d1);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorStr='#e1e1e1', EndColorStr='#d1d1d1');
}

div.expandable > ul h3 > a.collapsed,
div.expandable > ul h3 > a.expanded {
  background-image: url(../images/expand-arrows.png);
  background-position: 0px -3px;
  background-repeat: no-repeat;
  width: 13px;
  border-right: 1px solid #cacaca;
  padding: 8px 11px;
}

div.expandable > ul h3 > a.expanded {
  background-position: -38px -3px;
}

div.expandable > ul h3 > a.collapsed:hover {
  background-image: url(../images/expand-arrows.png);
  background-position: 0px -43px;
  background-repeat: no-repeat;
  width: 13px;
  border-right: 1px solid #cacaca;
  padding: 8px 11px;
}

div.expandable > ul h3 > a.expanded:hover {
  background-position: -38px -43px;
}

/* @end */
