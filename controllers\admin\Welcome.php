<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
 * DO NOT FORGOT TO CHANGE ENVIRONMENT=development IN index.php 
 * TO ENVIRONMENT=production
 * BEFORE UPLOAD SITE TO AIR
 */

class Welcome extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
    }
    
    public function index() {
        //$developer_id = $this->aauth->create_user('<EMAIL>', 'waveprojects!', 'developers');
        //$this->aauth->add_member($developer_id, 'Admin');
//        
        //$admin_id = $this->aauth->get_user_id('<EMAIL>');
        //$this->aauth->delete_user($admin_id);
//		
        //$admin_id = $this->aauth->create_user('<EMAIL>', 'reactStore', 'admin');
        //$this->aauth->add_member($admin_id, 'Admin');
        
        $this->load->helper(array('form', 'url'));
        $this->load->library('form_validation');
        
        $this->form_validation->set_rules('username', 'Username', 'trim|required|min_length[1]|max_length[64]');
        $this->form_validation->set_rules('password', 'Password', 'trim|required|min_length[4]');

        $redirect = $this->input->post_get('redirect') ? $this->input->post_get('redirect') : base_url('admin/site');
        
        if ($this->form_validation->run() == FALSE) {
            //form is not valid, do nothing
        } else {
            if($this->aauth->login(set_value('username'), set_value('password'))) {
                //user login successfuly go to home page 
                redirect($redirect);
            } else {
                //print_r($this->aauth->get_errors_array());
                
            }
        }
        //show login page
        $this->load->view('admin/login');
    }
    
    public function logout() {
        $this->aauth->logout();
        redirect('admin');
    }
    
   
    
}
