<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Gallery extends CI_Controller {
    
    const TABLE_CATEGORIES = 'gallery_categories';
    const TABLE_GALLERY = 'gallery';
    
    public function __construct() {
        parent::__construct();
        
        //if is not logged in die  
        if( ! $this->aauth->is_loggedin() ) { 
            redirect(base_url('admin?redirect=' . current_url() . getQS()), 'refresh', 401);
            die('The user is not connected.'); 
        } else if( ! $this->aauth->is_allowed($this->router->fetch_class() . '_' . $this->router->fetch_method())) {
            die('The user not have permission to view the content.'); 
        }
        $this->load->model('msite');
        
    }
    
    public function index($category_id = FALSE) {
        $migration_file = read_file(APPPATH . 'migration/' . self::TABLE_CATEGORIES . '.json');
        $data['migration'] = $migration_file ? json_decode($migration_file, TRUE) : FALSE;
        if($category_id){
            $data['selected_category'] = $data['object'] = $category_id ? $this->msite->get_object(self::TABLE_CATEGORIES, $category_id) : NULL;
            if(isset($data['migration']['fields']) && !empty($data['migration']['fields'])) {
                foreach($data['migration']['fields'] as $field) {
                    if($field['type'] === 'table') {
                        $data['options'][$field['name']] = $this->msite->get_all_objects($field['options']['table']);
                    }
                }
            }
        }
        
        $data['categories'] = $this->msite->get_all_objects(self::TABLE_CATEGORIES);
        
        
        $data['page'] = $this->input->get('page') ? $this->input->get('page') : 1;
        $data['limit'] = $this->input->get('limit') ? $this->input->get('limit') : 12;
        $data['order'] = $this->input->get('order') ? $this->input->get('order') : 'sort';
        $data['sort'] = $this->input->get('sort') ? $this->input->get('sort') : 'DESC';

        if($category_id) {$this->msite->set_where(" category_id = '$category_id'");}
        $data['total'] = $this->msite->count_all_objects(self::TABLE_GALLERY);
        $this->msite->limit_objects($data['page'], $data['limit']);
        $this->msite->sort_objects($data['order'], $data['sort']);
        if($category_id) {$this->msite->set_where(" category_id = '$category_id'");}
        $data['gallery'] = $this->msite->get_all_objects(self::TABLE_GALLERY);
        
        
        $data['category_id'] = $category_id;
                
        $data['view'] = $this->router->fetch_class() . '/index';
        $data['script'] = $this->router->fetch_class() . '/script';
        $this->load->view('admin/index', $data);
    }
    
    public function images($gallery_id = FALSE) {
        $migration_file = read_file(APPPATH . 'migration/' . self::TABLE_GALLERY . '.json');
        $data['migration'] = $migration_file ? json_decode($migration_file, TRUE) : FALSE;
        if($gallery_id){
            $data['object'] = $gallery_id ? $this->msite->get_object(self::TABLE_GALLERY, $gallery_id) : NULL;
            if(isset($data['migration']['fields']) && !empty($data['migration']['fields'])) {
                foreach($data['migration']['fields'] as $field) {
                    if($field['type'] === 'table') {
                        $data['options'][$field['name']] = $this->msite->get_all_objects($field['options']['table']);
                    }
                }
            }
        }
        
        $data['parent_selected']['category_id'] = $this->input->get('category_id');
        
        $data['selected_category'] = isset($data['object']) ? $this->msite->get_object(self::TABLE_CATEGORIES, $data['object']->Arg('category_id')) : $this->msite->get_object(self::TABLE_CATEGORIES, $this->input->get('category_id'));
        $data['categories'] = $this->msite->get_all_objects(self::TABLE_CATEGORIES);
        
        $data['gallery_id'] = $gallery_id;
        
        if(isset($data['migration']['fields']) && !empty($data['migration']['fields'])) {
            foreach($data['migration']['fields'] as $field) {
                if($field['type'] === 'table') {
                    $data['options'][$field['name']] = $this->msite->get_all_objects($field['options']['table']);
                }
            }
        }
        
        
        $data['view'] = $this->router->fetch_class() . '/image';
        $data['script'] = $this->router->fetch_class() . '/script';
        $this->load->view('admin/index', $data);
    }
    
    
}