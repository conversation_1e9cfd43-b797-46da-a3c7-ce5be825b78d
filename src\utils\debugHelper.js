// Debug helper for troubleshooting white screen issues
class DebugHelper {
  constructor() {
    this.isDebugMode = process.env.REACT_APP_DEBUG === 'true';
    this.logs = [];
    this.startTime = Date.now();
    this.checkInterval = null;
    
    if (this.isDebugMode) {
      this.init();
    }
  }

  init() {
    console.log('🔍 Debug Helper initialized');
    this.startWhiteScreenDetection();
    this.logAppState();
  }

  log(message, data = null) {
    if (!this.isDebugMode) return;
    
    const timestamp = Date.now() - this.startTime;
    const logEntry = {
      timestamp,
      message,
      data,
      time: new Date().toISOString()
    };
    
    this.logs.push(logEntry);
    console.log(`🔍 [${timestamp}ms] ${message}`, data || '');
    
    // Keep only last 100 logs
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-100);
    }
  }

  startWhiteScreenDetection() {
    // Check for white screen every 2 seconds
    this.checkInterval = setInterval(() => {
      this.checkForWhiteScreen();
    }, 2000);

    // Stop checking after 2 minutes
    setTimeout(() => {
      if (this.checkInterval) {
        clearInterval(this.checkInterval);
        this.checkInterval = null;
        this.log('White screen detection stopped');
      }
    }, 120000);
  }

  checkForWhiteScreen() {
    const body = document.body;
    const hasContent = body && (
      body.children.length > 0 ||
      body.textContent.trim().length > 0
    );

    if (!hasContent) {
      this.log('⚠️ Potential white screen detected', {
        bodyChildren: body?.children.length || 0,
        bodyText: body?.textContent?.trim() || '',
        location: window.location.href
      });
      
      // Try to recover
      this.attemptRecovery();
    }
  }

  attemptRecovery() {
    this.log('🔄 Attempting recovery from white screen');
    
    // Check if React root exists
    const root = document.getElementById('root');
    if (!root || root.children.length === 0) {
      this.log('❌ React root is empty, suggesting reload');
      this.showRecoveryDialog();
    }
  }

  showRecoveryDialog() {
    // Create a simple recovery dialog
    const dialog = document.createElement('div');
    dialog.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 20px;
      border: 2px solid #e74c3c;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      z-index: 10000;
      text-align: center;
      font-family: Arial, sans-serif;
      direction: rtl;
    `;
    
    dialog.innerHTML = `
      <h3 style="color: #e74c3c; margin-bottom: 15px;">זוהה מסך לבן</h3>
      <p style="margin-bottom: 15px;">האפליקציה נתקעה במסך לבן</p>
      <button onclick="window.location.reload()" style="
        padding: 10px 20px;
        background: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      ">רענן דף</button>
      <button onclick="this.parentElement.remove()" style="
        padding: 10px 20px;
        background: #95a5a6;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      ">סגור</button>
    `;
    
    document.body.appendChild(dialog);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (dialog.parentElement) {
        dialog.remove();
      }
    }, 10000);
  }

  logAppState() {
    this.log('App state check', {
      url: window.location.href,
      userAgent: navigator.userAgent,
      localStorage: this.getLocalStorageInfo(),
      sessionStorage: this.getSessionStorageInfo(),
      reactVersion: this.getReactVersion()
    });
  }

  getLocalStorageInfo() {
    try {
      return {
        userData: !!localStorage.getItem('userData'),
        rakazid: !!localStorage.getItem('rakazid'),
        sayeretid: !!localStorage.getItem('sayeretid'),
        keys: Object.keys(localStorage)
      };
    } catch (e) {
      return { error: e.message };
    }
  }

  getSessionStorageInfo() {
    try {
      return {
        keys: Object.keys(sessionStorage),
        length: sessionStorage.length
      };
    } catch (e) {
      return { error: e.message };
    }
  }

  getReactVersion() {
    try {
      return window.React?.version || 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  exportLogs() {
    const logsData = {
      logs: this.logs,
      appState: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }
    };
    
    const blob = new Blob([JSON.stringify(logsData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `debug-logs-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  cleanup() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }
}

// Create global instance
const debugHelper = new DebugHelper();

// Make it available globally for manual debugging
window.debugHelper = debugHelper;

export default debugHelper;
