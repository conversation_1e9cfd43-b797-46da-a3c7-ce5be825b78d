{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.3.89", "private": true, "engines": {"node": ">=22.0.0"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hebcal/core": "^5.9.5", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^8.0.0", "@radix-ui/react-slot": "^1.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.0", "@testing-library/user-event": "^14.6.1", "axios": "^1.8.4", "bootstrap": "^5.3.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.9.2", "hebrew-date": "^1.0.7", "html2pdf.js": "^0.10.3", "idb": "^8.0.2", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "postcss-loader": "^8.1.1", "react": "^19.1.0", "react-bootstrap": "^2.10.9", "react-circular-progressbar": "^2.2.0", "react-datepicker": "^8.3.0", "react-device-detect": "^2.2.3", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.0", "react-infinite-scroller": "^1.2.6", "react-lottie-player": "^2.1.0", "react-multi-select-component": "^4.3.4", "react-router-dom": "^7.5.1", "react-scripts": "^5.0.1", "react-scroll": "^1.9.3", "react-search-autocomplete": "^8.5.2", "react-share": "^5.2.2", "react-stopwatch": "^2.0.4", "react-text-truncate": "^0.19.0", "react-timer-hook": "^4.0.5", "react-toastify": "^11.0.5", "sass": "^1.86.3", "stylis": "^4.3.6", "stylis-plugin-rtl": "^2.1.1", "sweetalert2": "^11.19.1", "tailwindcss-animate": "^1.0.7", "validator": "^13.15.0", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "settings": {"react": {"version": "detect"}}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610", "devDependencies": {"@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-separator": "^1.1.4", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "daisyui": "^5.0.27", "lucide-react": "^0.503.0", "postcss": "^8.5.3", "raw-loader": "^4.0.2", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "@babel/plugin-proposal-private-property-in-object": "7.21.0"}}