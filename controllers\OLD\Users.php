<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+OtzarHaaretz';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+OtzarHaaretz-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
        
        $this->load->model('msiteWs');
        $this->load->model('OtzarHaretz');
        
        
//        if($param === 'uploadMethod') {
//            
//            $postCode = $this->input->post('siteCode');
//            if( $postCode != md5($this->data['code']) ) {
//                die('siteCodeERROR');
//            }
//        }
        
        if(isset($param['action']) && $param['action'] == 'resendPass') {
            
            if( $param['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
            
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    
    public function loginUser ($param = FALSE) {
          
        $this->_loaderWS($param);
                
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('username','password'));
        
        
        if( !empty($jsonPosts['username']) && !empty($jsonPosts['password']) ) {
            
                
                $this->db->select('*');
                $this->db->from('leadsLandpage');
                $this->db->where('user', $jsonPosts['username']);
                $this->db->where('status', 1);
                //$this->db->where('password', md5($jsonPosts['password']));
                $this->db->where('password', md5($jsonPosts['password']));
                $result= $this->db->get();

                $user = $result->row_array();
                
                
                if(!empty($user)) {
                    
                    $userCredential = 'user'; //superAdmin //admin //user

                    $this->load->helper('text');
                    
                    $name = $user['firstName'].' '.$user['lastName'];
                    $name = character_limiter($name, 20,'...');
                    
                    
                    $output['data'] = array(
                        'id' => $user['id'],
                        'firstName' => $user['firstName'],
                        'lastName' => $user['lastName'],
                        'username' => $user['user'],
                        'email' => $user['email'],
                        'phone' => $user['phone'],
                        
                        'name' => $name,
                        'userCredential' => $userCredential,
                        'token' => JWT::encode($user['id'], $this->data['usersCode']),
                        'dateTimeLogin' => date("Y-m-d H:i:s")
                    );

                } else {
                    
                     $this->db->select('*');
                    $this->db->from('leadsLandpage');
                    $this->db->where('user', $jsonPosts['username']);
                    $this->db->where('status', 0);
                    //$this->db->where('password', md5($jsonPosts['password']));
                    $this->db->where('password', md5($jsonPosts['password']));
                    $result= $this->db->get();

                    $user = $result->row_array();

                    if(!empty($user)) {
                        $output['error'] = 'שם משתמש נעול. '.'נא לצור קשר בטלפון: '."9273*";

                    } else {
                        $output['error'] =  'שם משתמש או סיסמה לא נכון';
                    }
                    
                }
                
                
                
        } else {
            
            $output['error'] = 'נא הכניס שם משתמש וסיסמה';
            
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    public function md5Show($param=false) {
        
        //https://otzarhaaretz.wdev.co.il/api/users/md5Show
        //
        //user: otzarHaretz
        //1377713  //5adfb2bc411509f41715cf08f57f783e
        
        //user: service
        //pass: serviceuser
        
        echo md5('serviceuser');
        
    }
    
    public function loginUserAdmin ($param = FALSE) {
          
        $this->_loaderWS($param);
                
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('username','password'));
        
        
        if( !empty($jsonPosts['username']) && !empty($jsonPosts['password']) ) {
            
                
                $this->db->select('*');
                $this->db->from('suppliers');
                $this->db->where('username', $jsonPosts['username']);
                $this->db->where('status', 1);
                //$this->db->where('password', md5($jsonPosts['password']));
                $this->db->where('passwordMd5', md5($jsonPosts['password']));
                $result= $this->db->get();

                $user = $result->row_array();
                
                
                if(!empty($user)) {
                    
                    $userCredential = 'admin'; //superAdmin //admin //user

                    $this->load->helper('text');
                    
                    $name = $user['name'];
                    $name = character_limiter($name, 20,'...');
                    
                    
                    $output['data'] = array(
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'phone' => $user['phone'],
                        
                        'name' => $name,
                        'userCredential' => $userCredential,
                        'token' => JWT::encode($user['id'], $this->data['usersCode']),
                        'dateTimeLogin' => date("Y-m-d H:i:s")
                    );

                } else {
                    
                    
                    $this->db->select('id');
                    $this->db->from('suppliers');
                    $this->db->where('username', $jsonPosts['username']);
                    $this->db->where('status', 0);
                    //$this->db->where('password', md5($jsonPosts['password']));
                    $this->db->where('passwordMd5', md5($jsonPosts['password']));
                    $result= $this->db->get();

                    $user = $result->row_array();
                    
                    if(!empty($user)) {
                        
                        $output['error'] = 'שם משתמש נעול. '.'נא לצור קשר בטלפון: '."9273*";
                        
                        
                    } else {
                        $output['error'] =  'שם משתמש או סיסמה לא נכון';
                    }
                    
                    
                    
                    
                }
                
                
                
        } else {
            //set_status_header(500);
            $output['error'] = 'נא הכניס שם משתמש וסיסמה';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function loginUserSystem ($param = FALSE) {
          
        $this->_loaderWS($param);
                
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('username','password'));
        
        
        if( !empty($jsonPosts['username']) && !empty($jsonPosts['password']) ) {
            
                
                $this->db->select('*');
                $this->db->from('app_Users');
                $this->db->where('username', $jsonPosts['username']);
                $this->db->where('passwordMd5', md5($jsonPosts['password']));
                $this->db->where('status', 1);
                
                $result= $this->db->get();

                $user = $result->row_array();
                
                
                if(!empty($user)) {
                    
                    // user: reports : 13777 | 74367f78d716836cc099eeb6497f8703
                    $userCredential = 'superAdmin'; //superAdmin //admin //user

                    $this->load->helper('text');
                    
                    $name = $user['name'];
                    $name = character_limiter($name, 20,'...');
                    
                    
                    $output['data'] = array(
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'phone' => $user['phone'],
                        'userType' => $user['userType'],
                        'name' => $name,
                        'userCredential' => $userCredential,
                        'token' => JWT::encode($user['id'], $this->data['usersCode']),
                        'dateTimeLogin' => date("Y-m-d H:i:s")
                    );

                } else {
                    $output['error'] =  'שם משתמש או סיסמה לא נכון';
                }
                
                
                
        } else {
            //set_status_header(500);
            $output['error'] = 'נא הכניס שם משתמש וסיסמה';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
        
    public function logoutUser ($param = FALSE) {
        
        //$this->_loaderWS($param);
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId'));
        
        
        if( !empty($jsonPosts['userId']) ) {
            
                $output = 'logOut';
                
        } else {
            //set_status_header(500);
            $output = 'error NO-DATA';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function getMoneyUser($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized' or $this->input->get('sebas')==1) {
            
            if($this->input->get('sebas')==1) {
                $jsonPosts['userId'] = 2;
            }            
        
            $output['money'] = $this->OtzarHaretz->getUserMoney($jsonPosts);
            
            if($this->input->get('sebas')==1) {
                //die(' _die');
                
            }
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function resendPass ($param = FALSE) {
          
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('email','siteCode'));
        
        $params = array(
            'action' => 'resendPass',
            'siteCode' => $jsonPosts['siteCode']
        );
        
        $this->_loaderWS($params);
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();

        if( !empty($jsonPosts['email']) ) {
         
            
            $this->db->select('email,password,firstName,lastName,user');
            $this->db->from('leadsLandpage');
            $this->db->where('email', $jsonPosts['email']);
            $result= $this->db->get();
            $user = $result->row_array();
            
            if( !empty($user['email']) && !empty($user['password']) ) {
                
                $this->load->helper('string');
                $newPassword = strtolower( random_string($type = 'alnum', $len = 5) );
                
                //$newPassword = rand(1111,9999);
                $newPassUser['password'] = md5($newPassword);
                
                $this->db->where('email', $user['email']);
                $update = $this->db->update('leadsLandpage', $newPassUser); 
                
                if($update) {
                    
                    $output['ok'] = 'הסיסמה נשלחה למייל';
                    $user['text'] = 'שחזור סיסמה';
                    
                    $user['password'] = $newPassword;
                    $html_body = $this->bodyMailNoUser($user);
                    
                    $to_emails = $user['email'];
                    $subject = 'אוצר הארץ';
                    $output['emailResponse'] = $this->OtzarHaretz->send_email($to_emails, $subject,$html_body);
                    
                }else {
                    $output['error'] = 'שגיאה';
                }
                
                
            } else {
                
                $output['error'] = 'כתובת מייל לא קיימת במערכת';
                
            }
                
                
        } else {
            $output['error'] = 'כתובת מייל לא קיימת במערכת';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    public function resendPassAdmin ($param = FALSE) {
          
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('email','siteCode'));
        
        $params = array(
            'action' => 'resendPass',
            'siteCode' => $jsonPosts['siteCode']
        );
        
        $this->_loaderWS($params);
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();

        if( !empty($jsonPosts['email']) ) {
         
            
            $this->db->select('passwordMd5,name,username,phone');
            $this->db->from('suppliers');
            $this->db->where('phone', $jsonPosts['email']); // $jsonPosts['email'] > phone on admin!!!
            $result= $this->db->get();
            $user = $result->row_array();
            
            if( !empty($user['phone']) && !empty($user['passwordMd5']) ) {
                
                $this->load->helper('string');
                $newPassword = strtolower( random_string($type = 'alnum', $len = 5) );
                
                //$newPassword = rand(1111,9999);
                $newPassUser['passwordMd5'] = md5($newPassword);
                
                $this->db->where('phone', $user['phone']);
                $update = $this->db->update('suppliers', $newPassUser); 
                
                if($update) {
                    
                    $output['ok'] = 'הסיסמה נשלחה לטלפון בהצלחה';
                    $user['text'] = 'שחזור סיסמה';
                    
                    $phone = $user['phone'];
        
                    $message = 'סיסמה חדשה. שם משתמש: ';
                    $message .=  $user['username'].'. ';
                    $message .=  'סיסמה: '.$newPassword;
                    

                    $output['emailResponse'] = $this->OtzarHaretz->sendSMS($phone, $message, $from = 'OtzarHaretz', $param = FALSE);
                    
                    //$user['password'] = $newPassword;
                    //$html_body = $this->bodyMailNoUser($user);
                    
                    //$to_emails = $user['email'];
                    //$subject = 'אוצר הארץ';
                    //$output['emailResponse'] = $this->OtzarHaretz->send_email($to_emails, $subject,$html_body);
                    
                }else {
                    $output['error'] = 'שגיאה';
                }
                
                
            } else {
                
                $output['error'] = 'מספר הטלפון לא קיים במערכת.';
                
            }
                
                
        } else {
            $output['error'] = 'כתובת מייל לא קיימת במערכת';
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    
    private function bodyMailNoUser($params) {
        
         //table lead style
        
        $td_style_title="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 22px;
                  padding: 20px 0 5px;color:#009eb3;";  
        
        $td_style_1="background-color: rgb(235, 235, 235); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;";  

        $td_style_2="background-color: rgb(213, 213, 213); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;padding:10px 0;";
        
        $td_style_p ="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 14px;
                  padding:0px 0;color: #100f15;"; 
        
        $td_style_p2 ="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:regular;text-align: right;font-size: 14px;
                  padding:0px 20px;color: #100f15;"; 
        
        $imgTop = base_url().IMG.'mailImg/top.jpg?v='.VERSION;
        $imgDown = base_url().IMG.'mailImg/down-noEnter.jpg?v='.VERSION;
        $logo = base_url().IMG.'mailImg/logo.jpg?v='.VERSION;
        
        $tableData = "<table style='background: #f3f3f3;margin:0;padding:0;' align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' >";
        
        $tableData .= "<tbody>";
        $tableData .= "<tr><td colspan=2 style='$td_style_title'>שלום {$params['firstName']} {$params['lastName']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['text']}</td></tr>";
        
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['user']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>שם משתמש</span>
                </td>
            </tr>";
                
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['password']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>סיסמה</span>
                </td>
            </tr>";
        
        
        $tableData .= "</tbody>";
        $tableData .="</table>";
       

        $body_mail= "
        <div style='text-align: center;text-direction:rtl; font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;'>
        <br/><br/>";
        
        $body_mail .= "<table align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' style='width: 345px;'>
        <tbody>";
        
        $body_mail .= "<tr><td style='padding: 0px;' border='0' cellpadding='0' cellspacing='0'><img style='display: inherit;' src='$imgTop' border='0'></td></tr>";
        $body_mail .= "<tr><td style='padding: 0px 10px' >$tableData</td></tr>";
                
        $body_mail .= "<tr>
                <td style='text-align:center'>
                  <img style='display: inherit;' src='$imgDown' border='0'>
                </td></tr>";
        
        $body_mail .= "<tr>
                <td style='text-align:center'>
                  <a style='padding: 0;' href='https://waveproject.co.il/'>
                  <img style='display: inherit;' src='$logo' border='0'>
                  </a>
                </td></tr>";
                
                
                
        $body_mail .= "</tbody></table><br/><br/></div>";
        
        return $body_mail;  
    }
    
    
    public function editUser($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(
                    array('userId',
                        'token',
                        'userCredential',
                        'firstName',
                        'lastName',
                        'email',
                        'username',
                        'phone',
                        'pass')
                );
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if( !empty($jsonPosts['firstName']) &&
                !empty($jsonPosts['lastName']) &&
                !empty($jsonPosts['email']) &&
                !empty($jsonPosts['username']) &&
                !empty($jsonPosts['phone']) ) 
                {

                

                $newData = array(
                                'firstName' => $jsonPosts['firstName'],
                                'lastName' => $jsonPosts['lastName'],
                                'email' => $jsonPosts['email'],
                                'user' => $jsonPosts['username'],
                                'phone' => $jsonPosts['phone']
                            );
                
                $update = false;
                $flagDuplicateUser = false;
                $flagPasswordError = false;
                
                
                $this->db->select('id');
                $this->db->from('leadsLandpage');
                $this->db->where('id !=', $jsonPosts['userId']);
                $this->db->where('user', $jsonPosts['username']);
                $result= $this->db->get();

                if($result->num_rows() > 0) {
                    $flagDuplicateUser = true;
                }
                
                if(!empty($jsonPosts['pass'])) {
                    
                    $pass = $jsonPosts['pass'];
                    
                    if( strlen($pass) < 5 ) {
                     
                        $flagPasswordError = 'הסיסמה חייבת להיות מעל חמישה תווים';
                                
                    } else if (!preg_match('~[0-9]+~', $pass)) {
                        
                        $flagPasswordError = 'חייב לפחות מספר אחד בסיסמה';
                        
                    }  else if ( !preg_match("/[a-z]/i", $pass) && !preg_match("/\p{Hebrew}/u", $pass) ) {
                        
                        $flagPasswordError = 'חייב לפחות אות אחת';
                        
                    } else {
                        
                        $newData['password'] = md5($pass);
                        
                    }
                }
                
                if( !$flagDuplicateUser && !$flagPasswordError ) {
                    
                    $this->db->where('id', $jsonPosts['userId']);
                    $update = $this->db->update('leadsLandpage', $newData); 
                }
                

                if($update) {
                    
                    $output['newSession']['data'] = array(
                        'id' => $jsonPosts['userId'],
                        
                        'username' => $jsonPosts['username'],
                        'email' => $jsonPosts['email'],
                        'phone' => $jsonPosts['phone'],
                        'name' => $jsonPosts['firstName'].' '.$jsonPosts['lastName'],
                        
                        'userCredential' => 'admin',
                        'token' => JWT::encode($jsonPosts['userId'], $this->data['usersCode']),
                        'dateTimeLogin' => date("Y-m-d H:i:s")
                    );
                    
                    
                    $output['ok'] = 'הפרטים עודכנו בהצלחה';
                }              
                
                else if($flagDuplicateUser) {
                
                    $output['error'] = 'שם משתמש כבר קיים במערכת';
                }
                
                else if($flagPasswordError) {
                
                    $output['error'] = 'שגיאה: '.$flagPasswordError;
                }
                
                } else {

                    $output['error'] = 'שגיאה';

                }    
                    
        }
            
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function editUserSystem($param = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(
                    array('userId',
                        'token',
                        'userCredential',
                        'rowId',
                        'firstName',
                        'lastName',
                        'email',
                        'phone',
                        'address',
                        'comments',
                        'pass',
                        'totalPrice'
                        )
                );
        
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        $output['rowId'] = $jsonPosts['rowId'];
        
        
        if($checkUserCredentials != 'unauthorized' ) {
            
            if( !empty($jsonPosts['firstName']) &&
                !empty($jsonPosts['lastName']) &&
                !empty($jsonPosts['email']) &&
                !empty($jsonPosts['rowId']) &&
                !empty($jsonPosts['totalPrice']) &&
                !empty($jsonPosts['phone']) ) 
                {

                $totalPrice = $this->OtzarHaretz->checkTotalPrice($jsonPosts);
                
                $newData = array(
                                'firstName' => $jsonPosts['firstName'],
                                'lastName' => $jsonPosts['lastName'],
                                'email' => $jsonPosts['email'],
                                'comments' => $jsonPosts['comments'],
                                'address' => $jsonPosts['address'],
                                'phone' => $jsonPosts['phone']
                            );
                
                if($totalPrice) {
                    
                    if( (float)$jsonPosts['totalPrice'] > 5000) {
                        $jsonPosts['totalPrice'] = 5000;
                    }
                    
                    $newData['TotalPrice'] = (float)$jsonPosts['totalPrice'];
                }
                
                $update = false;
                
                if(!empty($jsonPosts['pass']) &&  strlen($jsonPosts['pass']) >= 4) {
                    
                    $newData['password'] = md5($jsonPosts['pass']);
                    
                }
                    
                $this->db->where('id', $jsonPosts['rowId']);
                $update = $this->db->update('leadsLandpage', $newData); 
                

                if($update) {
                    $output['ok'] = 'הפרטים עודכנו בהצלחה';
                }              
                
                else {
                
                    $output['error'] = 'הפרטים לא עודכנו';
                }
                
                
            } else {

                $output['error'] = 'הפרטים לא עודכנו';

            }    
                    
        }
            
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function editUserAdmin($jPData = FALSE) {
        
        $this->_loaderWS();
        
        //$pass = md5('1234');
        
        //$newData['passwordMd5'] = $pass;
        
        //$this->db->where('id', 2);
        //$update = $this->db->update('app_Users', $newData); 
        //die();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('admin','superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(
                    array('userId',
                        'token',
                        'userCredential',
                        'firstName',
                        'name',
                        'email',
                        'username',
                        'phone',
                        'pass')
                );
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if( !empty($jsonPosts['name']) &&
                !empty($jsonPosts['email']) &&
                !empty($jsonPosts['username']) &&
                !empty($jsonPosts['phone']) ) 
                {

                

                $newData = array(
                                'name' => $jsonPosts['name'],
                                'email' => $jsonPosts['email'],
                                'username' => $jsonPosts['username'],
                                'phone' => $jsonPosts['phone']
                            );
                
                $update = false;
                $flagDuplicateUser = false;
                $flagPasswordError = false;
                
                
                $this->db->select('id');
                $this->db->from('suppliers');
                $this->db->where('id !=', $jsonPosts['userId']);
                $this->db->where('username', $jsonPosts['username']);
                $result= $this->db->get();

                if($result->num_rows() > 0) {
                    $flagDuplicateUser = true;
                }
                
                if(!empty($jsonPosts['pass'])) {
                    
                    $pass = $jsonPosts['pass'];
                    
                    if( strlen($pass) < 5 ) {
                     
                        $flagPasswordError = 'הסיסמה חייבת להיות מעל חמישה תווים';
                                
                    } else if (!preg_match('~[0-9]+~', $pass)) {
                        
                        $flagPasswordError = 'חייב לפחות מספר אחד בסיסמה';
                        
                    }  else if ( !preg_match("/[a-z]/i", $pass) && !preg_match("/\p{Hebrew}/u", $pass) ) {
                        
                        $flagPasswordError = 'חייב לפחות אות אחת';
                        
                    } else {
                        
                        $newData['passwordMd5'] = md5($pass);
                        
                    }
                }
                
                if( !$flagDuplicateUser && !$flagPasswordError ) {
                    
                    $this->db->where('id', $jsonPosts['userId']);
                    $update = $this->db->update('suppliers', $newData); 
                }
                

                if($update) {
                    
                   
                    
                    $output['newSession']['data'] = array(
                        'id' => $jsonPosts['userId'],
                        
                        'username' => $jsonPosts['username'],
                        'email' => $jsonPosts['email'],
                        'phone' => $jsonPosts['phone'],
                        'name' => $jsonPosts['name'],
                        
                        'userCredential' => 'admin',
                        'token' => JWT::encode($jsonPosts['userId'], $this->data['usersCode']),
                        'dateTimeLogin' => date("Y-m-d H:i:s")
                    );
                    
                    
                    $output['ok'] = 'הפרטים עודכנו בהצלחה';
                }              
                
                else if($flagDuplicateUser) {
                
                    $output['error'] = 'שם משתמש כבר קיים במערכת';
                }
                
                else if($flagPasswordError) {
                
                    $output['error'] = 'שגיאה: '.$flagPasswordError;
                }
                
                } else {

                    $output['error'] = 'שגיאה';

                }    
                    
        }
            
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    public function getClients($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','page','limit','search'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
            $rows = !empty($jsonPosts['limit']) ? (int)$jsonPosts['limit'] : 8;
            
            
            $this->db->select('id');
            $this->db->from('leadsLandpage');
            if(!empty($jsonPosts['search'])) {
                
                $this->db->like('firstName', $jsonPosts['search'], 'both');
                $this->db->or_like('lastName', $jsonPosts['search'], 'both');
                $this->db->or_like('phone', $jsonPosts['search'], 'both');
                $this->db->or_like('address', $jsonPosts['search'], 'both');
                $this->db->or_like('email', $jsonPosts['search'], 'both');
                
                $terms = explode(' ', $jsonPosts['search']); 
                
                if(isset($terms[1])) {
                    $this->db->like('firstName', $terms[0], 'both');
                    $this->db->or_like('lastName', $terms[1], 'both');
                }
                
            }
            
            $result1= $this->db->get();
            
            $totalPages = ceil($result1->num_rows() / $rows); //ONLY ROUND UP
        
            
            
            $this->db->select('id,created_at,user,firstName,lastName,phone,email,address,TotalPrice,Comments,status,OrderId');
            $this->db->from('leadsLandpage');
            
            if(!empty($jsonPosts['search'])) {
                
                

                $this->db->like('firstName', $jsonPosts['search'], 'both');
                $this->db->or_like('lastName', $jsonPosts['search'], 'both');
                $this->db->or_like('phone', $jsonPosts['search'], 'both');
                $this->db->or_like('address', $jsonPosts['search'], 'both');
                $this->db->or_like('email', $jsonPosts['search'], 'both');
                
                $terms = explode(' ', $jsonPosts['search']); 
                
                if(isset($terms[1])) {
                    $this->db->like('firstName', $terms[0], 'both');
                    $this->db->or_like('lastName', $terms[1], 'both');
                }
                
                
                
                
                
            }
            
            $output['search'] = $jsonPosts['search'];
            
            
            $data = array();
            
            $this->db->order_by('id', 'DESC');
            //$this->db->order_by('id', 'ASC');
            
            //$this->db->where('firstName', 'asdasdad');

            $page = !empty((int)$jsonPosts['page']) ? (int)$jsonPosts['page'] : 0;
            $page = $page > 0 ? $rows*$page : $page;
            
            
            $this->db->limit($rows,$page);
            
            $result= $this->db->get();
            $clientsDb = $result->result_array();
            
            
            
            if(!empty($clientsDb)) {
                
                
                
                foreach ($clientsDb as $client) {
                    
                    $totalPrice = $this->OtzarHaretz->getAllBuysClient($client);
                    
                    $data[] = array (
                            'id' => $client['id'],
                            'created_at' => changeDateFormat($client['created_at'], 'Y-m-d H:i:s', 'd.m.y H:i'),
                            'status' => $client['status'],
                            'name' => $client['firstName'].' '.$client['lastName'],
                            'firstName' => $client['firstName'],
                            'lastName' => $client['lastName'],
                            'phone' => str_replace("+972", "", $client['phone']),
                            'email' => $client['email'],
                            'address' => character_limiter($client['address'], 15,'...'),
                            'fullAddress' => $client['address'],
                            'TotalPrice' => $totalPrice,
                            'comments' => $client['Comments'],
                            'user' => $client['user']
                        );
                    
                }
                
            }
            
            $output['totalPages'] = $totalPages;
            $output['clients'] = $data;
            
            
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function usersCsv() {
        
        
        //let token = md5(user.id + user.phone);
        //let csvDownload = ConstantsNames.base_url + 'Suppliers/csv?token=' + token + '&id=' +  md5(user.id);
        
        $token = $this->input->get('token');
        $search = $this->input->get('search');
        
        $search = ($search == 'undefined') ? '' : $search;
        
        
        if( md5(md5($this->data['code'])) !== $token ) {die('ERROR');}
        
        
        $this->db->select('id,firstName,lastName,phone,email,address,TotalPrice,status,city,tz,Comments');
        $this->db->from('leadsLandpage');
            
        if(!empty($search)) {
            $this->db->like('firstName', $search, 'both');
            $this->db->or_like('lastName', $search, 'both');
            $this->db->or_like('phone', $search, 'both');
            $this->db->or_like('address', $search, 'both');
            $this->db->or_like('email', $search, 'both');
            
            $terms = explode(' ', $search); 
                
            if(isset($terms[1])) {
                $this->db->like('firstName', $terms[0], 'both');
                $this->db->or_like('lastName', $terms[1], 'both');
            }
            
        }

        $this->db->order_by('id', 'DESC');
        $result= $this->db->get();
        $clientsDb = $result->result_array();
            

        $data = array();

        if(!empty($clientsDb)) {

            foreach ($clientsDb as $client) {

                $data[] = array (
                        'id' => $client['id'],
                        'status' => $client['status'] == '1' ? 'פעיל' : 'לא פעיל',
                        'firstName' => $client['firstName'],
                        'lastName' => $client['lastName'],
                        'phone' => str_replace("+972", "", $client['phone']),
                        'email' => $client['email'],
                        'address' => $client['address'],
                        'TotalPrice' => $client['TotalPrice'],
                        'city' => $client['city'],
                        'tz' => $client['tz'],
                        'comments' => $client['Comments'],
                    );

            }

        }
        
        
        $dateFile = date('d_m_Y');  //date('m');
        $filename = 'users_'.$dateFile.'_'.rand(111,999);
        
        $csv = array();
        
        $csv[] = array (
            'id' => '#',
            'status' => 'סטטוס',
            'firstName' => 'שם פרטי',
            'lastName' => 'שם משפחה',
            'phone' => 'טלפון',
            'email' => 'מייל',
            'address' => 'כתובת',
            'TotalPrice' => 'סכום שובר',
            'city' => 'עיר',
            'tz' => 'ת.ז.',
            'Comments' => 'הערות'
        );
        
        $excellData = array_merge($csv,$data);
        
        
        
        header('Content-Encoding: UTF-8'); 
        header('Content-type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename='.$filename.'.csv');
        header("Pragma: no-cache");
        header("Expires: 0");

        $handle = fopen('php://output', 'w');
        fwrite($handle, "\xEF\xBB\xBF");
        

        foreach ($excellData as $data_array) {
            fputcsv($handle, $data_array);
        }
            fclose($handle);
        exit;
        
        
        
        
        //force_download($filename, $data);
    }
    
    
    
    public function onOffUser($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','clientId','currentStatus'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized' ) {
            
            $output['current'] = $jsonPosts['currentStatus'];
            
            if(!empty($jsonPosts['clientId'])) {
                
                $status = $jsonPosts['currentStatus'] == 'false' ? 0 : 1;
                
                $params = array(
                    'status' => $status
                );
                
                $this->db->where('id', $jsonPosts['clientId']);
                $this->db->update('leadsLandpage', $params); 
                
                $output['ok'] = '1';
                
            } else {
                $output['error'] = '1';
            }
        
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function cleanDBUsers ($param=FALSE) {
        
        $this->_loaderWS();

        die('STOPPED');
        
        $this->db->select('id,phone');
        $this->db->from('leadsLandpage');
        $this->db->where('id', '4506');
        $result= $this->db->get();
        $users = $result->result_array();
        
        $counter = 0;
        
        foreach ($users as $key => $value) {
            
            $phone = '';
            $phone = $this->OtzarHaretz->cleanPhone($value['phone']);
            
            if(!empty($phone) && strlen($phone)==10 ) {
                
                $params = array(
                    'phone' => $phone
                );

                $this->db->where('id', $value['id']);
                $this->db->update('leadsLandpage', $params); 
                
                $counter++;
                
            }
            
            
            
        }
        //4292
        
        echo "<pre>";
        //print_r($users);
        echo "COUNTER: ".$counter;
        
        //echo '<br/><br/>'.strlen($phone);
        
        die('<br/><br/>- END -');
        
    }
    
    
    
    
    public function testNode ($param = FALSE) {
          
        $output['hello'] = "sebas";
        $output['header'] = $this->input->request_headers();
        
        
        $this->data = $output;
         
        return $this->output
                //->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
}