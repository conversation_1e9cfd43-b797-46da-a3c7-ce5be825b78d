<?php

namespace OAuth2\Encryption;

use OAuth2\Storage\Bootstrap;

class JwtTest extends \PHPUnit_Framework_TestCase
{
    private $privateKey;

    public function setUp()
    {
        $this->privateKey = <<<EOD
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOD;
    }

    /** @dataProvider provideClientCredentials */
    public function testJwtUtil($client_id, $client_key)
    {
        $jwtUtil = new Jwt();

        $params = array(
            'iss' => $client_id,
            'exp' => time() + 1000,
            'iat' => time(),
            'sub' => '<EMAIL>',
            'aud' => 'http://myapp.com/oauth/auth',
            'scope' => null,
        );

        $encoded = $jwtUtil->encode($params, $this->privateKey, 'RS256');

        // test BC behaviour of trusting the algorithm in the header
        $payload = $jwtUtil->decode($encoded, $client_key);
        $this->assertEquals($params, $payload);

        // test BC behaviour of not verifying by passing false
        $payload = $jwtUtil->decode($encoded, $client_key, false);
        $this->assertEquals($params, $payload);

        // test the new restricted algorithms header
        $payload = $jwtUtil->decode($encoded, $client_key, array('RS256'));
        $this->assertEquals($params, $payload);
    }

    public function testInvalidJwt()
    {
        $jwtUtil = new Jwt();

        $this->assertFalse($jwtUtil->decode('goob'));
        $this->assertFalse($jwtUtil->decode('go.o.b'));
    }

    /** @dataProvider provideClientCredentials */
    public function testInvalidJwtHeader($client_id, $client_key)
    {
        $jwtUtil = new Jwt();

        $params = array(
            'iss' => $client_id,
            'exp' => time() + 1000,
            'iat' => time(),
            'sub' => '<EMAIL>',
            'aud' => 'http://myapp.com/oauth/auth',
            'scope' => null,
        );

        // testing for algorithm tampering when only RSA256 signing is allowed
        // @see https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/
        $tampered = $jwtUtil->encode($params, $client_key, 'HS256');

        $payload = $jwtUtil->decode($tampered, $client_key, array('RS256'));

        $this->assertFalse($payload);
    }

    public function provideClientCredentials()
    {
        $storage = Bootstrap::getInstance()->getMemoryStorage();
        $client_id  = 'Test Client ID';
        $client_key = $storage->getClientKey($client_id, "<EMAIL>");

        return array(
            array($client_id, $client_key),
        );
    }
}
