import React, { useState, useEffect, useRef } from "react";
import { Backdrop, CircularProgress } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";

/**
 * UploadFile - קומפוננט להעלאת קבצים למערכת
 *
 * מאפשר העלאת קבצים מסוגים שונים (תמונות ו-PDF), עם אפשרות לצפייה מקדימה
 * במקרים מסוימים (כמו אישור מחלה) דורש בחירת תאריכים לפני העלאת הקובץ
 * מונע העלאת קבצים כאשר סטטוס המסמך הוא "Exist"
 *
 * Props:
 * - formData: נתוני הטופס
 * - countDaysFirst: האם לדרוש בחירת תאריכים תחילה
 * - allowReupload: האם לאפשר העלאה מחדש
 * - refreshForms: פונקציה לרענון רשימת הטפסים אחרי העלאה/מחיקה
 */

import axios from "axios";
import { useDropzone } from "react-dropzone"; /* https://react-dropzone.js.org/ */
//import { toast } from 'react-toastify'
import { RestUrls } from "../../../Components/-Helpers-/config";

//import NoPic from './../../../img/sherut-leumi/pic.png';
import NoPic from "./../../../img/sherut-leumi/svg/files/pdfDOC.png?v=1";

import { Row, Col, ProgressBar, Modal, Button, Form } from "react-bootstrap";
import { toast } from "react-toastify";

import { checkDatesAPI, sendFileFormToClientApi } from "./fileFunctions";

import loader from "../../../img/preLoader.gif";

import fileBgQuad from "../../../img/sherut-leumi/svg/files/fileBgQuad.png";
import x from "../../../img/sherut-leumi/svg/files/x.svg";
import infoModal from "../../../img/sherut-leumi/svg/files/infoModal.svg";
import trash from "../../../img/sherut-leumi/svg/files/trash.svg";
import success from "../../../img/sherut-leumi/svg/files/success.svg";

import face from "../../../img/sherut-leumi/svg/files/formsPic/face.jpg";
import tz from "../../../img/sherut-leumi/svg/files/formsPic/tz.jpg";
import sign from "../../../img/sherut-leumi/svg/files/formsPic/sign.jpg";

import { Grid } from "@mui/material";

export default function UploadFile({
  formData,
  texts,
  userJ,
  allowReupload,
  closeSection,
  noHistoryForm,
  countDaysFirst,
  datesInput,
  datesOk,
  onCancel,
  onUploadSuccess,
  customTitle,
  processId,
  isReupload = false,
  refreshForms,
  autoOpenFilePicker = false,
  initialSelectedFile = null,
  onFilePickerOpened = () => {},
  ...props
}) {
  // Generate a local process ID if none was provided
  const localProcessId = processId || Date.now();

  console.log("UploadFile rendered with processId:", localProcessId, {
    formData,
    countDaysFirst,
    datesInput,
    datesOk,
    userDataValid:
      userJ && userJ.SessionKey && userJ.IDNO ? "Valid" : "Invalid",
    isReupload,
  });

  const {
    userJ: userJFromProps,
    formData: formDataFromProps,
    texts: textsFromProps,
    customTitle: customTitleFromProps,
    onCancel: onCancelFromProps,
    countDaysFirst: countDaysFirstFromProps,
  } = props;
  const fileInputRef = useRef(null);
  const displayTitle = customTitle || texts?.title;
  const didMountRef = useRef(false); // For tracking if component mounted
  const filePickerOpened = useRef(false); // Track if file picker has been opened

  const [picUrl, setPicUrl] = useState(false);
  const [isPicture, setIsPicture] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(null);

  const [showFirstModal, setShowFirstModal] = useState(false);
  const [showThanksModal, setShowThanksModal] = useState(false);

  const [finalSendFile, setFinalSendFile] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fileError, setFileError] = useState(null);
  const [uploadBtnEnabled, setUploadBtnEnabled] = useState(true);

  // Only for internal use when not provided via props
  const [internalDatesInput, setInternalDatesInput] = useState({
    StartDate: datesInput?.StartDate || "",
    EndDate: datesInput?.EndDate || "",
  });
  const [internalDatesOk, setInternalDatesOk] = useState(datesOk || false);

  // Use either the props or the internal state
  const effectiveDatesInput = datesInput || internalDatesInput;
  const effectiveDatesOk = datesOk || internalDatesOk;

  const [shouldOpenDatesModal, setShouldOpenDatesModal] = useState(false);

  const backgroundQuad = "url('" + fileBgQuad + "')";

  // CSS for pulse animation
  const pulseAnimation = `
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }
    `;

  // Log current state of dates and files
  useEffect(() => {
    if (localProcessId) {
      console.log("UploadFile state updated with processId:", localProcessId, {
        picUrl: picUrl ? "Image loaded" : "No image",
        isPicture,
        uploadProgress,
        showFirstModal,
        showThanksModal,
        finalSendFile: finalSendFile ? "File ready" : "No file",
        loading,
        shouldOpenDatesModal,
        effectiveDatesOk: effectiveDatesOk
          ? "Dates validated"
          : "Dates not validated",
        effectiveDatesInput,
      });
    }
  }, [
    localProcessId,
    picUrl,
    isPicture,
    uploadProgress,
    showFirstModal,
    showThanksModal,
    finalSendFile,
    loading,
    shouldOpenDatesModal,
    effectiveDatesOk,
    effectiveDatesInput,
  ]);

  const clearData = () => {
    console.log("clearData called with processId:", localProcessId);
    setPicUrl(false);
    setIsPicture(false);
    setLoading(false);
    setFinalSendFile(false);
    setInternalDatesOk(false);
    setInternalDatesInput({ StartDate: "", EndDate: "" });
    setShouldOpenDatesModal(false);
    setShowThanksModal(false);
    setUploadProgress(null);
    setFileError(null);
    setUploadBtnEnabled(true);

    // רענון הרשימה כשמנקים את הנתונים
    if (refreshForms) {
      console.log("Refreshing forms list after clearing data");
      refreshForms();
    }

    // If onCancel is provided, call it to return to the status view
    if (onCancel) {
      console.log("Calling onCancel from clearData");
      onCancel();
    }
  };

  const onDrop = (acceptedFiles) => {
    console.log("onDrop called with processId:", localProcessId, {
      acceptedFiles: acceptedFiles ? acceptedFiles.length : 0,
      countDaysFirst,
    });

    if (!acceptedFiles || acceptedFiles.length === 0) {
      return;
    }

    let file = acceptedFiles[0];
    let filename = (file?.name).toLowerCase();
    console.log("File dropped:", filename);

    // בדיקה חדשה: אם כבר יש לנו תאריכים (התאריכים נבחרו מראש),
    // שלח את הקובץ ישירות במקום לפתוח שוב את מודל התאריכים
    if (
      countDaysFirst &&
      effectiveDatesInput?.StartDate &&
      effectiveDatesOk?.ok
    ) {
      console.log("Dates already selected, proceeding with file upload");
      // שמור את הקובץ
      setFinalSendFile(file);
      // בדוק שם קובץ תקין
      if (
        !filename.includes(".pdf") &&
        !(
          filename.includes(".jpg") ||
          filename.includes(".jpeg") ||
          filename.includes(".png")
        )
      ) {
        toast.error("יש להעלות קבצים: " + texts?.filesType);
        clearData();
        return true;
      }

      // קבע אם זה תמונה או לא
      if (
        !filename.includes(".pdf") &&
        (filename.includes(".jpg") ||
          filename.includes(".jpeg") ||
          filename.includes(".png"))
      ) {
        setIsPicture(true);
      } else {
        setIsPicture(false);
      }

      // שמור את ה-URL של הקובץ לתצוגה מקדימה
      setPicUrl(URL.createObjectURL(file));

      // שלח את הקובץ ישירות עם הפרמטר file במקום להסתמך על finalSendFile
      uploadImage(file);
      sendMyFile(file);
      return;
    }

    // מקרה רגיל - אם אין תאריכים עדיין, נפתח את מודל התאריכים
    if (countDaysFirst) {
      console.log("countDaysFirst flow - setting shouldOpenDatesModal");
      setShowFirstModal(true);
      setShouldOpenDatesModal(true);
      // We'll save the file for later when the dates have been entered
      setFinalSendFile(file);
      return;
    }

    setPicUrl(URL.createObjectURL(file));
    setShowFirstModal(false);

    if (
      !filename.includes(".pdf") &&
      !(
        filename.includes(".jpg") ||
        filename.includes(".jpeg") ||
        filename.includes(".png")
      )
    ) {
      toast.error("יש להעלות קבצים: " + texts?.filesType);
      clearData();
      return true;
    }

    if (
      !filename.includes(".pdf") &&
      (filename.includes(".jpg") ||
        filename.includes(".jpeg") ||
        filename.includes(".png"))
    ) {
      setIsPicture(true);
    } else {
      setIsPicture(false);
    }

    uploadImage(file);
  };

  const { getRootProps, getInputProps, open } = useDropzone({
    noKeyboard: true,
    multiple: false,
    onDrop,
    noClick: false,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png"],
      "application/pdf": [".pdf"],
    },
  });

  // Use effect to open file picker after date selection
  useEffect(() => {
    // Only run if we're supposed to select dates first, dates are selected, and no file is picked yet
    // Don't open if we're showing thanks modal, loading, or after a successful upload
    // Also don't open if file picker has already been opened
    if (
      countDaysFirst &&
      datesOk?.ok &&
      !picUrl &&
      !showThanksModal &&
      !loading &&
      !finalSendFile &&
      !filePickerOpened.current
    ) {
      console.log(
        "Preparing to open file picker after date selection - processId:",
        localProcessId
      );
      // Add a small delay to allow the component to fully render
      const timer = setTimeout(() => {
        // Final check before opening
        if (
          !showThanksModal &&
          !loading &&
          !finalSendFile &&
          !filePickerOpened.current
        ) {
          console.log(
            "File picker dialog opening after date selection - processId:",
            localProcessId
          );
          // Try using our custom function that has fallbacks
          filePickerOpened.current = true;
          openFilePicker();
        } else {
          console.log(
            "Skipping file picker open - states changed - processId:",
            localProcessId
          );
        }
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [countDaysFirst, datesOk?.ok, picUrl, showThanksModal]);

  // Use effect to open file picker or modal on first mount
  useEffect(() => {
    // רק לוגים ואתחול, בלי פתיחה אוטומטית של המודל
    if (!didMountRef.current) {
      console.log(
        "Component first mount - processId:",
        localProcessId,
        "formData:",
        formData
      );
      didMountRef.current = true;

      // לא עושים שום דבר אוטומטי - המשתמש צריך ללחוץ על כפתור העלאת קובץ בעצמו
      // אין יותר פתיחה אוטומטית של מודלים
    }
  }, []);

  // Reset filePickerOpened when component unmounts or when thanksModal status changes
  useEffect(() => {
    if (showThanksModal) {
      // Reset the file picker flag when the thanks modal is shown
      // This allows a new file picker to be opened if the user closes the modal
      // and tries again without a full component unmount
      filePickerOpened.current = false;
    }

    return () => {
      console.log("UploadFile cleanup with processId:", localProcessId);
      filePickerOpened.current = false;
    };
  }, [showThanksModal, localProcessId]);

  // Consolidated file picker function with fallbacks
  const openFilePicker = () => {
    // גם אפשר ללחוץ על הכפתור וגם פותח אוטומטית
    console.log(
      "Attempting to open file picker dialog - processId:",
      localProcessId
    );

    // Check for conditions that would prevent opening
    if (showThanksModal || loading || finalSendFile || picUrl) {
      console.log(
        "Skipping file picker open due to state - processId:",
        localProcessId,
        {
          showThanksModal,
          loading,
          finalSendFile,
          picUrl,
        }
      );
      return;
    }

    try {
      // Method 1: Use direct file input reference
      if (fileInputRef.current) {
        console.log(
          "Using direct file input click - processId:",
          localProcessId
        );
        fileInputRef.current.click();
      }
      // Method 2: Use dropzone open method
      else if (open) {
        console.log("Using dropzone open method - processId:", localProcessId);
        open();
      }
      // Method 3: Create a temporary input element as fallback
      else {
        console.log(
          "Using fallback temporary input - processId:",
          localProcessId
        );
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "image/jpeg,image/png,application/pdf";
        input.onchange = (e) => {
          if (e.target.files && e.target.files.length > 0) {
            onDrop([e.target.files[0]]);
          }
        };
        input.click();
      }
    } catch (err) {
      console.error("Error opening file picker:", err);
    }
  };

  useEffect(() => {
    console.log("UploadFile mounted with processId:", localProcessId);
    return () => {
      console.log("UploadFile unmounted with processId:", localProcessId);
    };
  }, [localProcessId]);

  const uploadImage = (file) => {
    setFinalSendFile(file);
  };

  const sendMyFile = (directFile = null) => {
    console.log("sendMyFile called with processId:", localProcessId, {
      hasDirectFile: !!directFile,
      hasFinalSendFile: !!finalSendFile,
    });

    // Use the file upload handler with either the direct file or the one from state
    fileUploadHandler(directFile || finalSendFile);
  };

  // For uploading files via API
  const fileUploadHandler = (file) => {
    console.log("fileUploadHandler called with processId:", localProcessId, {
      countDaysFirst,
      hasDatesInput: effectiveDatesInput?.StartDate,
      hasFinalSendFile: !!finalSendFile,
    });

    // Prevent multiple uploads
    if (loading) {
      console.log("Upload already in progress, ignoring duplicate request");
      return;
    }

    // Validate required form data
    if (!formData || !formData.FormID) {
      console.error("Missing required form data", { formData });
      toast.error("שגיאה: מידע הטופס חסר, אנא רענן את הדף ונסה שוב");
      return;
    }

    // Validate user session data
    if (!userJ || !userJ.SessionKey || !userJ.IDNO) {
      console.error("Missing user session data", {
        userJ: userJ ? "Exists but incomplete" : "Missing",
      });
      toast.error("שגיאה: מידע המשתמש חסר, אנא התחבר מחדש ונסה שוב");
      return;
    }

    // Disable the upload button to prevent multiple clicks
    setUploadBtnEnabled(false);

    // Create the standard upload data object for all uploads
    const sendData = {
      file: finalSendFile || file,
      SessionKey: userJ.SessionKey,
      IDNumber: userJ.IDNO,
      formId: formData.FormID,
    };

    // Add date information if available
    if (countDaysFirst && effectiveDatesOk?.dates) {
      sendData.dates = effectiveDatesOk.dates;
    }

    // Always use the sendFileFormToClientApi function which handles the API correctly
    sendFileFormToClientApi(
      sendData,
      setLoading,
      clearData,
      setShowThanksModal,
      (data) => {
        // If we have a success callback from parent, call it
        if (onUploadSuccess) {
          console.log("Upload succeeded, calling parent onUploadSuccess");
          // Reset the file picker flag before calling onUploadSuccess
          filePickerOpened.current = false;
          onUploadSuccess(data);
        }

        // רענון הרשימה לאחר העלאת קובץ בהצלחה
        // זה קריטי במיוחד עבור אישור מחלה שצריך לראות סטטוס עדכני
        if (refreshForms) {
          console.log("Refreshing forms list after successful upload");
          setTimeout(() => {
            refreshForms();
          }, 1000); // מתן זמן לשרת לעדכן את הנתונים
        }
      }
    );
  };

  // שיפור של openMyModal כדי שתטפל בכל המקרים
  const openMyModal = () => {
    console.log(
      "openMyModal called - formData:",
      formData?.FormID,
      "Msg:",
      !!formData?.Msg,
      "countDaysFirst:",
      countDaysFirst
    );

    // תמיד פתח מודל אם יש הודעה או צורך בתאריכים
    if (formData?.Msg || countDaysFirst) {
      console.log("Showing modal - has message or needs dates");
      setShowFirstModal(true);

      // אם צריך תאריכים, הגדר זאת
      if (countDaysFirst) {
        setShouldOpenDatesModal(true);
      }

      return;
    }

    // אין הודעה ואין צורך בתאריכים - פתח ישירות בוחר קבצים
    console.log("No message and no date needed - opening file picker directly");
    openFilePicker();
  };

  const checkDates = () => {
    console.log(
      "checkDates in UploadFile called with processId:",
      localProcessId,
      {
        effectiveDatesInput,
      }
    );
    checkDatesAPI(
      effectiveDatesInput,
      (result) => {
        console.log("checkDatesAPI result in UploadFile:", result);
        setInternalDatesOk(result);
      },
      (loadingState) => {
        console.log("Loading state in UploadFile changed to:", loadingState);
        setLoading(loadingState);
      }
    );
  };

  // עידכון ופישוט של handleOpenFilePicker
  const handleOpenFilePicker = (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // אם צריך להציג מודל קודם ועוד לא מוצג - צריך לעצור ולהציג מודל
    if (!showFirstModal && (formData?.Msg || countDaysFirst)) {
      console.log("Need to show modal first before file picker");
      openMyModal(); // openMyModal יטפל בכל המקרים
      return;
    }

    // לא לפתוח אם כבר העלינו קובץ או בתהליך טעינה
    if (finalSendFile || loading || showThanksModal || picUrl) {
      console.log("Not opening file picker because: ", {
        hasFinalFile: !!finalSendFile,
        loading,
        showThanksModal,
        hasPicUrl: !!picUrl,
      });
      return;
    }

    try {
      console.log("Opening file picker dialog");
      // Method 1: Use direct file input reference
      if (fileInputRef.current) {
        console.log("Using direct file input click");
        fileInputRef.current.click();
      }
      // Method 2: Use dropzone open method
      else if (open) {
        console.log("Using dropzone open method");
        open();
      }
      // Method 3: Create a temporary input element as fallback
      else {
        console.log("Using fallback temporary input");
        const input = document.createElement("input");
        input.type = "file";
        input.accept = "image/jpeg,image/png,application/pdf";
        input.onchange = (e) => {
          if (e.target.files && e.target.files.length > 0) {
            onDrop([e.target.files[0]]);
          }
        };
        input.click();
      }
    } catch (err) {
      console.error("Error opening file picker:", err);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log(
      "UploadFile handleSubmit called with processId:",
      localProcessId
    );
    sendMyFile();
  };

  const handleCancel = () => {
    console.log("UploadFile cancel called with processId:", localProcessId);
    if (onCancel) {
      onCancel();
    }
  };

  // Handle closing the thanks modal
  const handleCloseModal = () => {
    console.log(
      "Closing thanks modal and clearing data - processId:",
      localProcessId
    );
    // First hide the modal
    setShowThanksModal(false);

    // Reset the file picker flag
    filePickerOpened.current = false;

    // רענון הרשימה בכל מקרה של סגירת מודל תודה
    if (refreshForms) {
      console.log("Refreshing forms list after closing thanks modal");
      refreshForms();
    }

    // Call onUploadSuccess callback if provided
    if (onUploadSuccess) {
      console.log(
        "Calling onUploadSuccess callback - processId:",
        localProcessId
      );
      // Add a short delay to ensure the modal is hidden first
      setTimeout(() => {
        onUploadSuccess();
      }, 100);
    } else {
      // Then clear all data and return to status view
      clearData();
    }
  };

  // Check if upload should be disabled based on document status
  const isUploadDisabled = (formId, status) => {
    // רק ת"ז חסומה להעלאה במצב Exist
    return formId === 1 && status === "Exist";
  };

  const handleUpload = (formId, status) => {
    if (isUploadDisabled(formId, status)) {
      return;
    }

    if (formId === 10) {
      // אישור מחלה
      // תמיד פותח את הדיאלוג של אישור מחלה
      setShowFirstModal(true);
      setShouldOpenDatesModal(true);
    } else {
      // לוגיקה רגילה להעלאת קבצים
      openFilePicker();
    }
  };

  // השתמש ב-useEffect כדי לפתוח אוטומטית את בוחר הקבצים כשהקומפוננט מוצג והדגל מופעל
  useEffect(() => {
    if (autoOpenFilePicker && fileInputRef.current) {
      // פתח את בוחר הקבצים באופן מושהה קלות
      const timer = setTimeout(() => {
        fileInputRef.current.click();
        // הודע לקומפוננט האב שבוחר הקבצים נפתח
        onFilePickerOpened();
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [autoOpenFilePicker, onFilePickerOpened]);

  const [selectedFile, setSelectedFile] = useState(initialSelectedFile);
  const [isUploading, setIsUploading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(
    initialSelectedFile !== null
  );

  // טיפול בבחירת קובץ
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      // הצג את מסך האישור אחרי בחירת קובץ
      setShowConfirmation(true);
    }
  };

  // טיפול בשליחת הקובץ - חזרה לפונקציה המקורית
  const handleSubmitFile = async () => {
    if (!selectedFile) return;

    setIsUploading(true);

    // שימוש בפונקציות המקוריות
    setFinalSendFile(selectedFile);
    uploadImage(selectedFile);
    sendMyFile(selectedFile);
  };

  // אם אין קובץ נבחר, הצג את מסך בחירת הקובץ
  if (!showConfirmation) {
    return (
      <div className="p-4">
        <h3 className="text-lg font-medium mb-4">בחירת קובץ</h3>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".jpg,.jpeg,.png,.pdf"
          style={{ display: "none" }}
        />

        <div className="flex flex-col items-center space-y-3">
          <Button
            onClick={() => fileInputRef.current?.click()}
            variant="default"
            className="w-full"
          >
            בחר קובץ
            <CloudUploadIcon className="mr-2 h-4 w-4" />
          </Button>

          <Button onClick={onCancel} variant="outline" className="w-full">
            ביטול
          </Button>
        </div>
      </div>
    );
  }

  // אם יש קובץ נבחר, הצג את מסך האישור
  return (
    <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg shadow-sm">
      <h3 className="text-lg font-medium mb-4 text-blue-800 flex items-center">
        אישור העלאת קובץ
        <CheckCircleOutlineIcon className="mr-2 h-5 w-5 text-blue-600" />
      </h3>

      <div className="mb-6 p-4 bg-white rounded-lg border border-blue-200 shadow-sm">
        <p className="text-sm font-medium text-gray-600 mb-1">הקובץ שנבחר:</p>
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-full mr-3">
            {selectedFile.name.endsWith(".pdf") ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-700"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-blue-700"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            )}
          </div>
          <p className="text-base font-medium text-gray-800">
            {selectedFile.name}
          </p>
        </div>
      </div>

      {isUploading ? (
        <div className="flex flex-col items-center justify-center p-6 bg-white rounded-lg border border-blue-100 shadow-sm">
          <CircularProgress
            size={40}
            thickness={4}
            style={{ color: "#3B82F6" }}
          />
          <p className="mt-4 text-sm text-gray-600 font-medium">
            מעלה את הקובץ, אנא המתן...
          </p>
          <p className="text-xs text-gray-500 mt-2">הקובץ נשלח לשרת לאישור</p>
        </div>
      ) : (
        <div className="space-y-3">
          <Button
            onClick={handleSubmitFile}
            variant="default"
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2.5 transition-colors duration-200"
          >
            <span className="font-medium text-blue-600">שלח לאישור</span>
            <CheckCircleOutlineIcon className="mr-2 h-5 w-5 text-blue-600" />
          </Button>

          <div className="grid grid-cols-2 gap-3">
            <Button
              onClick={() => {
                setSelectedFile(null);
                setShowConfirmation(false);
              }}
              variant="outline"
              className="border-blue-300 text-blue-700 hover:bg-blue-50"
            >
              בחר קובץ אחר
            </Button>

            <Button
              onClick={onCancel}
              variant="ghost"
              className="text-gray-600 hover:bg-gray-100"
            >
              ביטול
            </Button>
          </div>

          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-xs text-green-700 text-center">
              לחץ על "שלח לאישור" כדי להמשיך בתהליך
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
