<div class="jobForm"> 
    <div class="form-wrapper">
        <?php if(!$this->input->get('thank')): ?>
        <?php echo form_open(base_url('welcome/sendJobMail'.getQS()), array("enctype" => "multipart/form-data", "class" => "", "onsubmit" => "return validate_form(this);")); ?>
        <input id="input1" type="text" class="iname" value="<?php echo set_value('name'); ?>"  name="name" aria-label="שם מלא " placeholder="שם מלא " data-required="נא להזין שם מלא / שם חברה">
        <input id="input2" style="margin-left: 0" type="tel" class="iname"  value="<?php echo set_value('phone'); ?>" name="phone" aria-label="מספר טלפון" placeholder="טלפון" data-required="נא להזין טלפון תקין">
            <input id="input3" type="email" class="iname" value="<?php echo set_value('email'); ?>" name="email" aria-label="דואר אלקטרוני" placeholder="דואר אלקטרוני" data-required="נא להזין מייל תקין">       


            <input type="hidden" name="jobId" value="<?php echo $this->input->get('jobId') ?>">


            <input id="fileupload" type="file" value="" class="hidden" name="userfile" data-required="נא לבחור קובץ קורות החיים" onchange="inputChange(this, '#fileuploadname');">
            <!--from alexanderg-->
            <button role="button" aria-label="צרף קובץ קו״ח" type="button" class="ifile" onclick="performClick('fileupload');">
                <span>צרף קו״ח</span>
            </button>

            <button class="send_btn" type="submit">שלח</button>
            <div style="clear: both"></div>
            <span id="fileuploadname"></span>
            <input type="hidden" name="kind" value="דרושים">

        <?php echo form_close(); ?>
        <?php else: ?>
            <h3 style="" >הפנייה התקבלה.</h3>
        <?php endif; ?>
    </div>
</div>