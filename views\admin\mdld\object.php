<?php
//echo '<pre>';
//print_r($migration);
//echo '</pre>';

function cmp($a, $b) {
    return ($a['sort'] > $b['sort']) ? -1 : 1;
}

if(isset($migration['fields']) && !empty($migration['fields'])) {
    usort($migration['fields'], "cmp");
}
?>

<div class="row">
    <div class="col-sm-12">
        <h4>
            <?php if(isset($object) && $object) { ?>
            עריכת <?php echo $object->Arg($migration['title']); ?>
            <?php } else { ?>
            הוספת <?php echo $migration['explain']; ?>
            <?php } ?>
        </h4>
    </div>
    <div class="col-sm-12">
        <?php $action = isset($object) && $object ? 'admin/object/' . $migration['table'] . '/update/' . $object->Id() . '/' : 'admin/object/' . $migration['table'] . '/put/'; ?>
        <?php echo form_open_multipart($action . getQS(), array("class" => "ajax")); ?>
        <div class="row">
            <div class="col-sm-12">
                <div class="row">
                <?php foreach($migration['fields'] as $fkey => $field) { ?>
                    <div class="<?php echo $field['width']; ?>">
                        <?php if(IS_DEVELOPER) { ?>
                        <small><?php echo $field['name']; ?></small>
                        <?php } ?>
                        <?php if($field['type'] === 'short') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'long') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="form-control" name="<?php echo $field['name']; ?>"><?php echo isset($object) ? $object->Arg($field['name']) : ""; ?></textarea>

                        </div>
                        <?php } else if($field['type'] === 'html') { ?>
                        <div class="form-group ck">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <textarea id="input_object_<?php echo $fkey; ?>" type="text" class="form-control ckeditor" name="<?php echo $field['name']; ?>"><?php echo isset($object) ? $object->Arg($field['name']) : ""; ?></textarea>

                        </div>
                        <?php } else if($field['type'] === 'integer') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="number" step="1" class="form-control" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'double') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="number" step="0.00000001" class="form-control" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'datetime') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control datetime" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'date') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control date" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'time') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="text" class="form-control time" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'image') { ?>
                        <div class="form-group cropper">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="<?php echo $field['name']; ?>" accept="image/*" onchange="PreviewImage(this, 'preview<?php echo $fkey; ?>');">
                            <div dir="ltr">
                                <?php
                                $extra = $field['options'];
                                $width = isset($extra['width']) ? $extra['width'] : 0;
                                $height = isset($extra['height']) ? $extra['height'] : 0;
                                ?>
                                <input type="hidden" name="<?php echo $field['name']; ?>_crop" value="<?php echo $width > 0 ? TRUE : FALSE; ?>"/>
                                <img id="preview<?php echo $fkey; ?>" class="materialboxed" src="<?php echo isset($object) ? $object->Img($field['name']) : ""; ?>" alt="preview" style="max-width: 100%;" data-width="<?php echo $width; ?>" data-height="<?php echo $height; ?>" data-input-name="<?php echo $field['name']; ?>"/>
                            </div>
                        </div>
                        <?php } else if($field['type'] === 'file') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="file" class="form-control" name="<?php echo $field['name']; ?>">

                        </div>
                        <?php if(isset($object) && $object->File($field['name'])) { ?>
                        <div class="form-group">
                            <a href="<?php echo $object->File($field['name']); ?>" target="_blank">צפה בקובץ  -<?php echo $object->Arg($field['name']); ?></a>
                        </div>
                        <?php } ?>
                        <?php } else if($field['type'] === 'email') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="email" dir="ltr" class="form-control" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'color') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="text" dir="ltr" class="form-control colorpicker" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'tel') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="tel" dir="ltr" class="form-control" name="<?php echo $field['name']; ?>" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <?php } else if($field['type'] === 'video') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <input id="input_object_<?php echo $fkey; ?>" type="text" dir="ltr" class="form-control" name="<?php echo $field['name']; ?>" onchange="PreviewVideo(this, 'videoPreview<?php echo $fkey; ?>');" value="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>">

                        </div>
                        <div class="form-group">
                            <div class="embed-responsive embed-responsive-16by9">
                                <iframe id="videoPreview<?php echo $fkey; ?>" class="embed-responsive-item" src="<?php echo isset($object) ? $object->Arg($field['name']) : ""; ?>"></iframe>
                            </div>
                        </div>
                        <?php } else if($field['type'] === 'boolean') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <div id="input_object_<?php echo $fkey; ?>" class="checkbox">
                                <label>

                                    <input type="checkbox" name="<?php echo $field['name']; ?>" value="true" <?php echo isset($object) && $object->Arg($field['name']) > 0 ? "checked" : ""; ?>>
                                    <?php echo isset($field['options']['no']) ? $field['options']['no'] : ''; ?> / <?php echo isset($field['options']['yes']) ? $field['options']['yes'] : ''; ?>
                                </label>
                            </div>
                        </div>
                        <?php } else if($field['type'] === 'choice') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <select id="input_object_<?php echo $fkey; ?>" name="<?php echo $field['name']; ?>" class="form-control">
                                <option value="" disabled selected>בחר <?php echo $field['explain']; ?></option>
                                <?php $choices = isset($field['options']['choices']) ? explode(",", $field['options']['choices']) : array(); ?>
                                <?php foreach($choices as $c) { ?>
                                <option value="<?php echo $c; ?>" <?php echo isset($object) && $object->Arg($field['name']) === $c ? "selected" : ""; ?>><?php echo $c; ?></option>
                                <?php } ?>
                            </select>

                        </div>
                        <?php } else if($field['type'] === 'multiple') { ?>
                        <div class="form-group-no">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <div id="input_object_<?php echo $fkey; ?>" class="">
                                <?php $multiples = isset($field['options']['multiples']) ? explode(",", $field['options']['multiples']) : array(); ?>
                                <?php foreach($multiples as $mkey => $m) { ?>
                                <p>
                                    <input type="checkbox" id="input_object_<?php echo $fkey; ?>_tag<?php echo $mkey; ?>" name="<?php echo $field['name']; ?>[]" value="<?php echo $m; ?>" <?php echo isset($object) && strpos( $object->Arg($field['name']), $m ) !== false ? "checked" : ""; ?> />
                                    <label for="input_object_<?php echo $fkey; ?>_tag<?php echo $mkey; ?>"><?php echo $m; ?></label>
                                </p>
                                <?php } ?>
                            </div>

                        </div>
                        <?php } else if($field['type'] === 'table') { ?>
                        <div class="form-group">
                            <label for="input_object_<?php echo $fkey; ?>"><?php echo $field['explain']; ?></label>
                            <select id="input_object_<?php echo $fkey; ?>" type="text" class="form-control autocomplete" name="<?php echo $field['name']; ?>" data-autocomplete-url="<?php echo base_url('admin/object/' . $migration['table'] . '/search_in_objects?table='.$field['options']['table'].'&field='.$field['options']['field_text']); ?>" data-autocomplete-value="<?php echo $field['options']['field_value']; ?>">
                                <?php foreach($options[$field['name']] as $tr) { ?>
                                <option value="<?php echo $tr->Arg($field['options']['field_value']); ?>" <?php echo (isset($object) && $object->Arg($field['name']) === $tr->Arg($field['options']['field_value'])) || (isset($parent_selected[$field['name']]) && $parent_selected[$field['name']] === $tr->Arg($field['options']['field_value'])) ? "selected" : ""; ?>><?php echo $tr->Arg($field['options']['field_text']); ?></option>
                                <?php } ?>
                            </select>

                        </div>
                        <?php } ?>
                    </div>
                <?php } ?>
                </div>
            </div>
            <div class="col-sm-12">
                <a href="<?php echo base_url('admin/object/index/' . $migration['table'] . '/' . getQS()); ?>" class="btn btn-default">בטל</a>
                <button type="submit" class="btn <?php echo isset($object) && $object ? "btn-success" : "btn-primary"; ?>"><?php echo isset($object) && $object ? "עדכן" : "הוסף"; ?></button>
            </div>
        </div>
        <?php echo form_close(); ?>


    </div>
</div>


