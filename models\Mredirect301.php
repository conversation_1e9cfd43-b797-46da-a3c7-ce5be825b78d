<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Mredirect301 extends CI_Model {
    
    const TABLE = 'redirect301';
    
    public function __construct() {
        parent::__construct();
        
    }
    
    public function init() {
        $query = "
            CREATE TABLE IF NOT EXISTS `".self::TABLE."` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `from` text NOT NULL,
                `to` text NOT NULL,
                `created_at` datetime NOT NULL,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `status` tinyint(1) NOT NULL DEFAULT '0',
                `sort` int(4) NOT NULL DEFAULT '0',
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
            ";
        
        $this->db->query($query);
        
    }
    
    public function get_settings_data() {
        $this->db->select('*');
        $this->db->from(self::TABLE);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row){
                $data[$row['idx']] = htmlspecialchars(stripcslashes($row['val']));
            }
            return $data;
        }
        return array();
    }
    
    public function search($q = "") {
        $this->db->group_start();
        $this->db->or_like(self::TABLE . '.from', $q, 'both');
        $this->db->or_like(self::TABLE . '.to', $q, 'both');
        $this->db->group_end();
    }
    
    public function limit($page = 1, $limit = 100) {
        $this->db->limit($limit, $limit * ($page - 1));
    }
    
    public function sort($order = "id", $sort = "DESC") {
        $this->db->order_by($order, $sort);
    }
    
    public function count_all() {
        $this->db->select('*');
        $this->db->from(self::TABLE);
        return $this->db->count_all_results();
    }
    
    public function select_all() {
        $this->db->select('*');
        $this->db->from(self::TABLE);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $data = array();
            foreach($result->result_array() as $row){
                $data[] = $row;
            }
            return $data;
        }
        return array();
    }
    
    public function select_id($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE);
        $this->db->where('id', $id);
        $result = $this->db->get();
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            return $row;
        }
        return FALSE;
    }
    
    public function insert($params) {
        $this->db->insert(self::TABLE, $params); 
        return $this->db->insert_id();
    }
    
    public function update($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE);
        return $this->db->affected_rows();
    }
    
    public function status($id) {
        $obj = $this->select_id($id);
        if($obj) {
            $data = array(
                'status' => $obj['status'] > 0 ? 0 : 1
            );
            $this->db->where('id', $id);
            $this->db->update(self::TABLE, $data); 
            return $this->db->affected_rows();
        }
        return FALSE;
    }
    
}