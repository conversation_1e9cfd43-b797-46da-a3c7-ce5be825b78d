<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| Hooks
| -------------------------------------------------------------------------
| This file lets you define "hooks" to extend CI without hacking the core
| files.  Please see the user guide for info:
|
|	http://codeigniter.com/user_guide/general/hooks.html
|
*/

require_once (APPPATH."config/database.php");

$hook['pre_system'][] = array(
    'class'    => 'Router_Hook',
    'function' => 'redirect301',
    'filename' => 'Routerhook.php',
    'filepath' => 'hooks',
    'params'   => array('db' => $db['default'])
);

$hook['pre_system'][] = array(
    'class'    => 'Router_Hook',
    'function' => 'dynamic_route',
    'filename' => 'Routerhook.php',
    'filepath' => 'hooks',
    'params'   => array('db' => $db['default'])
);


//$hook['display_override'] = array(
//    'class' => 'Minifyhtml',
//    'function' => 'output',
//    'filename' => 'Minifyhtml.php',
//    'filepath' => 'hooks',
//    'params' => array()
//);

/* End of file hooks.php */
/* Location: ./application/config/hooks.php */