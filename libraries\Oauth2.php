<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

require_once( APPPATH . 'third_party/oauth2-server-php/src/OAuth2/Autoloader.php');


class Oauth2 { 
    public $CI;
    
    private $server;
    private $storage;


    public function __construct() {
        
        $this->CI = & get_instance();
         
        $dsn      = 'mysql:dbname=' . $this->CI->db->database . ';host=' . $this->CI->db->hostname;
        $username = $this->CI->db->username;
        $password = $this->CI->db->password;
        
        OAuth2\Autoloader::register();
        
        // $dsn is the Data Source Name for your database, for exmaple "mysql:dbname=my_oauth2_db;host=localhost"
        $this->storage = new OAuth2\Storage\Pdo(array('dsn' => $dsn, 'username' => $username, 'password' => $password));

        // Pass a storage object or array of storage objects to the OAuth2 server class
        $this->server = new OAuth2\Server($this->storage);

        // Add the "Client Credentials" grant type (it is the simplest of the grant types)
        $this->server->addGrantType(new OAuth2\GrantType\ClientCredentials($this->storage));

        // Add the "Authorization Code" grant type (this is where the oauth magic happens)
        $this->server->addGrantType(new OAuth2\GrantType\AuthorizationCode($this->storage));
        
        $this->server->addGrantType(new OAuth2\GrantType\UserCredentials($this->storage));

        $this->server->addGrantType(new OAuth2\GrantType\RefreshToken($this->storage));

    }
    
    public function handleTokenRequest() {
        // Handle a request for an OAuth2.0 Access Token and send the response to the client
        $this->server->handleTokenRequest(OAuth2\Request::createFromGlobals())->send();
    }
    
    public function verifyResourceRequest() {
        // Handle a request to a resource and authenticate the access token
        if (!$this->server->verifyResourceRequest(OAuth2\Request::createFromGlobals())) {
            return $this->server->getResponse()->send();
            die;
        }
        return true;
        echo json_encode(array('success' => true, 'message' => 'You accessed my APIs!'));
    }
    
    public function create_client($client_id, $client_secret, $redirect_uri) {
        return $this->storage->setClientDetails($client_id, $client_secret, $redirect_uri);
    }
    
    public function create_user($username, $password, $firstname = null, $lastname = null) {
        return $this->storage->setUser($username, $password, $firstname, $lastname);
    }
}