import React, { useEffect } from "react";
import HomePage from "../../Pages/HomePage";
import RegisterIndex from "../../Pages/Register/RegisterIndex";
import UserConsoleSearchIndex from "../../Pages/UserConsole/UserConsoleSearchIndex";
import UserConsoleDataIndex from "../../Pages/UserConsole/UserConsoleDataIndex";
import SherutPlacesIndex from "../../Pages/MekomotSherut/SherutPlacesIndex";
import FilesPage from "../../Pages/UserConsole/files/FilesPage";
import ClockInOutIndex from "../../Pages/clockInOut/ClockInOutIndex";
import TrainingIndex from "../../Pages/training/TrainingIndex";
import SortiesIndex from "../../Pages/sorties/SortiesIndex";
import ContactUsIndex from "../../Pages/contactUs/ContactUsIndex";
import AvatarEditPage from "../../Pages/UserConsole/closeApp/AvatarEditPage";
import { useAuth } from "../../context/AuthContext";

const ContentWrapper = ({ page, info, changeMenuright, children }) => {
  console.log("ContentWrapper rendered with page:", page);
  const { user } = useAuth();

  useEffect(() => {
    // Enhanced WebView detection
    const isWebView = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const hasReactNativeWebView = typeof window.ReactNativeWebView === 'object' && window.ReactNativeWebView !== null;
      const isWebkit = userAgent.includes('webkit');
      const isWKWebView = userAgent.includes('wkwebview');
      const isAndroidWebView = userAgent.includes('wv');
      
      // Log detection details for debugging
      console.log('WebView Detection:', {
        hasReactNativeWebView,
        userAgent,
        isWebkit,
        isWKWebView,
        isAndroidWebView,
        isWebView: hasReactNativeWebView || isWKWebView || isAndroidWebView
      });

      return hasReactNativeWebView || isWKWebView || isAndroidWebView;
    };

    const isReactNative = isWebView();
    const isLoggedIn = user && Object.keys(user).length > 0 && !["login", "register", "reSendPass", "reSendPassNew"].includes(page);

    // Handle camera permissions only in React Native environment
    let handleCameraPermissions;
    if (isReactNative) {
      handleCameraPermissions = () => {
        navigator.mediaDevices.getUserMedia({ video: true })
          .then(stream => {
            // Camera access granted
            stream.getTracks().forEach(track => track.stop()); // Clean up the stream
          })
          .catch(error => {
            console.error("Camera access error:", error);
          });
      };

      window.addEventListener("message", handleCameraPermissions);
    }

    // Send app state message with enhanced device info
    const message = JSON.stringify({
      isLoggedIn: isLoggedIn !== null ? isLoggedIn.toString() : "false",
      Category: user?.Category || "2",
      InService: user?.InService || "null",
      deviceInfo: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        isWebView: isReactNative,
        webViewType: isReactNative ? 'ReactNativeWebView' : 'browser'
      }
    });

    // Enhanced postMessage handling with error catching
    try {
      if (window.ReactNativeWebView && typeof window.ReactNativeWebView.postMessage === 'function') {
        window.ReactNativeWebView.postMessage(message);
        console.log('Message sent to ReactNativeWebView:', message);
      } else {
        window.postMessage(message, '*');
        console.log('Message sent to window:', message);
      }
    } catch (error) {
      console.error('Error sending postMessage:', error);
    }

    // Cleanup
    return () => {
      if (isReactNative && handleCameraPermissions) {
        window.removeEventListener("message", handleCameraPermissions);
      }
    };
  }, [page]);

  const renderContent = () => {
    const isDevUser = true; // This should be determined dynamically based on your app's logic

    console.log("renderContent called with page:", page);

    switch (page) {
      case "index":
        return <HomePage siteInfo={{ info }} />;
      case "register":
        console.log("Attempting to render RegisterIndex with register page");
        return <RegisterIndex siteInfo={{ info }} page={page} />;
      case "login":
      case "loginQueryUrl":
      case "reSendPass":
      case "reSendPassNew":
        return <RegisterIndex siteInfo={{ info }} page={page} />;
      case "userIndex":
        return <UserConsoleSearchIndex siteInfo={{ info }} changeMenuright={changeMenuright} />;
      case "userSearch":
        return <UserConsoleSearchIndex siteInfo={{ info }} />;
      case "userData":
        return <UserConsoleDataIndex siteInfo={{ info }} />;
      case "files":
        return <FilesPage siteInfo={{ info }} />;
      case "clockInOutIndex":
        return isDevUser ? <ClockInOutIndex siteInfo={{ info }} /> : <h2 style={{ color: "red" }}>שגיאה</h2>;
      case "training":
        return isDevUser ? <TrainingIndex siteInfo={{ info }} /> : <h2 style={{ color: "red" }}>שגיאה</h2>;
      case "sorties":
        return <SortiesIndex siteInfo={{ info }} />;
      case "contactUs":
        return <ContactUsIndex siteInfo={{ info }} />;
      case "editAvatar":
        return <AvatarEditPage siteInfo={{ info }} />;
      case "sherutPlacesIndex":
        return <SherutPlacesIndex siteInfo={{ info }} />;
      case "digitalCard":
        return children || <div>כרטיס דיגיטלי</div>;
      default:
        console.log("No matching page found for:", page);
        return <div>NO PAGE</div>;
    }
  };

  const content = renderContent();
  console.log("ContentWrapper rendering completed, returning content for page:", page);
  return <>{content}</>;
};

export default ContentWrapper;
