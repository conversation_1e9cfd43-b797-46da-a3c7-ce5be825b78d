<!DOCTYPE html>
<html lang="he" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-W6KMLZ7J");
    </script>
    <!-- End Google Tag Manager -->
    <link rel="icon" href="%PUBLIC_URL%/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="מערכת האגודה להתנדבות - פלטפורמה לניהול והרשמה להתנדבויות"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>האגודה להתנדבות - מערכת ניהול התנדבויות</title>

    <link rel="canonical" href="https://sherut-leumi.wdev.co.il" />
    <meta
      property="og:image"
      content="https://sherut-leumi.wdev.co.il/api/assets/img/default/face.png"
    />
    <meta
      property="og:title"
      content="האגודה להתנדבות - מערכת ניהול התנדבויות"
    />
    <meta
      property="og:description"
      content="מערכת האגודה להתנדבות - פלטפורמה לניהול והרשמה להתנדבויות"
    />

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=UA-100997025-38"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "UA-100997025-38");
    </script>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <!-- Google tag (gtag.js) -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-MBKBWV14F0"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-MBKBWV14F0");
    </script>
    <link
      href="https://cdn.jsdelivr.net/npm/daisyui@5/themes.css"
      rel="stylesheet"
      type="text/css"
    />
    <!-- Facebook Pixel Code -->
    <script>
      !(function (f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod
            ? n.callMethod.apply(n, arguments)
            : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = "2.0";
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(
        window,
        document,
        "script",
        "https://connect.facebook.net/en_US/fbevents.js"
      );
      fbq("init", "173017623235330");
      fbq("track", "PageView");
    </script>
    <noscript
      ><img
        height="1"
        width="1"
        style="display: none"
        src="https://www.facebook.com/tr?id=173017623235330&ev=PageView&noscript=1"
        alt="Facebook Pixel"
    /></noscript>
    <!-- End Facebook Pixel Code -->

    <!-- Meta Pixel Code -->
    <script>
      !(function (f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod
            ? n.callMethod.apply(n, arguments)
            : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = "2.0";
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(
        window,
        document,
        "script",
        "https://connect.facebook.net/en_US/fbevents.js"
      );
      fbq("init", "2130580417374325");
      fbq("track", "PageView");
    </script>
    <noscript
      ><img
        height="1"
        width="1"
        style="display: none"
        src="https://www.facebook.com/tr?id=2130580417374325&ev=PageView&noscript=1"
        alt="Facebook Pixel"
    /></noscript>
    <!-- End Meta Pixel Code -->
  </head>

  <body dir="rtl"> 
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-W6KMLZ7J"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <noscript>יש להפעיל JavaScript כדי להריץ את האפליקציה הזו.</noscript>
    <div id="root" role="main"></div>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const handleScroll = () => {
          const scrollThreshold = 100;
          const body = document.body;
          if (window.scrollY > scrollThreshold) {
            body.classList.add("header-scroll");
            document
              .querySelector("header")
              ?.setAttribute("aria-expanded", "true");
          } else {
            body.classList.remove("header-scroll");
            document
              .querySelector("header")
              ?.setAttribute("aria-expanded", "false");
          }
        };

        window.addEventListener("scroll", handleScroll);

        // Clean up event listener on page unload
        window.addEventListener("unload", () => {
          window.removeEventListener("scroll", handleScroll);
        });

        // Add skip to main content link
        const skipLink = document.createElement("a");
        skipLink.href = "#root";
        skipLink.textContent = "";
        skipLink.className = "skip-link";
        document.body.insertBefore(skipLink, document.body.firstChild);
      });
    </script>
  </body>
</html>
