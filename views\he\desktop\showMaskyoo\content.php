<?php 

    //print_r($settings);
    //echo $params['project']->Arg('lead_emails');
    
    
?>



<?php
        $lead_emails = "";
        $msq_api_key = '13591';
        $msq_default_tel = '0779966988';
        if(isset($page)) {
            //switch($params['project']->Arg('name')) {
                //case 'צמרת אביסרור בנווה זאב': $msq_api_key = '13596';$msq_default_tel = '0772315696'; break;
                //case 'רמת אביסרור': $msq_api_key = '13596';$msq_default_tel = '0772315696'; break;
                //case 'מגדלי אביסרור': $msq_api_key = '13596';$msq_default_tel = '0772315696'; break;
                //case '2נופי אביסרור': $msq_api_key = '13596';$msq_default_tel = '0772315696'; break;
                //case 'גרנד אביסרור': $msq_api_key = '13595';$msq_default_tel = '0779974290'; break;
                //case 'אביסרור במערב רחובות': $msq_api_key = '13597';$msq_default_tel = '0772314704'; break;
                //case 'אביסרור מגדלי המדע': $msq_api_key = '13598';$msq_default_tel = '0779974761'; break; 
                //case 'אביסרור בנווה נוי': $msq_api_key = '13594';$msq_default_tel = '0779966302'; break;
                //case 'אביסרור בנאות הדרים': $msq_api_key = '13594';$msq_default_tel = '0779966302'; break;
                //case 'אביסרור בפארק': $msq_api_key = '13594';$msq_default_tel = '0779966302'; break;
            //}
            
            $global_emails = explode(',', $settings->Arg('siteemail'));
            $emails = explode(',', $params['project']->Arg('lead_emails1'));
            $bcc_emails = explode(',', $settings->Arg('bcc_emails'));
            $to_emails = !empty($emails) ? array_merge($global_emails, $emails) : $global_emails;
            $lead_emails = implode(',', $to_emails);
        }
        ?>
        

<div class="maskyoo">
    <a class="phone_class phoneBtn" href="#" >
        <span>חייגו עכשיו</span>
        <img src="<?php echo base_url() . icIMG . 'phone.svg'; ?>" alt="טלפון">
    </a>
</div>
        


<?php if(true): ?>
        <script type="text/javascript">
            $(".phone_class").attr("href", "tel:<?php echo $params['project']->Arg('maskyoo_default_tel'); ?>");

        </script>
        
        <script type="text/javascript">
                                    
                var d = new Date();
                var _msq = _msq || [];
                _msq['msq_api_key']=<?php echo $params['project']->Arg('maskyoo_api_key'); ?>;
                _msq['ReplacePhoneByClass']='phone_class';
                //_msq['ReplacePhoneByClass_attr']='phone_class';
                _msq['SendCallerIDToGoogleAnalytics']=false;
                _msq['msq_url']=window.location.protocol + '//ws.dynamic-numbers.com/webservice.php';
                _msq['Debug']=false;
                _msq['mobile_click_to_call_view']=true;

                //here come the magic
                _msq['costum_client_var'] = new Object();
                _msq['costum_client_var'].clientname = "<?php echo isset($settings) ? $settings->Arg('clientname') : ''; ?>";
                _msq['costum_client_var'].landpagename = "<?php echo $params['project']->Arg('name'); ?>";
                _msq['costum_client_var'].custom2 = "אתר חדש - אביסרור";
                _msq['costum_client_var'].source = "<?php echo current_url() . getQS(); ?>";
                _msq['costum_client_var'].lead_ip = "<?php echo $this->input->ip_address(); ?>";
                _msq['costum_client_var'].campaignid = "<?php echo $params['campaignid']; ?>";
                
                <?php if(isset($_COOKIE['_ga'])): ?>
                // SEND EVENT TO ANALITYCS!!
                _msq['costum_client_var'].analytics = "UA-52573851-8";
                _msq['costum_client_var'].userId = "<?php echo preg_replace("/^.+\.(.+?\..+?)$/", "\\1", @$_COOKIE['_ga']); ?>";
                _msq['costum_client_var'].catName = "project";
                _msq['costum_client_var'].catValue = "maskyoo_<?php echo $this->router->fetch_method(); ?>";
                <?php endif;?>
                
                _msq['costum_client_var'].lead_to_emails = "<?php echo preg_replace('~[\r\n]+~', '', $lead_emails); ?>";
                _msq['costum_client_var'].die_timeout = (180*60*1000);  // will return to the original number after 180min
                _msq['costum_client_var'].load_time = d.toString();  //date and time of the dynamic number

                setTimeout(function(){
                    
                    /*  after X timeout will return to the original number and will send pixel about both numbers(original+dynamic) to maskyoo  */
                    _msq.die="true";
                    _msq.static=[];
                    p_static = {"maskyoo":"<?php echo $params['project']->Arg('maskyoo_default_tel'); ?>","trackingId":_msq.costum_client_var['key_google_analytics_data'].split('|')[1].split('=')[1],"clientId":_msq.costum_client_var['key_google_analytics_data'].split('|')[2].split('=')[1]};
                    for (var key in _msq.costum_client_var) p_static[key]=_msq.costum_client_var[key];
                    delete p_static["key_google_analytics_data"];

                    _msq.static.push(p_static);       
                    var track = new Image().src= window.location.protocol +"//analytics.maskyoo.net/json_pixel.php?p=" + encodeURIComponent(JSON.stringify(_msq.static)).replace(/!/g, '%21').replace(/'/g, '%27').replace(/\(/g, '%28').replace(/\)/g, '%29').replace(/\*/g, '%2A').replace(/%20/g, '+') ;

                            _msq.static[0].maskyoo=_msq.phone;
                    var track = new Image().src= window.location.protocol +"//analytics.maskyoo.net/json_pixel.php?p=" + encodeURIComponent(JSON.stringify(_msq.static)).replace(/!/g, '%21').replace(/'/g, '%27').replace(/\(/g, '%28').replace(/\)/g, '%29').replace(/\*/g, '%2A').replace(/%20/g, '+') ;

                }, (_msq['costum_client_var'].die_timeout)); 
        
                (function() {
                var msq_src = document.createElement('script'); msq_src.type = 'text/javascript'; msq_src.async = true;
                msq_src.src = window.location.protocol + '//ws.dynamic-numbers.com/class.maskyoov6.js';
                var msq_s = document.getElementsByTagName('script')[0]; msq_s.parentNode.insertBefore(msq_src, msq_s);
                })();
                
                function maskyoo_on_start_call(callobj){ /*  this event run when the call start  */    }
                function maskyoo_on_answer_call(callobj){ /*  this event run when the call hangup  */    }
                function maskyoo_on_hangup_call(callobj){ /*  this event run when the call answer  */    }
                function maskyoo_on_dtmf(callobj){ /*  this event run when the called dial DTMF [0-9]  */    }    
                function maskyoo_on_custom_server_var_change(callobj){ /*  this event run when the custom server var change  */    }
        </script>
<?php endif; ?>