{"table": "registersLog", "controller": "newRegister", "method": "", "explain": "registersLog", "title": "FirstName", "description": "LastName", "seo_title": "", "seo_description": "", "image": "", "fields": {"responseClient": {"sort": "80", "width": "col-md-12", "name": "responseClient", "type": "long", "explain": "responseClient", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "FirstName": {"sort": "200", "width": "col-md-12", "name": "FirstName", "type": "short", "explain": "FirstName", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "LastName": {"sort": "190", "width": "col-md-12", "name": "LastName", "type": "short", "explain": "LastName", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "IDNO": {"sort": "180", "width": "col-md-12", "name": "IDNO", "type": "integer", "explain": "IDNO", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Mobile": {"sort": "170", "width": "col-md-12", "name": "Mobile", "type": "short", "explain": "Mobile", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "BirthDate": {"sort": "160", "width": "col-md-12", "name": "BirthDate", "type": "short", "explain": "BirthDate", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "CityCode": {"sort": "150", "width": "col-md-12", "name": "CityCode", "type": "integer", "explain": "CityCode", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Email": {"sort": "140", "width": "col-md-12", "name": "Email", "type": "short", "explain": "Email", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "PrvSchool": {"sort": "130", "width": "col-md-12", "name": "PrvSchool", "type": "integer", "explain": "PrvSchool", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "sex": {"sort": "120", "width": "col-md-12", "name": "sex", "type": "integer", "explain": "sex", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Category": {"sort": "110", "width": "col-md-12", "name": "Category", "type": "integer", "explain": "Category", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "YearYad": {"sort": "100", "width": "col-md-12", "name": "YearYad", "type": "short", "explain": "YearYad", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "passwordMd5": {"sort": "90", "width": "col-md-12", "name": "passwordMd5", "type": "short", "explain": "passwordMd5", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}}}