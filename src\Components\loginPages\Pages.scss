// Pages.scss - Styles for the Pages component

.body-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 100%;
  margin: 0 auto;
  
  &.mobile {
    padding: 0 1rem;
  }
  
  &.desktop {
    padding: 0 2rem;
    
    @media (min-width: 1200px) {
      max-width: 1140px;
    }
    
    @media (min-width: 1600px) {
      max-width: 1400px;
    }
    
    @media (min-width: 2000px) {
      max-width: 1600px;
    }
  }
}

// Base styles for responsive behavior
html, body {
  touch-action: manipulation;
  -ms-content-zooming: none;
  -ms-touch-action: pan-x pan-y;
  font-size: 16px;
  
  @media (min-width: 1600px) {
    font-size: calc(16px + 0.2vw);
  }
}

.main-header {
  text-align: center;
  padding: 10px;
  background-color: #0c213a;
  
  .logo-client {
    width: 100px;
    
    @media (min-width: 1600px) {
      width: 120px;
    }
    
    @media (min-width: 2000px) {
      width: 140px;
    }
  }
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  
  @media (min-width: 1600px) {
    padding: 0 2rem;
  }
}

.mobile-content-container {
  width: 100%;
  max-width: 100%;
  padding: 1rem;
  
  @media (min-width: 768px) {
    max-width: 90%;
  }
}

.main-footer {
  margin-top: auto;
  width: 100%;
  
  .footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    
    @media (min-width: 1600px) {
      padding: 1.5rem;
    }
    
    .credit {
      margin-top: 1rem;
      text-align: center;
      
      a {
        color: inherit;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
} 