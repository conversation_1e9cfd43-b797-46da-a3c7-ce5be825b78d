<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

if ( ! function_exists('changeDateFormat'))
{
    function changeDateFormat($stDate, $stFormatFrom = "d/m/Y H:i", $stFormatTo = "Y/m/d H:i") {
        // When PHP 5.3.0 becomes available to me
        $date = date_parse_from_format($stFormatFrom, $stDate);
        //For now I use the function above
        //$date = dateParseFromFormat($stFormatFrom,$stDate);
        return date($stFormatTo,mktime($date['hour'],
                                          $date['minute'],
                                          $date['second'],
                                          $date['month'],
                                          $date['day'],
                                          $date['year']));
    }
}

if ( ! function_exists('setMessage'))
{
    function setMessage($message = "", $class = "success", $redirect = FALSE) {
        if(!empty($message)) {
            $_SESSION['message_alert']['message'] = $message;
            $_SESSION['message_alert']['class'] = $class;
        }
        header("Location: " . $redirect);
    }
}

if ( ! function_exists('showMessage'))
{
    function showMessage() {
        if(isset($_SESSION['message_alert']) && 
                !empty($_SESSION['message_alert']['message'])) {
            
            $message = $_SESSION['message_alert'];
            unset($_SESSION['message_alert']);
            $icon = "";
            switch($message['class']) {
                case "success": $icon = '<i class="icon fa fa-check"></i>';  break;
                case "info": $icon = '<i class="icon fa fa-info"></i>';  break;
                case "warning": $icon = '<i class="icon fa fa-warning"></i>';  break;
                case "danger": $icon = '<i class="icon fa fa-ban"></i>';  break;
                    
            }
            echo 
            
            '<div class="alert alert-'.$message['class'].' alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                '.$message['message'].'
            </div>';
        }
    }
}

if ( ! function_exists('showPages'))
{
    function showPages($_TOTAL, $_LIMIT, $_PAGING) {
        $pagination_links = '';
        if($_TOTAL > $_LIMIT) {
            $qs = $_GET;
            if(isset($qs['page'])) { unset($qs['page']); }
            $query_string = http_build_query($qs);

            $pagination_links .= '<ul class="pagination pagination-sm no-margin">';
            $_PAGES = $_TOTAL / $_LIMIT; 
            $i = ($_PAGING - 4) > 1 ? ($_PAGING - 4) : 1;
            $imax = ($_PAGING + 3) < $_PAGES ? ($_PAGING + 3) : $_PAGES;
            for($i; $i <= $imax; $i++) {
                if($i == $_PAGING) {
                    $pagination_links .= '<li class="page-item active"><a href="#" class="page-link">' . $i . '</a></li>';
                } else { 
                    $pagination_links .= '<li class="page-item"><a href="?page=' . $i . '&' . $query_string . '" class="page-link">' . $i . '</a></li>';
                }
            }
            if(($_TOTAL % $_LIMIT) != 0) {
                if($i == $_PAGING) {
                    $pagination_links .= '<li class="page-item active"><a href="#" class="page-link">' . $i . '</a></li>';
                } else {
                    $pagination_links .= '<li class="page-item"><a href="?page=' . $i . '&' . $query_string . '" class="page-link">' . $i . '</a></li>';
                }
            }
            $pagination_links .= '</ul>';
        }
        echo $pagination_links;
    }
}

if ( ! function_exists('showInfo'))
{
    function showInfo($_TOTAL, $_LIMIT, $_PAGING) {
        if($_TOTAL > $_LIMIT) {
            $numofpages = ceil($_TOTAL / $_LIMIT);
            echo '<div class="dataTables_info" id="allmemberstable_info">מציג '.(($_PAGING-1)*$_LIMIT +1).' עד '.(($_PAGING)*$_LIMIT).' מתוך '.$numofpages.' עמודים.</div>';
        }
    }
}

if ( ! function_exists('showOrder'))
{
    function showOrder($field, $text) {
        $sortquery = "sort=ASC";
	$orderquery = "order=".$field;
		
        $qs = $_GET;
        $currentsort = isset($qs['sort']) ? $qs['sort'] : "DESC";
        $currentfield = isset($qs['order']) ? $qs['order'] : "id";
        
	if($currentsort == "ASC"){
            $sortquery = "sort=DESC";
            $sortarrow = '&#9650;';
	}
		
	if($currentsort == "DESC"){
            $sortquery = "sort=ASC";
            $sortarrow = '&#9660;';
	}
		
	if($currentfield == $field) {
            $orderquery = "order=".$field;
	} else {
            $sortarrow = null;
	}
        
        
        if(isset($qs['order'])) { unset($qs['order']); }
        if(isset($qs['sort'])) { unset($qs['sort']); }
        $query_string = '?' . http_build_query($qs) . '&';
        
	echo '<a href="' . $query_string . $orderquery . '&' . $sortquery . '">' . $text . ' ' . $sortarrow . '</a>';	
    }
}

if ( ! function_exists('getQS'))
{
     function getQS($unset = FALSE) {
        $qs = $_GET;
        if($unset && isset($qs[$unset])) {
            unset($qs[$unset]);
        }
        
	unset($qs['thank']);
        unset($qs['message']);
        
        return '?' . http_build_query($qs);
        
        
    }
}


if ( ! function_exists('getQS2'))
{
     function getQS2($unset = FALSE) {
        $qs = $_GET;
        if($unset && isset($qs[$unset])) {
            unset($qs[$unset]);
        }
        
        unset($qs['thank']);
        unset($qs['message']);
        
        
        if(!empty(http_build_query($qs))) {
            $return = http_build_query($qs);
        }
        
        else $return = FALSE;
        
        return $return;
    }
    
       

}

