<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Welcome extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+asd';
        $this->data['current_language'] = 'he';
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
        
//        $this->load->helper('cookie');
//        if($this->input->get('language')) {
//            $cookie = array(
//                'name'   => 'current_language',
//                'value'  => $this->input->get('language'),
//                'expire' =>  86500,
//                'secure' => false
//            );
//            set_cookie($cookie); 
//            redirect(base_url());
//        } else if($lang = get_cookie('current_language', false)) {
//            $this->data['current_language'] = $lang;
//        } else {
//            $lang_array = explode('_', locale_accept_from_http($this->input->server('HTTP_ACCEPT_LANGUAGE')));
//            $langs = $this->config->item('available_lang');
//            $lang = isset($lang_array[0]) && isset($langs[$lang_array[0]]) ? $lang_array[0] : 'he';
//            $cookie = array(
//                'name'   => 'current_language',
//                'value'  => $lang,
//                'expire' =>  86500,
//                'secure' => false
//            );
//            set_cookie($cookie); 
            //$this->data['current_language'] = 'he';
            //$this->data['current_language'] = $lang;
        //}
        
        $this->data['platform'] = "desktop";
        
        
        $this->data['folderView'] = $this->folderView = $this->data['current_language'] . '/' . $this->data['platform'] . '/';
        
        if($is_error) {
            set_status_header($is_error);
            $this->data['controller'] = 'welcome';
            $this->data['method'] = 'error404';
            $this->data['param'] = $param;
        } else {
            $this->data['controller'] = $this->router->fetch_class();
            $this->data['method'] = $this->router->fetch_method();
            $this->data['param'] = $param;
        }
        
        $this->data['settings'] = $this->msite->get_settings();
        
        $this->data['pages'] = $this->msite->get_all_pages(0);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        $this->data['body_class'] = $this->data['controller'] . '-' . $this->data['method'];
        
        
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['welcome_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id(),$this->data['current_language']);
        $seo_id = $this->msite->get_seo_id($this->data['controller'], $this->data['method'], $this->data['param']);
        $this->data['page']->Set('seo', $this->msite->get_seo($seo_id));
        $this->data['alert'] = $this->session->flashdata('alert', false);
        
        $this->data['accessibility'] = 
                $this->session->has_userdata('accessibility') ? 
                $this->session->userdata('accessibility') : 
                array('zoom' => 0, 'accessibility' => false, 'display' => false, 'links' => false, 'texts' => false);
        
        //Menu
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $this->data['menu_items'] = $this->msite->get_all_objects('menu_items');
        
        //print_r($this->data['categories']);die();
        
        //$this->data['contactData'] = $this->msite->get_page_with_objects(16,$this->data['current_language']);
        
        //header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    public function leadform() {
        $this->_loader();
        $redirect = $this->input->post_get('redirect') ? $this->input->post_get('redirect') : base_url();
        $settings = $this->msite->get_settings();
        $mails_to = $settings->Arg('siteemail');
        
               
        $params = FALSE;

        if($this->input->get('code') === md5($this->data['code']) ) {
            
            $this->load->model('mleads');
            $message = $this->input->post_get('message') ? $this->input->post_get('message') : '';

            $message .= $this->input->post_get('custom1') ? ' - '.$this->input->post_get('custom1') : '';
            $message .= $this->input->post_get('place') ? ' - '.$this->input->post_get('place') : '';
            
            $utm = "";
            $qs = $this->session->has_userdata('query_string') ? $this->session->userdata('query_string') : null;
            if(!empty($this->input->get('utm_source')) && !empty($this->input->get('utm_campaign'))) {
                $utm_src = $this->input->get('utm_source');
                $campaign = explode('_', $this->input->get('utm_campaign'));
                $utm_campaign_id = !empty($campaign) ? end($campaign) : '';
                $utm = $utm_src . '_' . $utm_campaign_id;
            }
            
            
            
            $subject = "אתר - " . $settings->Arg('clientname');
            
            $landpage_name =  $this->input->post_get('landpage_name', true);
            
            $params = array(
                'subject' => $subject,
                'client_name' => $settings->Arg('clientname'),
                'landpage_name' => $landpage_name,
                'bannerid' => $this->input->post_get('bannerid', true),
                'page_link' => $this->input->post_get('page_link', true),
                
                'campaignid' => $this->input->post_get('campaign_id', true) ? $this->input->post_get('campaign_id', true) : '',
                
                'utm' => $utm,
                
                'created_at' => date("Y-m-d H:i:s"),
                'name' => $this->input->post_get('name') ? $this->input->post_get('name') : '',
                'phone' => $this->input->post_get('phone') ? $this->input->post_get('phone') : '',
                'email' => $this->input->post_get('email') ? $this->input->post_get('email') : '',
                'message' => $message,
                'custom1' => $this->input->post_get('custom1', true),
                'custom2' => $this->input->post_get('custom2', true),
                'itkunim' => $this->input->post_get('itkunim') ? $this->input->post_get('itkunim') : '',
                
                
                
                
                'kind' => empty($this->input->post_get('kind')) ? $this->input->post_get('kind') : $landpage_name
                
            );

            $this->msite->insert_object('leads', $params);


            $crm = $this->mleads->saveToCrm($params);
            
            if ( isset($crm['duplicated_lead']) && empty($crm['duplicated_lead']) ) {
                $this->mleads->sendEmail($mails_to, $params);
            }

            
    };
    
        
        $output['params'] = $params;
        
        
        $this->data = $output;
        
        if(!$params) {
            set_status_header(500);
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
}
    
    
    
    public function error404() {
        
        set_status_header(404);
        die('not Found');
        $this->_loader(FALSE, 404);
        
        $this->data['view'] = 'error/index';
        $this->load->view($this->folderView . 'index', $this->data);
    }
    
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
        
//        if( !$this->input->get('code') or $this->input->get('code') != md5($this->data['code']) ) {
//            die('error');
//        }
        
        $this->load->model('msiteWs');
        
        
        $this->load->helper('cookie');
        $this->data['current_language'] = 'he';
        
        if($is_error) {
            set_status_header($is_error);
            $this->data['controller'] = 'welcome';
            $this->data['method'] = 'error404';
            $this->data['param'] = $param;
        } else {
            $this->data['controller'] = $this->router->fetch_class();
            $this->data['method'] = $this->router->fetch_method();
            $this->data['param'] = $param;
        }
        
        
        $this->data['settings'] = $this->msiteWs->get_settings();
        $this->data['pages'] = $this->msite->get_all_pages(0);
        
        //print_r($this->data['pages']);die();
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        
        
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['first_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id(),$this->data['current_language']);
        
        
        //$seo_id = $this->msite->get_seo_id($this->data['controller'], $this->data['method'], $this->data['param']);
        //$this->data['page']->Set('seo', $this->msite->get_seo($seo_id));
        $this->data['alert'] = $this->session->flashdata('alert', false);
        
        $this->data['accessibility'] = 
                $this->session->has_userdata('accessibility') ? 
                $this->session->userdata('accessibility') : 
                array('zoom' => 0, 'accessibility' => false, 'display' => false, 'links' => false, 'texts' => false);
        

        
        
    }
    
    public function SiteDataItems ($param = FALSE) {
        $this->_loaderWS($param);
        
        $output['pages'] =  $this->msiteWs->get_pagesWs($this->data['pages']);
        $output['settings'] = $this->data['settings'];
        
        $output['contactData'] = $this->msiteWs->get_page_with_objectsArray(16,$this->data['current_language']);
        
        //GET PAGES WITH ID!!!
        $output['paramPages'] = $this->msiteWs->getParamPages();
        
        $csrf = array(
            'name' => $this->security->get_csrf_token_name(),
            'hash' => $this->security->get_csrf_hash()
        );
        
        $output['csrf'] = $csrf;
            
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $events = $this->msite->get_all_objects('events');
//        $output['events'] = $this->msiteWs->objects_to_ArrayNoseo($events);
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function index($param = FALSE) {
        $this->_loaderWS($param);
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $categories1 = $this->msite->get_all_objects('categories');
//        $output['categories'] = $this->msiteWs->objects_to_Array($categories1,true);
        
                
//        foreach ($peopleArray as $key => $value) {
//                unset($peopleArray[$key]['data']['created_at']);
//                unset($peopleArray[$key]['data']['text']);
//                unset($peopleArray[$key]['data']['tafkid']);
//                unset($peopleArray[$key]['data']['updated_at']);
//                unset($peopleArray[$key]['data']['status']);
//                unset($peopleArray[$key]['data']['sort']);
//                unset($peopleArray[$key]['data']['seo_id']);
//                unset($peopleArray[$key]['data']['seo']);
//                unset($peopleArray[$key]['data']['lang']);
//        } 
//        $output['people'] = $peopleArray;
        
        $output['page'] = $this->data['page']->Arr();
        
        
        $this->data = $output;
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function StoreSteps ($param = FALSE) {
        $this->_loaderWS($param);
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $galleryAboutPage1 = $this->msite->get_all_objects('galleryAboutPage');
//        $output['galleryAboutPage'] = $this->msiteWs->objects_to_ArrayNoseoData($galleryAboutPage1);
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    public function plants ($param = FALSE) {
        $this->_loaderWS($param);
        
        
//          $users1 = $this->msite->get_object('users', 1);
//          $output['users'] = $users1->MultiTableChoseArray('tete');
        
        
        if(!empty($this->input->get('whereDBId'))) {
            $this->msite->set_where("id='{$this->input->get('whereDBId')}'");
        }
        
        if(!empty($this->input->get('whereDBcategory'))) {
            $this->msite->set_where("category='{$this->input->get('whereDBcategory')}'");
        }
        
        
        
        $this->msite->sort_objects("sort", "DESC");
        $page = $this->input->get('page') ? $this->input->get('page') : 1;
        
        
        
        $this->msite->limit_objects($page,10);  // No hacer muy pocas porq tiene que leer todo el timpo de la DB
        $items = $this->msite->get_all_objects('projects');
            
        $output['items'] = $this->msiteWs->objects_to_Array($items, $shortSeo = true);

        
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    public function helpingPeople ($param = FALSE) {
        $this->_loaderWS($param);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $categories1 = $this->msite->get_all_objects('categories');
        $output['categories'] = $this->msiteWs->objects_to_Array($categories1,true);
        
        if($param != 2) {
            $this->msite->set_where(Msite::TABLE_PROJECTS . ".category='$param'");
        }
        
        $this->msite->set_where(Msite::TABLE_PROJECTS . ".status='1'");
        $this->msite->sort_objects(Msite::TABLE_PROJECTS . ".sort", "DESC");
        $projects1 = $this->msite->get_all_projects('');
        
        // https://chabad.ak-digital.co.il/api/welcome/helpingPeople/%D7%A9%D7%99%D7%A4%D7%95%D7%A6%D7%99%D7%9D_5?v=1
            
//        echo "<pre>";
//            print_r($projects1);
//        echo "</pre>";die('sebas');
        
        
        $output['projects'] = $this->msiteWs->objects_to_Array($projects1,true);
        
        
        
        //FILTERS
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $filter_target1 = $this->msite->get_all_objects('filter_target');
        $filter_target = $this->msiteWs->objects_to_ArrayNoseo($filter_target1);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $filter_money1 = $this->msite->get_all_objects('filter_money');
        $filter_money = $this->msiteWs->objects_to_ArrayNoseo($filter_money1);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $filter_places1 = $this->msite->get_all_objects('filter_places');
        $filter_places = $this->msiteWs->objects_to_ArrayNoseo($filter_places1);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $filter_time1 = $this->msite->get_all_objects('filter_time');
        $filter_time = $this->msiteWs->objects_to_ArrayNoseo($filter_time1);
        // END FILTERS
        
        
        $output['filters'] = array(
            'target' => $filter_target,
            'money' => $filter_money,
            'places' => $filter_places,
            'time' => $filter_time
        );
        
        
        if($param) {
            
           $output['paramPages'] = $this->msiteWs->getLowSeoPage('categories',$param);
           //print_r($output['categories']);die();
            foreach ($output['categories'] as $category) {
               
               if($category['data']['id']==$param) {
                   $output['selectedCat'] = $category['data'];
               }
               
            }
           
        }
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function projects ($param = FALSE) {
        
//        https://chabad.ak-digital.co.il/api/welcome/projects/%D7%A9%D7%99%D7%A4%D7%95%D7%A5_%D7%93%D7%99%D7%A8%D7%94_%D7%9C%D7%A0%D7%99%D7%A6%D7%95%D7%9C%D7%AA_%D7%A9%D7%95%D7%90%D7%94_9?v=4
        
        $this->_loaderWS($param);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $categories1 = $this->msite->get_all_objects('categories');
        $output['categories'] = $this->msiteWs->objects_to_Array($categories1,true);
        
        
        if($param) {
            
            $output['paramPages'] = $this->msiteWs->getLowSeoPage('projects',$param);
            
            //die('sebas');
            
            $this->msite->set_where(Msite::TABLE_PROJECTS . ".status='1'");
            $this->msite->set_where(Msite::TABLE_PROJECTS . ".id='$param'");
            $this->msite->sort_objects(Msite::TABLE_PROJECTS . ".sort", "DESC");
            
            
            $projects1 = $this->msite->get_all_projects(true);

            if(isset($projects1[0])) {
                $output['project'] = $this->msiteWs->object_to_Array($projects1[0]);
                
                $this->msite->set_where("status='1'");
                $this->msite->set_where("project_id='$param'");
                $this->msite->sort_objects("sort", "DESC");
                $pictures1 = $this->msite->get_all_objects('galleryProjects');
                
                if(!empty($pictures1)) {
                    $pictures2 = $this->msiteWs->objects_to_ArrayNoseo($pictures1);
                }
                
                $image = array (
                    'id' => 'image',
                    'image' => $output['project']['data']['image'],
                    'small' => '1'
                );
                
                $video = array (
                    'id' => 'video',
                    'image' => $output['project']['data']['video'],
                    'video' => '1'
                );
                
                $big_image = array (
                        'id' => 'big',
                        'image' => $output['project']['data']['big_image'],
                        //'m_image' => $output['project']['data']['m_big_image']
                    );
                
                if(!empty($pictures2)) {
                    
                    $output['project']['pictures'] = $pictures2;
                    
                    if(!empty($big_image['image'])) {
                        array_unshift($output['project']['pictures'], $big_image);
                    }
                    
                } else {
                    if(!empty($big_image['image'])) {
                        $output['project']['pictures'][] = $big_image;
                    } else {
                         $output['project']['pictures'][] = $image;
                    }
                }
                
                
                if(!empty($video['image'])) {
                    array_unshift($output['project']['pictures'], $video);
                }
                
                
            } else {
                $output['project'] = array();
            }
            
            foreach ($output['categories'] as $category) {
               
               if($category['data']['id']==$output['project']['data']['category']) {
                   $output['selectedCat'] = $category['data'];
                   $selectedCatId = $category['data']['id'];
                   $output['selectedCat_seo'] = $category['seo'];
               }
            }
            
            
            
        //GET פרויקטים נוספים
        $this->msite->set_where(Msite::TABLE_PROJECTS . ".status='1'");
        $this->msite->set_where(Msite::TABLE_PROJECTS . ".category='$selectedCatId'");
        
        
        $this->msite->set_where(Msite::TABLE_PROJECTS . ".id !='$param'");
        $this->msite->sort_objects(Msite::TABLE_PROJECTS . ".sort", "DESC");
        $this->msite->limit_objects(1,3);
        
        $projects1 = $this->msite->get_all_projects(false,true);
            
        $output['moreProjects'] = $this->msiteWs->objects_to_Array($projects1,true);
        
//        echo "<pre>";
//            print_r($projects1);
//        echo "</pre>";die('sebas'); 
        
    }
        
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function needHelp ($param = FALSE) {
        $this->_loaderWS($param);
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $galleryAboutPage1 = $this->msite->get_all_objects('galleryAboutPage');
//        $output['galleryAboutPage'] = $this->msiteWs->objects_to_ArrayNoseoData($galleryAboutPage1);
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    
    public function success ($param = FALSE) {
        $this->_loaderWS($param);
        
        $this->msite->set_where("status='1'");
        //$this->msite->sort_objects("sort", "DESC");
        
        $this->msite->sort_objects("DateTime", "DESC");
        
        $SuccessDB1 = $this->msite->get_all_objects('SuccessDB');
        $success = $this->msiteWs->objects_to_ArrayNoseoData($SuccessDB1);
        
        foreach ($success as $value) {
            
            $sortdata[]['data'] = array (
                'id' => $value['data']['id'],
                'title' => $value['data']['title'],
                'title' => $value['data']['title'],
                'text' => $value['data']['text'],
                'image' => $value['data']['image'],
                'dateTime' => changeDateFormat($value['data']['DateTime'], $stFormatFrom = "Y-m-d H:i:ss", $stFormatTo = "H:i | d/m/Y "),
            );
            
        }
        
        $output['success'] = $sortdata;
        
        //print_r($output['success']);
        //die('...success');
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
    }
    
    public function logInUsers ($param = FALSE) {
        
        $this->_loaderWS($param);
        
        //https://github.com/emreakay/CodeIgniter-Aauth
        
        //Groups  > Crear primero los grupos
        //$this->aauth->create_group('AdminUsers');
        
        //AddUser
        $username= 'sebasUser';
        $password='sebas';
        
        
        //$AddUser = $this->aauth->create_user('<EMAIL>', $password,$username);
        //$this->aauth->add_member($AddUser, 'AdminUsers');
        
        //Check PASS: TRUE OR FALSE?
        //$this->aauth->login(set_value('username'), set_value('password'))
        
        $output['checkUserYes'] = $this->aauth->login($username, $password);
        $output['error1'] = $this->aauth->get_errors_array();
        $output['user'] = $this->aauth->get_user();
        $output['is_group_admin'] = $this->aauth->is_member( 'AdminUsers', $this->aauth->get_user('id') );
        
        
        
        $output['checkUserNo'] = $this->aauth->login('developers', 'titi');
        $output['error2'] = $this->aauth->get_errors_array();
        
        $username= 'developers';
        $password='waveprojects!';
        $output['checkUserYes2'] = $this->aauth->login($username, $password);
        $output['is_group_admin2'] = $this->aauth->is_member( 'AdminUsers', $this->aauth->get_user('id') );
        $output['is_group_admin3'] = $this->aauth->is_member( 'Admin', $this->aauth->get_user('id') );
        
        //$output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
        
        
    }
    
    
    public function about ($param = FALSE) {
        $this->_loaderWS($param);
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $galleryAboutPage1 = $this->msite->get_all_objects('galleryAboutPage');
//        $output['galleryAboutPage'] = $this->msiteWs->objects_to_ArrayNoseoData($galleryAboutPage1);
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function questions ($param = FALSE) {
        $this->_loaderWS($param);
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        $questions1 = $this->msite->get_all_objects('questions');
        $output['questions'] = $this->msiteWs->objects_to_ArrayNoseoData($questions1);
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function takanon ($param = FALSE) {
        $this->_loaderWS($param);
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $questions1 = $this->msite->get_all_objects('questions');
//        $output['questions'] = $this->msiteWs->objects_to_ArrayNoseoData($questions1);
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function drupal ($param = FALSE) {
        $this->_loaderWS($param);
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
     public function leads () {
        $this->_loader();
        $this->load->model('mleads');
        
        $code = $this->input->get('code');
        
        if($code != '1123_$asdas*!') {
            die('ERROR');
        }
        
//        https://sorokafoundation.ak-digital.co.il/api/welcome/tranzila?sum=1&Response=000&contact=coments&pdesc=product&email=<EMAIL>&company=comapny&typeDonation=typeDonation&comments=comments&receive=receive&contact=contact&phone=phone&sum=sum
        
        $settings = $this->msite->get_settings();
        //$mails_to = $settings->Arg('siteemail');
               
        
        $this->data['pages'] = $this->msite->get_all_pages(0);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        $this->data['body_class'] = $this->data['controller'] . '-' . $this->data['method'];
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['welcome_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id());
        $this->data['settings'] = $settings;
        
        
        $this->msite->set_where("created_at >='2021-07-04'");
        $this->msite->sort_objects("id", "DESC");
        //$this->msite->limit_objects(1,10000);        
        $this->data['countFirst'] = count($this->msite->get_all_objects('leadsLandpage'));
        
        $dateFrom = $this->input->get('dateFrom') ? $this->input->get('dateFrom') : date("Y-m-d");
        
        $this->msite->set_where("created_at >='$dateFrom'");
        $this->msite->sort_objects("id", "DESC");
        //$this->msite->limit_objects(1,10000);        
        $this->data['leadsLandpage'] = $this->msite->get_all_objects('leadsLandpage');
        
        $this->data['countAll'] = $this->msite->count_all_objects('leadsLandpage');
        $this->data['dateFrom'] = $dateFrom;
        
        //print_r($this->data['leadsLandpage']);
    
        $this->data['view'] = $this->router->fetch_method() . '/index';
        $this->data['script'] = $this->router->fetch_method().'/script';
        
        $this->load->view('he/desktop/leads/index', $this->data);
    }
    
    
    
    
    
    public function helpRequestIframe () {
        $this->_loader();
        $this->load->model('mleads');
        
//        https://sorokafoundation.ak-digital.co.il/api/welcome/tranzila?sum=1&Response=000&contact=coments&pdesc=product&email=<EMAIL>&company=comapny&typeDonation=typeDonation&comments=comments&receive=receive&contact=contact&phone=phone&sum=sum
        
        $settings = $this->msite->get_settings();
        //$mails_to = $settings->Arg('siteemail');
               
        
        $this->data['pages'] = $this->msite->get_all_pages(0);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        $this->data['body_class'] = $this->data['controller'] . '-' . $this->data['method'];
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['welcome_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id());
        $this->data['settings'] = $settings;
        
        
        
        $this->msite->set_where("status='1'");
        $this->msite->sort_objects("sort", "DESC");
        
        $this->data['categories'] = $this->msite->get_all_objects('categories');
        
    
        $this->data['view'] = $this->router->fetch_method() . '/index';
        $this->data['script'] = $this->router->fetch_method().'/script';
        
        $this->load->view('he/desktop/helpRequestIframe/index', $this->data);
    }
    
    
    
    public function sendHelpRequestMail () {
        $this->_loader();
        
        $settings = $this->msite->get_settings();
        $mails_to = $settings->Arg('siteemail');
               
        $params = FALSE;
       
        //if ($this->input->post('jobId') && !empty($this->input->post('jobId')) and !empty($_FILES['userfile']['name'])) {
        if ( !empty($this->input->post('phone')) ) {
                    
            $this->load->model('mleads');
            
            $filename = isset($_FILES['userfile']['name']) ? $_FILES['userfile']['name'] : '';
            $place =  !empty($this->input->post('place')) ? $this->input->post('place') : '';
            $target_id = !empty($this->input->post('target')) ? $this->input->post('target') : '';
            $forWho = !empty($this->input->post('forWho')) ? $this->input->post('forWho') : '';
            $ishur = !empty($this->input->post('ishur')) ? $this->input->post('ishur') : '';
            
            if(!empty($target_id)) {
                
                $item = $this->msite->get_object('categories', $target_id);
                
                $target = $item->Arg('title');
                
            } else {$target = '';}
            
            
            
            $nameHelp = !empty($this->input->post('nameHelp')) ? $this->input->post('nameHelp') : '';
            $phoneHelp = !empty($this->input->post('phoneHelp')) ? $this->input->post('phoneHelp') : '';
            
            $message = $filename.' | '.$place.' | '.$target.' | '.$forWho.' | '.$ishur.' | '.$nameHelp.' | '.$phoneHelp;

            
            $utm = "";
            $qs = $this->session->has_userdata('query_string') ? $this->session->userdata('query_string') : null;
            if(!empty($this->input->get('utm_source')) && !empty($this->input->get('utm_campaign'))) {
                $utm_src = $this->input->get('utm_source');
                $campaign = explode('_', $this->input->get('utm_campaign'));
                $utm_campaign_id = !empty($campaign) ? end($campaign) : '';
                $utm = $utm_src . '_' . $utm_campaign_id;
            }
            
            
            $subject = "אתר - " . $settings->Arg('clientname');
            $landpage_name = 'זקוקים לעזרה';
            
            
            $params = array(
                'subject' => $subject,
                'client_name' => $settings->Arg('clientname'),
                'landpage_name' => $landpage_name,
                'bannerid' => $this->input->post_get('bannerid', true),
                'page_link' => '',
                
                
                'file' => $filename,
                'place' => $place,
                'target' => $target,
                'forWho' => $forWho,
                'ishur' => $ishur,
                'nameHelp' => $nameHelp,
                'phoneHelp' => $phoneHelp,
                
                'campaignid' => $this->input->post_get('campaign_id', true) ? $this->input->post_get('campaign_id', true) : '',
                
                'utm' => $utm,
                
                'created_at' => date("Y-m-d H:i:s"),
                'name' => $this->input->post_get('name') ? $this->input->post_get('name') : '',
                'phone' => $this->input->post_get('phone') ? $this->input->post_get('phone') : '',
                'email' => $this->input->post_get('email') ? $this->input->post_get('email') : '',
                'message' => $message,
                'custom1' => $this->input->post_get('place', true),
                'custom2' => $message,
                'itkunim' => $this->input->post_get('itkunim') ? $this->input->post_get('itkunim') : '',
                
                'kind' => $this->input->post_get('kind') ? $this->input->post_get('kind') : ''
                
            );
            
            
            

            if($params['forWho'] != 'עבורי') {
                    $ishur1 = empty($ishur) ? '' : 'קיבלת את הסכמתו? '.$ishur.' | ';
                    $nameHelp1 = empty($nameHelp) ? '' : 'שם האדם הזקוק לעזרה '.$nameHelp.' | ';
                    $phoneHelp1 = empty($phoneHelp) ? '' : 'טלפון האדם הזקוק לעזרה '.$phoneHelp.' | ';

                    $lead_3rdPersonHelp = $ishur1.$nameHelp1.$phoneHelp1;
            } else {$lead_3rdPersonHelp='';}
            
            
                    
                    
            $paramsProjects = array(
                
                'lead_place' =>  $params['place'],
                'category' => !empty($target_id) ? $target_id : 5,
                
                'place' => '',
                'city' => '',
                'money' => 1,
                'timeFilter' => 5,
                'whoFilter' => 2,
                'volunteers' => 5,
                'timeProj' => '',
                'dateProj' => '',
                
                'lead_4whoHelp' =>  $params['forWho'],
                'ishur' => $ishur,
                'nameHelp' => $nameHelp,
                'phoneHelp' => $phoneHelp,
                'sort' =>  $this->msite->get_max('projects', 'sort') + 10,
                'created_at' => date("Y-m-d H:i:s"),
                'user_id' => $params['name'],
                'phone' => $params['phone'],
                'mail' => $params['email'],
                'lead_3rdPersonHelp' => $lead_3rdPersonHelp
                
                
            );
            
            
            $insert_id = $this->msite->insert_object('projects', $paramsProjects);
            
            if($insert_id) {
                $data_seo = array(
                        'title' => $target,
                        'description' => '',
                        'friendly' => url_title($target . '_' . $insert_id),
                        'controller' => 'welcome',
                        'method' => 'projects',
                        'param' => $insert_id,
                        'robots' => 'index, follow',
                        'image' => NULL,
                        'canonical' => base_url('welcome' . '/' . 'projects' . '/' . $insert_id),
                    );

                $seo_id = $this->msite->insert_object('seo', $data_seo); 
                $this->msite->update_object('projects', $insert_id, array('seo_id' => $seo_id));

                if(isset($_FILES['userfile']['name']) and !empty($_FILES['userfile']['name']) ) {
                    
                    $picture = $this->msite->uploadProjectPicutes($insert_id);
                    //print_r($picture);die('<br/>seb2');
                }
            }
            


            $this->mleads->saveToCrm($params);
            $this->mleads->sendEmail($mails_to, $params, true);
            
            $alert = array("class" => "green", "title" => "תודה על פנייתך!", "message" => "נציג שלנו יצור איתך קשר
בהקדם לתיאום העזרה.");
            $this->session->set_flashdata('alert', $alert);
            
            $mobile = $this->input->get('mobile') == 1 ? '&mobile=1' : '';
            
            redirect(base_url('welcome/helpRequestIframe?thank=1'.$mobile));
        };
        
        
        $this->data['pages'] = $this->msite->get_all_pages(0);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        $this->data['body_class'] = $this->data['controller'] . '-' . $this->data['method'];
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['welcome_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id());
        $this->data['settings'] = $settings;
    
        $this->data['view'] = $this->router->fetch_method() . '/index';
        $this->data['script'] = $this->router->fetch_method().'/script';
        
        $this->load->view('he/desktop/jobs/index', $this->data);
    }
    
    
        
    
    
    public function error404Page($param = FALSE) {
        
         
        $this->_loaderWS($param);
        
                
        $output['page'] = $this->data['page']->Arr();
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function contact($param = FALSE) {
        $this->_loaderWS($param);
        
        
        
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $this->data['logos'] = $this->msite->get_all_objects('logos');
        //$this->load->view($this->folderView . 'index', $this->data);
        
        
        $output['page'] = $this->data['page']->Arr();
     
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function accessibility ($param = FALSE) {
        $this->_loaderWS($param);
        
        $output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
       
    
    public function tranzila () {
        $this->_loader();
        $this->load->model('mleads');
        
//        https://sorokafoundation.ak-digital.co.il/api/welcome/tranzila?sum=1&Response=000&contact=coments&pdesc=product&email=<EMAIL>&company=comapny&typeDonation=typeDonation&comments=comments&receive=receive&contact=contact&phone=phone&sum=sum
        
        $settings = $this->msite->get_settings();
        //$mails_to = $settings->Arg('siteemail');
               
        
        $this->data['pages'] = $this->msite->get_all_pages(0);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        $this->data['body_class'] = $this->data['controller'] . '-' . $this->data['method'];
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['welcome_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id());
        $this->data['settings'] = $settings;
        
        $response['post'] = $this->input->post();
        $response['get'] = $this->input->get();

        $responseJson = json_encode($response);

        if(!empty($responseJson)) {
            //print_r($responseJson);
            
            $params = array(
                'status' => 0,
                'created_at' => date("Y-m-d H:i:s"),
                'response' => $responseJson,
                
                'responseKartis' => $this->input->post_get('Response') ? $this->input->post_get('Response') : '',
                'pdesc' => $this->input->post_get('pdesc') ? $this->input->post_get('pdesc') : '',
                'email' => $this->input->post_get('email') ? $this->input->post_get('email') : '',
                'company' => $this->input->post_get('company') ? $this->input->post_get('company') : '',
                
                
                'typeDonation' => $this->input->post_get('typeDonation') ? $this->input->post_get('typeDonation') : '',
                'comments' => $this->input->post_get('phone') ? $this->input->post_get('comments') : '',
                'receive' => $this->input->post_get('receive') ? $this->input->post_get('receive') : '',
                
                
                'name' => $this->input->post_get('contact') ? $this->input->post_get('contact') : '',
                'phone' => $this->input->post_get('phone') ? $this->input->post_get('phone') : '',
                'sum' => $this->input->post_get('sum') ? $this->input->post_get('sum') : ''                 
            );

            $insert_id = $this->msite->insert_object('merchant', $params);
            //echo 'insert_id: '.$insert_id.'<br/><br/>';           
            
        }
        
        
        if( 
                ($this->input->post() or $this->input->get()) && 
                (
                    ($this->input->post('Response') && $this->input->post('Response') === "000") or
                    ($this->input->get('Response') && $this->input->get('Response') === "000") 
                )
            ) {
                $this->data['tanzilaResponse'] = 'OK';
                
        } 
        else {
            $this->data['tanzilaResponse'] = 'ERROR';
        }
        
        
        $mails_toTruma = $settings->Arg('siteemailTruma');
        
        $message = 'תיאור: '.$params['pdesc'].' | ';
        $message .= 'חברה: '.$params['company'].' | ';
        $message .= 'תרומה חד פעמית /  חודשי: '.$params['typeDonation'].' | ';
        $message .= 'הערות: '.$params['comments'].' | ';
        $message .= 'קבלה: '.$params['receive'];
                
        $paramsMailLead = array(
                'subject' => 'תרומה - '.$this->data['tanzilaResponse'],
                'client_name' => $settings->Arg('clientname'),
                'landpage_name' => 'תרומה מאתר',
                'bannerid' => $this->input->post_get('bannerid', true),
                'page_link' => $this->input->post_get('page_link', true),                
                'campaignid' => $this->input->post_get('campaign_id', true) ? $this->input->post_get('campaign_id', true) : '',
                'utm' => '',
                'created_at' => date("Y-m-d H:i:s"),
                'name' => $params['name'],
                'phone' => $params['phone'],
                'email' => $params['email'],
                'message' => $message,
                'custom1' => 'עבר?: '.$this->data['tanzilaResponse'],
                'custom2' => $message,
                'itkunim' => '',
                'kind' => 'tranzila'
            );

            $crm = $this->mleads->saveToCrm($paramsMailLead);
            
            if ( isset($crm['duplicated_lead']) && empty($crm['duplicated_lead']) ) {
                $this->mleads->sendEmail($mails_toTruma, $paramsMailLead);
            }
        
    
        $this->data['view'] = $this->router->fetch_method() . '/index';
        $this->data['script'] = $this->router->fetch_method().'/script';
        
        $this->load->view('he/desktop/tranzila/index', $this->data);
    }
    
    
}





