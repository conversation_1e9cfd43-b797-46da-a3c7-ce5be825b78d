<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Lead extends CI_Controller {
    
    private $table = 'leads';
    
    public function __construct() {
        parent::__construct();
        //if is not logged in die  
        if( ! $this->aauth->is_loggedin() ) { 
            redirect(base_url('admin?redirect=' . current_url() . getQS()), 'refresh', 401);
            die('The user is not connected.'); 
        } else if( ! $this->aauth->is_allowed($this->router->fetch_class() . '_' . $this->router->fetch_method())) {
            die('The user not have permission to view the content.'); 
        }
        $this->load->model('mleads');
    }
    
    public function index() {
        //show page of all objects
        $data['q'] = $this->input->get('q') ? $this->input->get('q') : '';
        $data['page'] = $this->input->get('page') ? $this->input->get('page') : 1;
        $data['limit'] = $this->input->get('limit') ? $this->input->get('limit') : 100;
        $data['order'] = $this->input->get('order') ? $this->input->get('order') : 'created_at';
        $data['sort'] = $this->input->get('sort') ? $this->input->get('sort') : 'DESC';
        $data['from_date'] = $this->input->get('from_date') ? changeDateFormat($this->input->get('from_date')) : FALSE;
        $data['to_date'] = $this->input->get('to_date') ? changeDateFormat($this->input->get('to_date')) : FALSE;

        
        if($data['from_date']) {$this->msite->set_where("created_at >= '".$data['from_date']."'");}
        if($data['to_date']) {$this->msite->set_where("created_at <= '".$data['to_date']."'");}
        $data['total'] = $this->msite->count_all_objects($this->table);
        if($data['from_date']) {$this->msite->set_where("created_at >= '".$data['from_date']."'");}
        if($data['to_date']) {$this->msite->set_where("created_at <= '".$data['to_date']."'");}
        $this->msite->limit_objects($data['page'], $data['limit']);
        $this->msite->sort_objects($data['order'], $data['sort']);
        $data['objects'] = $this->msite->get_all_objects($this->table);
        
        
        $data['view'] = $this->router->fetch_class() . '/index';
        $this->load->view('admin/index', $data);
    }
    
    public function csv() {
        $this->load->helper('file');
        $this->load->helper('download');
        $this->load->dbutil();
        
        $filename = 'leads_'.date("Y-m-d h-i-s").'.csv';
        $delimiter = ",";
        $newline = "\r\n";
        $enclosure = '"';
        
        $where = " WHERE 1 ";
        if($this->input->get('kind')) {
            $where .= " AND `kind` = '" . $this->input->get('kind') . "' ";
        }
        
        if($this->input->get('from_date')) {
            $where .= " AND `created_at` >= '" . changeDateFormat($this->input->get('from_date')) . "' ";
        }
        
        if($this->input->get('to_date')) {
            $where .= " AND `created_at` <= '" . changeDateFormat($this->input->get('to_date')) . "' ";
        }
        $qs = "SELECT `id`, `name`, `phone`, `email`, `kind`, `message`, `itkunim`, `created_at`, `status` FROM `leads` $where";
        //echo $qs;die();
        $query = $this->db->query($qs);
        $data = $this->dbutil->csv_from_result($query, $delimiter, $newline, $enclosure);
        force_download($filename, $data);
    }
    
    public function status($update_id) {
        $obj = $this->msite->get_object($this->table, $update_id);
        $status = $obj->Arg('status') > 0 ? 0 : 1;
        if($this->msite->update_object($this->table, $update_id, array("status" => $status))) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    public function destroy($delete_id) {
        if($this->msite->delete_object($this->table, $delete_id)) {
            $data['success'] = TRUE;
            $this->output->set_status_header('200');
        } else {
            $data['error'] = $this->db->Error();
            $this->output->set_status_header('500');
        }
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($data));
    }
    
    
    
}