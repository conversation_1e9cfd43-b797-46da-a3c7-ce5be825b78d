import { useState, useCallback } from 'react';

const validateField = (value, rules = {}) => {
  if (!rules) return '';

  if (rules.required && !value) {
    return 'שדה חובה';
  }

  if (rules.minLength && value.length < rules.minLength) {
    return `אורך מינימלי ${rules.minLength} תווים`;
  }

  if (rules.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    return 'כתובת אימייל לא תקינה';
  }

  if (rules.phone && !/^\d{7}$/.test(value)) {
    return 'מספר טלפון לא תקין';
  }

  if (rules.idNumber && !/^\d{9}$/.test(value)) {
    return 'תעודת זהות לא תקינה';
  }

  if (rules.password) {
    const hasNumber = /\d/.test(value);
    const hasLetter = /[a-zA-Z]/.test(value);
    if (!hasNumber || !hasLetter || value.length < 8) {
      return 'סיסמה חייבת להכיל לפחות 8 תווים, מספר ואות';
    }
  }

  if (rules.match && value !== rules.match.value) {
    return rules.match.message || 'הערכים אינם תואמים';
  }

  return '';
};

/**
 * Custom hook for form validation
 * @param {Object} initialValues - Initial form values
 * @param {Object} validationRules - Validation rules for each field
 * @returns {Object} Form state and handlers
 */
const useFormValidation = (initialValues = {}, validationRules = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle field change
  const handleChange = useCallback((name, value) => {
    setValues(prev => ({ ...prev, [name]: value }));
    setTouched(prev => ({ ...prev, [name]: true }));
    
    // Validate field on change
    const error = validateField(value, validationRules[name]);
    setErrors(prev => ({ ...prev, [name]: error }));
  }, [validationRules]);

  // Validate all fields
  const validateForm = useCallback(() => {
    const newErrors = {};
    let isValid = true;

    Object.keys(values).forEach(name => {
      const error = validateField(values[name], validationRules[name]);
      if (error) {
        newErrors[name] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [values, validationRules]);

  // Reset form
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);

  // Handle form submission
  const handleSubmit = useCallback(async (onSubmit) => {
    setIsSubmitting(true);
    
    if (validateForm()) {
      try {
        await onSubmit(values);
      } catch (error) {
        console.error('Form submission error:', error);
        setErrors(prev => ({ 
          ...prev, 
          submit: 'אירעה שגיאה בשליחת הטופס. אנא נסה שוב.' 
        }));
      }
    }
    
    setIsSubmitting(false);
  }, [values, validateForm]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleSubmit,
    resetForm,
    validateForm
  };
};

export default useFormValidation;
