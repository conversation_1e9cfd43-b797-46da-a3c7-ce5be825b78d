/**
 * pdfModules.test.js - בדיקות פשוטות למודולים החדשים
 *
 * קובץ זה מכיל בדיקות בסיסיות שמוודאות שכל המודולים החדשים עובדים יחד כראוי.
 * ניתן להשתמש בו כדוגמה לשימוש במודולים החדשים.
 */

// לבדיקה ידנית בלבד - קובץ זה אינו מיועד לריצה אוטומטית

import { processDocumentContent, generateDocument } from "./pdfOperations";
import { fixDatesInDocument } from "./documentProcessors";
import { adjustSignatureForForm101 } from "./signatureUtils";
import { createGlobalStyle, generatePdfFromElement } from "./pdfUtils";

/**
 * דוגמה לשימוש בממשק processDocumentContent
 */
function testProcessDocumentContent() {
  // דוגמה לקלט HTML
  const sampleHtml = `
    <div>
      <p>תאריך: 01/15/2024</p>
      <p>בס"ד</p>
      <div>תוכן המסמך</div>
    </div>
  `;

  // עיבוד המסמך עם סוג מסמך 101
  const processedHtml = processDocumentContent(
    sampleHtml,
    "101",
    "path/to/logo.png"
  );

  console.log("HTML מעובד:", processedHtml);

  // תוצאה צפויה:
  // - תאריך מתוקן ל-15/01/2024
  // - בס"ד הועבר לראש המסמך
  // - הוסף לוגו בראש המסמך
}

/**
 * דוגמה לשימוש בממשק generateDocument
 */
function testGenerateDocument() {
  // דוגמה לקלט HTML
  const sampleHtml = `
    <div>
      <p>תאריך: 01/15/2024</p>
      <div>תוכן המסמך</div>
    </div>
  `;

  // אובייקט חתימות
  const signatures = {
    logo: "path/to/logo.png",
    yaron: "path/to/yaron.jpg",
    aguda: "path/to/aguda.jpg",
  };

  // יצירת PDF
  generateDocument(
    sampleHtml,
    "101",
    "document.pdf",
    signatures.logo,
    signatures,
    () => console.log("PDF נוצר בהצלחה"),
    (error) => console.error("שגיאה ביצירת PDF:", error)
  );
}

/**
 * הפעלת בדיקות ידניות
 *
 * אפשר להוסיף את השורות הבאות בקומפוננט כלשהו כדי להפעיל את הבדיקות:
 *
 * useEffect(() => {
 *   import('./pdfModules.test').then(module => {
 *     module.runTests();
 *   });
 * }, []);
 */
export function runTests() {
  console.log("--- מריץ בדיקות למודולים PDF ---");

  try {
    console.log("בודק processDocumentContent:");
    testProcessDocumentContent();

    console.log("בודק generateDocument:");
    testGenerateDocument();

    console.log("--- הבדיקות הסתיימו בהצלחה ---");
  } catch (error) {
    console.error("שגיאה בבדיקות:", error);
  }
}
