import React, { useState, useEffect } from "react";
import { getSafeUserData } from "../../../context/AuthContext";
import {
  Button,
  CircularProgress,
  Paper,
  Typography,
  Box,
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import CreditCardIcon from "@mui/icons-material/CreditCard";
import VisibilityIcon from "@mui/icons-material/Visibility";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import "./DigitalCard.scss";

const DigitalCard = () => {
  const [downloadLink, setDownloadLink] = useState(null);
  const [previewLink, setPreviewLink] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [previewError, setPreviewError] = useState(false);

  // Function to create image preview URL from SharePoint download link
  const createImagePreviewUrl = (downloadUrl) => {
    if (!downloadUrl) return null;

    try {
      // For JPG files, we can often use the download URL directly as image src
      // The browser will handle it properly without forcing download in an img tag
      return downloadUrl;
    } catch (error) {
      console.error("Error creating image preview URL:", error);
      return downloadUrl;
    }
  };

  // Function to handle image load error
  const handleImageError = () => {
    setPreviewError(true);
  };

  // Function to open image in new window
  const openImageInNewWindow = () => {
    if (previewLink) {
      window.open(previewLink, "_blank");
    }
  };

  useEffect(() => {
    const fetchDigitalCard = async () => {
      try {
        setLoading(true);
        // Get user data from localStorage
        const userJ = getSafeUserData();
        if (!userJ || !userJ.SessionKey) {
          throw new Error("משתמש לא מחובר");
        }
        // Create request object
        const newSendObj = {
          IDNumber: userJ.IDNO,
          SessionKey: userJ.SessionKey,
        };
        // Determine the base URL from environment variables
        const baseUrl =
          process.env.REACT_APP_ENVIRONMENT === "prod"
            ? process.env.REACT_APP_API_BASE_URL
            : process.env.REACT_APP_API_BASE_URL_DEV;

        const response = await fetch(
          `${baseUrl}/api/v2/Volunteer/digitalCard`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(newSendObj),
          }
        );

        if (!response.ok) {
          throw new Error(`שגיאה בטעינת הכרטיס: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.DownloadLink) {
          setDownloadLink(data.DownloadLink);
          // Create image preview URL
          const preview = createImagePreviewUrl(data.DownloadLink);
          setPreviewLink(preview);
        } else {
          throw new Error("הקישור להורדת הכרטיס אינו זמין");
        }
      } catch (err) {
        console.error("Error fetching digital card:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDigitalCard();
  }, []);

  const handleDownload = () => {
    if (downloadLink) {
      // Open the download link in a new tab
      window.open(downloadLink, "_blank");
    }
  };

  return (
    <div className="digital-card-container">
      <Typography variant="h4" component="h1" gutterBottom align="center">
        כרטיס דיגיטלי
      </Typography>
      {loading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Paper elevation={3} className="error-container">
          <Typography color="error" align="center">
            {error}
          </Typography>
        </Paper>
      ) : (
        <Box className="">
          <div elevation={3} className="">
            {previewLink && !previewError ? (
              <Box className="card-preview-container flex flex-col items-center justify-center">
                <img
                  src={previewLink}
                  alt="כרטיס דיגיטלי"
                  className="card-preview-image"
                  onError={handleImageError}
                  onClick={openImageInNewWindow}
                />
                <Typography
                  variant="caption"
                  align="center"
                  color="textSecondary"
                  sx={{ mt: 1, display: "block" }}
                >
                  לחץ על התמונה לפתיחה בחלון חדש
                </Typography>
              </Box>
            ) : (
              <Box className="card-info-container">
                <CreditCardIcon
                  sx={{ fontSize: 60, color: "#1976d2", marginBottom: 2 }}
                />
                <Typography
                  variant="body1"
                  align="center"
                  color="textSecondary"
                  gutterBottom
                >
                  הכרטיס הדיגיטלי שלך מוכן להורדה
                </Typography>
                <Typography
                  variant="body2"
                  align="center"
                  color="textSecondary"
                >
                  לחץ על הכפתור למטה כדי להוריד את הכרטיס הדיגיטלי שלך
                </Typography>
              </Box>
            )}

            <Box mt={3} textAlign="center">
              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  justifyContent: "center",
                  flexWrap: "wrap",
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<DownloadIcon />}
                  onClick={handleDownload}
                  disabled={!downloadLink}
                >
                  הורד כרטיס דיגיטלי
                </Button>
              </Box>
            </Box>
          </div>
        </Box>
      )}
    </div>
  );
};

export default DigitalCard;
