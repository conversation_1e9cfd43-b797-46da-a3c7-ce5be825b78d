import dayjs from "dayjs";
import "dayjs/locale/he"; // Import Hebrew locale
import hebrewDate from "hebrew-date";
dayjs.locale("he");

//* קובץ זה הוא כלי להתמודדות עם תאריכים בפורמטים שונים  כדי להציג אותם בפורמט אחיד - DD/MM/YYYY
/**
 * Converts MM/DD/YY format to DD/MM/YYYY format
 * @param {string} dateStr - Date string in MM/DD/YY format
 * @returns {string} Formatted date string in DD/MM/YYYY format
 */
export const convertMMDDYYToStandard = (dateStr) => {
  if (!dateStr || dateStr === "---") return dateStr;

  // Handle MM/DD/YY format
  const mmddyyPattern = /(\d{1,2})\/(\d{1,2})\/(\d{2})/;
  if (mmddyyPattern.test(dateStr)) {
    const matches = dateStr.match(mmddyyPattern);
    if (matches && matches.length === 4) {
      const month = matches[1].padStart(2, "0");
      const day = matches[2].padStart(2, "0");
      let year = matches[3];

      // Adjust year (assuming 20xx for brevity)
      if (year.length === 2) {
        year = "20" + year;
      }

      return `${day}/${month}/${year}`;
    }
  }

  return dateStr;
};

/**
 * Formats a date string to DD/MM/YYYY format
 * @param {string} dateStr - Date string in various formats
 * @returns {string} Formatted date string
 */
export const formatDate = (dateStr) => {
  if (!dateStr || dateStr === "---") return "---";

  // First try to convert from MM/DD/YY if it's in that format
  const convertedDate = convertMMDDYYToStandard(dateStr);
  if (convertedDate !== dateStr) {
    return convertedDate;
  }

  // Handle YYYYMMDD format
  if (dateStr.length === 8 && /^\d{8}$/.test(dateStr)) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${day}/${month}/${year}`;
  }

  // Handle ISO date string format
  try {
    return dayjs(dateStr).format("DD/MM/YYYY");
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateStr;
  }
};

/**
 * Formats a date for Form 101
 * Fixes issue with dates not matching assignment period
 * @param {string} dateStr - Date string
 * @returns {string} Formatted date string
 */
export const formatForm101Date = (dateStr) => {
  if (!dateStr) return "";

  // First try to convert from MM/DD/YY format
  const convertedDate = convertMMDDYYToStandard(dateStr);
  if (convertedDate !== dateStr) {
    return convertedDate;
  }

  try {
    // If conversion didn't happen, try to parse as a regular date
    return dayjs(dateStr).format("DD/MM/YYYY");
  } catch (error) {
    console.error("Error formatting Form 101 date:", error);
    return dateStr;
  }
};

/**
 * Formats a date for service period certificate
 * @param {string} dateStr - Date string
 * @returns {string} Formatted date string
 */
export const formatServicePeriodDate = (dateStr) => {
  if (!dateStr) return "";

  // First try to convert from MM/DD/YY format
  const convertedDate = convertMMDDYYToStandard(dateStr);
  if (convertedDate !== dateStr) {
    return convertedDate;
  }

  try {
    // If conversion didn't happen, try to parse as a regular date
    return dayjs(dateStr).format("DD/MM/YYYY");
  } catch (error) {
    console.error("Error formatting service period date:", error);
    return dateStr;
  }
};

/**
 * Formats a date for receipt report
 * @param {string} dateStr - Date string
 * @returns {string} Formatted date string
 */
export const formatReceiptDate = (dateStr) => {
  if (!dateStr) return "";

  // First try to convert from MM/DD/YY format
  const convertedDate = convertMMDDYYToStandard(dateStr);
  if (convertedDate !== dateStr) {
    return convertedDate;
  }

  try {
    // If conversion didn't happen, try to parse as a regular date
    return dayjs(dateStr).format("DD/MM/YYYY");
  } catch (error) {
    console.error("Error formatting receipt date:", error);
    return dateStr;
  }
};

/**
 * חדש: מחזיר את התאריך העברי הנוכחי במבנה מסורתי
 * לדוגמה: י"א באייר תשפ"ד
 * @returns {string} התאריך העברי המפורמט
 */
export const getCurrentHebrewDate = () => {
  try {
    const today = new Date();
    const hd = hebrewDate(today);

    // לוגיקה לטיפול במבנים שונים של האובייקט שחוזר מhebrewDate
    if (hd.toString && typeof hd.toString === "function") {
      // אם יש פונקציית toString, משתמשים בה
      return hd.toString();
    } else if (hd.date && hd.date.toString) {
      // אם יש אובייקט date עם toString
      return hd.date.toString();
    } else {
      // מנסים לבנות את התאריך מהשדות השונים
      let day = "";
      let month = "";
      let year = "";

      // בדיקה לפי סדר עדיפויות של שדות אפשריים
      if (hd.hd) day = hd.hd;
      else if (hd.day) day = hd.day;
      else if (hd.d) day = hd.d;
      else day = "י'"; // ערך ברירת מחדל

      if (hd.hm) month = hd.hm;
      else if (hd.monthName) month = hd.monthName;
      else if (hd.m) month = hd.m;
      else month = "חשון"; // ערך ברירת מחדל

      if (hd.hy) year = hd.hy;
      else if (hd.year) year = hd.year;
      else if (hd.y) year = hd.y;
      else year = 'תשפ"ד'; // ערך ברירת מחדל

      // אם יש שם חודש ללא תווית "ב", נוסיף אותה
      if (month && !month.startsWith("ב")) {
        month = "ב" + month;
      }

      return `${day} ${month} ${year}`;
    }
  } catch (error) {
    console.error("Error getting Hebrew date:", error);
    // אם יש שגיאה, החזר תאריך עברי קשיח כברירת מחדל
    return "כ״ז בסיון תשפ״ד";
  }
};

/**
 * חדש: מחזיר את התאריך הלועזי הנוכחי בפורמט DD/MM/YYYY
 * @returns {string} התאריך הלועזי המפורמט
 */
export const getCurrentGregorianDate = () => {
  return dayjs().format("DD/MM/YYYY");
};

/**
 * חדש: מקבל אובייקט נתונים ומחזיר אובייקט חדש עם נתוני התאריך המעודכנים
 * @param {Object} data - אובייקט נתוני המסמך המקורי
 * @returns {Object} אובייקט נתונים מעודכן
 */
export const enrichDocumentData = (data) => {
  return {
    ...data,
    currentHebrewDate: getCurrentHebrewDate(),
    currentGregorianDate: getCurrentGregorianDate(),
    // פורמט תאריכים קיימים אם יש
    startDate: data.startDate ? formatDate(data.startDate) : "",
    endDate: data.endDate ? formatDate(data.endDate) : "",
    issueDate: data.issueDate
      ? formatDate(data.issueDate)
      : getCurrentGregorianDate(),
    periodStart: data.periodStart ? formatDate(data.periodStart) : "",
    periodEnd: data.periodEnd ? formatDate(data.periodEnd) : "",
  };
};
