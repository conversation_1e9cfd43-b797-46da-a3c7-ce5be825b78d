import React from 'react';
import SmallForm from './SmallForm';
import ForgotPassword from './ForgotPassword';
import ForgotPasswordNew from './ForgotPasswordNew';
import Login from './Login';
import LoginQueryUrl from './LoginQueryUrl';

const RegisterIndex = ({ page, ...props }) => {
  console.log("RegisterIndex rendering with page:", page, "and props:", props);

  const renderComponent = () => {
    console.log("RegisterIndex.renderComponent called with page:", page);
    
    switch (page) {
      case 'register':
        console.log("Rendering SmallForm component");
        return <SmallForm {...props} />;
      case 'reSendPass':
        console.log("Rendering ForgotPassword component");
        return <ForgotPassword {...props} />;
      case 'reSendPassNew':
        console.log("Rendering ForgotPasswordNew component");
        return <ForgotPasswordNew {...props} />;
      case 'login':
        console.log("Rendering Login component");
        return <Login {...props} />;
      case 'loginQueryUrl':
        console.log("Rendering LoginQueryUrl component");
        return <LoginQueryUrl {...props} />;
      default:
        console.log("No matching component found for page:", page);
        return <div>Page not found: {page}</div>;
    }
  };

  const component = renderComponent();
  console.log("RegisterIndex returning component for page:", page);
  
  return (
    <div className="clear">
      {component}
    </div>
  );
};

export default RegisterIndex;
