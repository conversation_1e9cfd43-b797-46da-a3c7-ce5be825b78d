<!--https://chabad.ak-digital.co.il/api/welcome/helpRequestIframe-->

<div class="wrapper <?php echo $this->input->get('mobile')==1 ? 'mobile' : ''; ?>">
    <div class="form-wrapper">
        
        <?php echo form_open(base_url('welcome/sendHelpRequestMail'.getQS()), array("enctype" => "multipart/form-data", "class" => "", "onsubmit" => "return validate_form(this);")); ?>
        
        <div class="line">
            <input type="text" class="iname" value="<?php echo set_value('name'); ?>"  name="name" aria-label="שם מלא " placeholder="שם מלא " data-required="נא להזין שם מלא ">
            <input type="tel" class="iname"  value="<?php echo set_value('phone'); ?>" name="phone" aria-label="מספר טלפון" placeholder="טלפון" data-required="נא להזין טלפון תקין">
            <input type="email" class="iname" value="<?php echo set_value('email'); ?>" name="email" aria-label="דואר אלקטרוני" placeholder="דואר אלקטרוני" data-required="נא להזין מייל תקין">       
        </div>
        
        <div class="line line2">
            <input type="text" class="iname" value="<?php echo set_value('place'); ?>"  name="place" aria-label="ישוב" placeholder="ישוב" data-required="נא להזין ישוב">
            <select class="iselect" name="target">
                <option selected="" value="">בחר סוג העזרה המבוקשת</option>
                <?php foreach ($categories as $value): ?>
                <option value="<?php echo $value->Arg('id');?>"><?php echo $value->Arg('title');?></option>
                <?php endforeach;?>
<!--                <option value="אחר">אחר</option>-->
            </select>
        </div>
        
        <section class="uploadFiles">
            
            <div class="tableUpload">
                
                <div class="text col">
                    <img class="v" src="<?php echo base_url() . icIMG . 'v.svg?v=' . VERSION; ?>" />
                    <p>
                        <?php echo $page->Text('textVIframe'); ?>
                    </p>
                    
                </div>
                
                <figure class="col">
                    <img id="picPreview" src="<?php echo base_url() . icIMG . 'picture.svg?v=' . VERSION; ?>" />
                </figure>
                
                <div class="col uploadBtn">
<!--                    multiple -->
                    <!--<input id="fileupload" type="file" class="hidden" multiple="" name="userfile[]" onchange="inputChange(this, '#fileuploadname');">-->
                    <input id="fileupload" type="file" value="" class="hidden" name="userfile"  onchange="inputChange(this, '#fileuploadname');">
                    <!--from alexanderg-->
                    <button role="button" aria-label="צרף קובץ קו״ח" type="button" class="ifile" onclick="performClick('fileupload');">
                        <img alt="העלאה קובץ" src="<?php echo base_url() . icIMG . 'uploadBTN.svg?v=' . VERSION; ?>" />
<!--                        <span>צרף קו״ח</span>-->
                    </button>
                    <div style="clear: both"></div>
                    <span id="fileuploadname"></span>
                </div>
                        
                
            </div>
            
            
            
        </section>
        
        <section class="forWho">
            
            <div class="question1">
                
                <h2>עבור מי העזרה?</h2>
                
                <div class="checkbox">
                    <input class="checkCkecked" type="radio" id="radio1" value="עבורי" name="forWho" >
                    <label for="radio1">עבורי</label>
                </div>
                
                <div class="checkbox">
                    <input class="checkCkecked" type="radio" id="radio2" value="אדם אחר" name="forWho" >
                    <label for="radio2">אדם אחר</label>
                </div>
                
            </div>
            
            
            
            
            <div class="question2 disabled">
                
                <h2>קיבלת את הסכמתו?</h2>
                
                <div class="checkbox">
                    <input class="fields" type="radio" id="radio3" disabled="" value="כן" name="ishur" >
                    <label for="radio3">כן</label>
                </div>
                
                <div class="checkbox">
                    <input class="fields" type="radio" id="radio4" disabled="" value="לא" name="ishur" >
                    <label for="radio4">לא</label>
                </div>
                
            </div>
            
        </section>

        <div class="checkbox clear" style="clear: both;margin: 20px 0;width: 100%;">
            <input  data-required="חייב לאשר את התקנון" type="checkbox" id="checkboxTaka" value="1" name="itkunim" >
            <?php $seo = $pages['welcome_takanon']->Arr('seo'); ?>
            <label style="margin-right: 20px;" for="checkboxTaka">אני מאשר/ת את  <a href="<?php echo str_replace('/api','',base_url().$seo->Arg('friendly')); ?>" target="_blank"> תקנון האתר</a></label>
        </div>
        
        <div class="line last hidePerson">
            <h2>פרטי האדם הזקוק לעזרה</h2>
            <input type="text" class="iname" value="<?php echo set_value('nameHelp'); ?>"  name="nameHelp" aria-label="שם מלא " placeholder="שם מלא " >
            <input type="tel" class="iname"  value="<?php echo set_value('phoneHelp'); ?>" name="phoneHelp" aria-label="מספר טלפון" placeholder="טלפון" >
            
            
            
            
            <button class="send_btn" type="submit">שליחה</button>
            
            
        </div>
            
        <input type="hidden" name="kind" value="זקוקים לעזרה">

        <?php echo form_close(); ?>
        
    </div>
</div>

<!--   SCRIPT ON SITE.JS  !!!!!-->