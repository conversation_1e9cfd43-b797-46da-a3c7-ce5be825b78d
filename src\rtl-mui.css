/* Simple RTL styling for Material UI components */
.rtl-input {
  direction: rtl !important;
  text-align: right !important;
}

.rtl-mui-input {
  margin: 10px 0 !important;
}

.rtl-mui-input .MuiInputBase-input {
  text-align: right !important;
  direction: rtl !important;
  padding-right: 14px !important;
  height: 1.4em !important;
  padding-top: 25px !important;
  padding-bottom: 10px !important;
}

.rtl-mui-input .MuiInputLabel-root {
  right: 14px !important;
  left: auto !important;
  transform-origin: top right !important;
  margin-right: 0 !important;
}

.rtl-mui-input .MuiInputLabel-formControl {
  transform: translate(-14px, 16px) scale(1) !important;
}

.rtl-mui-input .MuiInputLabel-shrink {
  transform: translate(0, -6px) scale(0.75) !important;
  transform-origin: top right !important;
}

.rtl-mui-input .MuiSelect-icon {
  right: auto !important;
  left: 10px !important;
  top: calc(50% - 0.5em) !important;
}

.rtl-mui-input .MuiOutlinedInput-notchedOutline {
  text-align: right !important;
  padding-right: 8px !important;
}

.rtl-mui-input .MuiFormHelperText-root {
  text-align: right !important;
  margin-right: 14px !important;
  margin-left: 0 !important;
}

.rtl-mui-input .MuiOutlinedInput-root {
  padding-right: 0 !important;
  height: 56px !important;
  display: flex !important;
  align-items: center !important;
}

.rtl-mui-input .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2 !important; 
}

.rtl-mui-input .MuiInputAdornment-root {
  margin-right: auto !important;
  margin-left: 8px !important;
  height: 100% !important;
  max-height: 56px !important;
  display: flex !important;
  align-items: center !important;
}

.rtl-mui-input .MuiIconButton-root {
  padding: 8px !important;
}

.rtl-mui-input .MuiOutlinedInput-root .MuiInputAdornment-positionEnd {
  margin-left: 8px !important;
}

.rtl-mui-input:focus-within .MuiInputLabel-root {
  color: #1976d2 !important;
}

/* Support for link styling */
.rtl-mui-input + a {
  display: block !important;
  text-align: right !important;
  margin-top: 4px !important;
  margin-bottom: 16px !important;
} 
