<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Documents extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('token'));
           if( $postCode['token'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    
    public function getDocummentPostJSONOLD ($jPData = FALSE) {
        
        
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('IDNumber'));
        
        $output = $jsonPosts;
        
        $this->data = $output;
        //if($output['error']) {$header = '400'; };
        
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    public function getDocummentPost ($jPData = FALSE) {
        
        //echo "<pre>";
        //print_r($_FILES);
        //die('end');
        
        //$output = $this->input->post();
        
        $config = array();
        $config['upload_path']          = appFILES.'documents';
        $config['allowed_types']        = 'jpg|jpeg|png|tif';
        $config['file_name']            = uniqid();
        $config['max_size']             = 5500;
        //$config['max_width']            = 1024;
        //$config['max_height']           = 768;


        $this->load->library('upload', $config);
        $this->upload->initialize($config, TRUE);

        $field['name'] = 'filename';

        if($this->upload->do_upload($field['name'])) {
            
            $filedata = $this->upload->data(); 

            if($filedata['file_name']) {

                //$output['filename'] = $filedata['file_name'];
                
                
                $path = appFILES.'documents/'.$filedata['file_name'];
                $type = pathinfo($path, PATHINFO_EXTENSION);
                $data = file_get_contents($path);
                $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
                //$output['base64'] = $base64;
                
                //delette File
                //unlink( $path );
                //die('-END-');
                
                $pagePosts = array (
                    
                    'IDNumber' => $this->input->post('IDNumber'),
                    'SessionKey1' => $this->input->post('SessionKey'),
                    'FormID' => $this->input->post('FormID'),
                    'Photo' => $base64
                    
                );
                
                $output['sent'] = $pagePosts;
                
                $output['responseClient'] = $this->sherutLeumi->ApiClient($url = 'v1/volunteer/forms/send', $pagePosts);

                
            }
            
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
     // NOT IN USE!!! NOT IN USE!!! NOT IN USE!!!
    public function getBase64NO ($jPData = FALSE) { 
        
        //echo "<pre>";
        //print_r($_FILES);
        //die('end');
        $datesData = $this->getDatesData();
        
        if( isset($datesData['SikDays']) && $datesData['SikDays'] <= 0 ) {
            
            $output['error'] = 'שגיאה בבחירת התאריכים באישור מחלה';
            $this->data = $output;
         
            return $this->output
                    ->set_status_header(!isset($header) ? 200 : $header)
                    ->set_content_type('application/json')
                    ->set_output(json_encode($this->data));
            
        }
        
        
        if(!isset($_FILES['filename'])) {
            
            $output['dates'] = $datesData;
            $this->data = $output;
         
            return $this->output
                    ->set_status_header(!isset($header) ? 200 : $header)
                    ->set_content_type('application/json')
                    ->set_output(json_encode($this->data));
            
        }
        
        
        $config = array();
        $config['upload_path']          = appFILES.'documents';
        $config['allowed_types']        = 'jpg|jpeg|png|tif';
        $config['file_name']            = uniqid();
        $config['max_size']             = 5500;
        //$config['max_width']            = 1024;
        //$config['max_height']           = 768;


        $this->load->library('upload', $config);
        $this->upload->initialize($config, TRUE);

        $field['name'] = 'filename';

        if($this->upload->do_upload($field['name'])) {
            
            $filedata = $this->upload->data(); 

            if($filedata['file_name']) {

                //$output['filename'] = $filedata['file_name'];
                
                
                $path = appFILES.'documents/'.$filedata['file_name'];
                $type = pathinfo($path, PATHINFO_EXTENSION);
                $data = file_get_contents($path);
                $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
                
                $extentionFile = $type;
                $typeFile = 'image/' . $type;
                
                //$output['base64'] = $base64;
                
                //delette File
                //unlink( $path );
                //die('-END-');
                
                $pagePosts = array (
                    
                    'base64' => $base64,
                    'extentionFile' => $extentionFile,
                    'typeFile' => $typeFile,
                    'datesData' => $datesData
                    
                );
                
                $output['fileData'] = $pagePosts;
                
            }
            
        } else {
            
            $output['error'] = 'שגיאה בהעלאת הקובץ';
            
        }
        
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function getDates ($jPData = FALSE) { //READ FROM FRONT
        
        //echo "<pre>";
        //print_r($_FILES);
        //die('end');
        $datesData = $this->getDatesData();
        
        if( isset($datesData['SikDays']) && $datesData['SikDays'] <= 0 ) {
            
            $output['error'] = 'שגיאה בבחירת התאריכים';
            
            $this->data = $output;
         
            return $this->output
                    ->set_status_header(!isset($header) ? 200 : $header)
                    ->set_content_type('application/json')
                    ->set_output(json_encode($this->data));
            
        }
        
        if(isset($datesData['errorMonth'])) {
                
            $output['warning'] = 'יש לשים לב ש'.'התאריכים שהוזנו לא שייכים לאותו החודש';

        }
        
        $output['ok'] = '1';
        $output['dates'] = $datesData;
        $this->data = $output;

        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));

        
    }
    
    
    
    public function getDatesData() { //Funcion Only
        
        
        $SikDateStart = $this->input->post('StartDate');
        $SikDateEndDate = $this->input->post('EndDate');
        
        
        if( empty($SikDateStart) || empty($SikDateEndDate) ) {
            
            return array();
            
        } else {
            
            $day1= new DateTime($SikDateStart);
            $day2= new DateTime($SikDateEndDate);
            $diff = $day1->diff($day2);
            
            //echo "<pre>";
            //print_r($diff);die();
            
            $SikDays = $diff->days + 1;
            if($diff->invert == '1') {$SikDays = 0;}
            
            //$start_date = new DateTime($sayeret['created_at']);
            //$since_start = $start_date->diff(new DateTime());
                    
            $return = array(
                'SikDate' => changeDateFormat($SikDateStart, $stFormatFrom = "Y-m-d", $stFormatTo = "Ymd"),
                'SikDays' => $SikDays,
                'ok' => '1'
            );
            
            if (
                changeDateFormat($SikDateStart, $stFormatFrom = "Y-m-d", $stFormatTo = "m") !==
                changeDateFormat($SikDateEndDate, $stFormatFrom = "Y-m-d", $stFormatTo = "m")
                ) {
            
            $return['errorMonth'] = '1';
            
            }
            
            return $return;
            
        }
        
        
    }
    
}