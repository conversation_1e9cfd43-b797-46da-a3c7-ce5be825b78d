# Sherut Leumi Application

This repository contains the React code for the She<PERSON>t Leumi web application. It provides volunteers with a modern interface to manage their national service data. The project is based on Create React App and uses Material&nbsp;UI alongside Tailwind CSS for styling.

## Setup

### Prerequisites

- Node.js 22 or later
- npm or Yarn

### Installation

Clone the repository and install the dependencies:

```bash
git clone <repository-url>
cd sherutLeumi-main
npm install        # or `yarn install`
```

### Environment variables

Configuration is controlled through environment variables. Copy the provided `.env` file to `.env.local` (or create a new `.env.local`) and adjust values as needed:

```bash
cp .env .env.local
```

Important keys include:

```
REACT_APP_ENVIRONMENT=dev
REACT_APP_API_BASE_URL=https://example.com/api/
REACT_APP_API_BASE_URL_DEV=https://dev.example.com/api/
REACT_APP_SEASON_TOGGLE=on
```

Set `REACT_APP_ENVIRONMENT` to `prod` and update the API URLs for production. Options such as `HOST` and `GENERATE_SOURCEMAP` can also be configured here.

## Development

Start the development server with:

```bash
npm start
# or
yarn start
```

Source code lives under `src/`. Pages are in `src/Pages` and reusable components are in `src/Components`. Keep new code organized in these directories and use Tailwind classes for styling.

## Running tests

Unit tests are written with `@testing-library/react`. Run all tests using:

```bash
npm test
# or
yarn test
```

Add `--watchAll=false` when running tests in CI environments.

## Building for production

Create an optimized build with:

```bash
npm run build
```

## Contributing

- Follow the existing code style and project structure.
- Keep commits focused and descriptive.
- Ensure tests pass before opening a pull request.
