import React from "react";
import { Routes, Route } from "react-router-dom"; // removed BrowserRouter
import PrintView from "../../Pages/PrintView/PrintView";
import UserConsoleIndex from "../../Pages/UserConsole/UserConsoleIndex";
import DigitalCard from "../../Pages/UserConsole/DigitalCard/DigitalCard";

const PagesUserConsole = (props) => {
  // Add debug logging to see what's happening
  console.log("PagesUserConsole rendered with props:", props);

  return (
    <React.Fragment>
      <Routes>
        <Route
          path="search"
          element={<UserConsoleIndex {...props} page="userSearch" />}
        />
        <Route
          path="data"
          element={<UserConsoleIndex {...props} page="userData" />}
        />
        <Route
          path="files"
          element={<UserConsoleIndex {...props} page="files" />}
        />
        <Route
          path="printview"
          element={<PrintView {...props} page="files" />}
        />
        <Route
          path="clockInOut"
          element={<UserConsoleIndex {...props} page="clockInOutIndex" />}
        />
        <Route
          path="training"
          element={<UserConsoleIndex {...props} page="training" />}
        />
        <Route
          path="sorties"
          element={<UserConsoleIndex {...props} page="sorties" />}
        />
        <Route
          path="editAvatar"
          element={<UserConsoleIndex {...props} page="editAvatar" />}
        />
        <Route
          path="contactUs"
          element={<UserConsoleIndex {...props} page="contactUs" />}
        />
        <Route
          path="digitalCard"
          element={<UserConsoleIndex {...props} page="digitalCard">
            <DigitalCard />
          </UserConsoleIndex>}
        />
        <Route
          path="/"
          element={<UserConsoleIndex {...props} page="userIndex" />}
        />
      </Routes>
    </React.Fragment>
  );
};

export default PagesUserConsole;
