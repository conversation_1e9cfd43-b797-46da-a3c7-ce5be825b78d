/**
 * ExternalLink - קומפוננט לטיפול בקישורים חיצוניים
 *
 * מציג כפתור שפותח קישור חיצוני (למשל לדיווח תקלה או שאלון)
 *
 * Props:
 * - formData: נתוני הטופס כולל ה-URL
 * - texts: טקסטים להצגה
 */
import React, { useState } from "react";
import v from "../../../img/sherut-leumi/svg/files/v.svg";
import link from "../../../img/sherut-leumi/svg/files/link.svg";
import { Modal } from "react-bootstrap";
import infoModal from "../../../img/sherut-leumi/svg/files/infoModal.svg";
import { Button } from "@mui/material";
import { Grid } from "@mui/material";
import LaunchIcon from "@mui/icons-material/Launch";

export default function ExternalLink({ formData, texts }) {
  const [showModal, setShowModal] = useState(false);

  const handleOpenModal = () => setShowModal(true);
  const handleCloseModal = () => setShowModal(false);

  const handleGoToUrl = () => {
    setShowModal(false);
    const url = formData?.URL || "#";
    window.open(url, "_blank");
  };

  const isDocumentExists = formData.Status !== "Missing";

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-4">
      <div className="mb-4">
        <h3 className="text-xl font-semibold mb-2 flex items-center gap-2">
          <img src={link} alt="link" className="w-6 h-6" />
          <span>{texts.title}</span>
        </h3>
        <p className="text-gray-600">{texts.filesType}</p>
      </div>

      {isDocumentExists ? (
        <div className="bg-green-50 p-6 rounded-lg flex flex-col items-center justify-center">
          <img src={v} alt="המסמך קיים במערכת" className="w-12 h-12 mb-4" />
          <p className="text-green-800 text-center">המסמך קיים במערכת</p>
        </div>
      ) : (
        <div className="bg-blue-50 p-6 rounded-lg flex flex-col items-center justify-center">
          <p className="text-blue-800 text-center mb-4">(לינק חיצוני)</p>
          <Button variant="contained" color="primary" onClick={handleOpenModal}>
            <span className="mr-2">{texts.btnName}</span>
            <LaunchIcon />
          </Button>
        </div>
      )}

      <Modal
        show={showModal}
        onHide={handleCloseModal}
        animation={false}
        aria-labelledby="external-link-modal"
        centered
        className="rounded-lg"
      >
        <Modal.Body className="p-6">
          <Button
            onClick={handleCloseModal}
            variant="text"
            className="absolute top-2 right-2"
          >
            X
          </Button>

          <div className="flex flex-col items-center text-center mb-6">
            <img
              src={infoModal}
              alt={texts.startText}
              className="w-16 h-16 mb-4"
            />
            <h2 className="text-xl font-semibold mb-2">{texts.startText}</h2>
            {texts?.startTextP && (
              <p className="text-gray-600">{texts.startTextP}</p>
            )}
          </div>

          <Grid
            container
            spacing={2}
            alignItems="center"
            direction="row"
            justifyContent="center"
          >
            <Grid item md={6}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleGoToUrl}
                fullWidth
              >
                {texts.btnName}
                <LaunchIcon />
              </Button>
            </Grid>
          </Grid>
        </Modal.Body>
      </Modal>
    </div>
  );
}
