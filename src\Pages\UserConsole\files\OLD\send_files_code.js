/* eslint-disable no-undef */

haagudApp.controller('formsCtrl', ['$scope', '$rootScope', '$timeout', '$state', 'server', 'generalDetails', 'camera', 'fileManager', function ($scope, $rootScope, $timeout, $state, server, generalDetails, camera, fileManager) {
    
    
    // $scope.gender = generalDetails.getUserGender();
     var fromIdForSend = "";
 
     $scope.sikDays = null;
     $scope.sikDate = null;
 
     $scope.sikDateChange = function (event) {
         if(event.target.value){
         if (event && event.target) {
             var date = new Date(event.target.value);
             $scope.sikDate = date;
         }
       } else $scope.sikDate = !$scope.sikDate;
 
       if($scope.sikDate != null && $scope.sikDays != null){
 
         var time_difference = $scope.sikDays.getTime() - $scope.sikDate.getTime();  
         $scope.days_difference = time_difference / (1000 * 60 * 60 * 24) + 1 ;  
 
                 if($scope.days_difference < 30 ){
                    $('#less_30_days').html("*לתשומת ליבך! מספר הימים באישור הוא " + $scope.days_difference);
                    $('#more_30_days').html("");
                 }
                 
                 else  if($scope.days_difference >= 30 ){
                    $('#more_30_days').html("*לתשומת ליבך! מספר הימים באישור הוא " + $scope.days_difference);
                    $('#less_30_days').html("");
                 }
                 
         }
 
             var firstMonth = $scope.sikDate.getMonth() + 1
             var secMonth = $scope.sikDays.getMonth() + 1
             if(firstMonth != secMonth){
               $('#equalMonthsErrorAlert').modal('show');
             }
 
     }
 
     $scope.sikDaysChange = function (event) {
         if(event.target.value){
         if (event && event.target) {
             var days = new Date(event.target.value);
             $scope.sikDays = days;
              
            }
          }else $scope.sikDays = !$scope.sikDays; 
 
          if($scope.sikDate != null && $scope.sikDays != null){
 
             var time_difference = $scope.sikDays.getTime() - $scope.sikDate.getTime();  
             $scope.days_difference = time_difference / (1000 * 60 * 60 * 24) + 1 ;  
 
                     if($scope.days_difference < 30 ){
                         $('#less_30_days').html("*לתשומת ליבך! מספר הימים באישור הוא " + $scope.days_difference);
                         $('#more_30_days').html("");
                     }
                     else  if($scope.days_difference >= 30 ){
                          $('#more_30_days').html("*לתשומת ליבך! מספר הימים באישור הוא " + $scope.days_difference);
                          $('#less_30_days').html("");
                     }
                    
             }
 
             var firstMonth = $scope.sikDate.getMonth() + 1
             var secMonth = $scope.sikDays.getMonth() + 1
             if(firstMonth != secMonth){
               $('#equalMonthsErrorAlert').modal('show');
             }
         
     }
 
     $scope.CleanFields = function(){
         $('#less_30_days').html("");
         $('#more_30_days').html("");
     }
 
 
     // $scope.test = function () {
     //     if ($scope.fromid === 10) {
 
     //         var _day = $scope.sikDate.getDate() + '';
     //         var _year = $scope.sikDate.getFullYear() + '';
     //         var _month = $scope.sikDate.getMonth() + 1;
     //         if (_month < 10) {
     //             _month = '0' + _month;
     //         } else {
     //             _month = _month + '';
     //         }
     //         var sikDate = _year + _month + _day;
 
     //         var metaData = {
     //             IDNumber: localStorage["userId"],
     //             FormID: fromIdForSend,
     //             SessionKey: localStorage["userSessionKey"],
     //             SikDate: sikDate,
     //             SikDays: $scope.sikDays
     //         }
     //         console.log(metaData)
     //     }
     // }
 
     $scope.myImage = "";
     $scope.missingForms = 0;
     $scope.ListForms = [];
     //ID aligns with array position, which is why some of these are blank (they are unused IDs)
     $scope.txtfromID = [
         '',
         'צילום ת"ז + ספח נשלח בהצלחה',
         '',
         '',
         'תמונת הפנים נשלחה בהצלחה',
         'אישור רפואי נשלח בהצלחה',
         'פטור מהצבא נשלח בהצלחה',
         '',
         '',
         '',
         'אישור המחלה נשלח בהצלחה ויעודכן עבור',
         'אישור ניהול חשבון בנק נשלח בהצלחה',
         '',
         '',
         'אישור אפוטרופוס נשלח בהצלחה',
         '',
         '',
         '',
         'אישור עבודה נשלח בהצלחה',
         'אישור לימודים נשלח בהצלחה',
         'א.מוכרות במנהל מוגבלויות נשלח בהצלחה',
         'אבחון פסיכולוגי נשלח בהצלחה',
         'א.רפואי נשלח בהצלחה',
         'קצבת ביטוח לאומי נשלח בהצלחה',
         'וועדת השמה נשלח בהצלחה',
         'דוח תפקודי נשלח בהצלחה',
         'א.מוכרות ברווחה נשלח בהצלחה',
         'דו"ח סוציאלי נשלח בהצלחה',
         'תסקיר קצין מבחן נשלח בהצלחה'
     ];
     $scope.fromid;
     $scope.goToProfile = function () {
         $state.transitionTo('profile');
     }
 
     $scope.filterForme = function (form) {
         //        if(form.FormID==4)
         //       	 {
         //        	
         //        	$scope.uplodImg=true;   
         //        	return false;
         //       	 }
         //        else
         if (form.FormID == 7) {
             $scope.bunkDitel = form;
             // $scope.updetBankDetail = true;
 
             return false;
         }
         return true;
     }
 
     var link = "";
     
     $scope.getListForms = function (status) {
 
         $scope.request = {
             SessionKey: localStorage.getItem("userSessionKey"),
             IDNumber: localStorage.getItem("userId"),
             RequestID: 'fdh6758' //ms
         }
 
         server.request('/volunteer/forms/list', $scope.request, true)
             .then(function (data) {
                 if (data.Result == 'Success') {
                     $scope.ListForms = data.Forms;
                     for (i = 0; i < data.Forms.length; i++) {
                         //constants!
                         if (data.Forms[i].Status == 'Missing') {
                             if(data.Forms[i].FormName == 'אישור עבודה' || data.Forms[i].FormName == 'אישור לימודים') continue;
                             $scope.missingForms++;
                         }
 
                         if(data.Forms[i].FormType == 'Link'){
                             link = data.Forms[i].URL;
                         }
 
                     }
 
                 }
 
 
             })
 
     }
     
     $scope.getListForms();
 
     $scope.goToRegulations = function (form) {
         $state.transitionTo('regulations', { formsId: form.FormID, status: form.Status });
 
     }
 
 
     
 
     $scope.openCameraModal = function (fromID, Status) {
         //$rootScope.showLoader = true;
         $scope.fromid = fromID;
         if (fromID == 4 && Status == 'Exist') {
 
             $('#imageExists').modal('show');
 
         }
         else {
             setTimeout(function () {
 
                 $('#slide-bottom-popup').modal('show');
             }, 250); // milliseconds
             $scope.sendButton = false;
             fromIdForSend = fromID;
             if (fromID == 4) {
                 $scope.enabelCrop = true;
             }
             else {
                 $scope.enabelCrop = false;
             }
         }
     };
 
 
     $scope.iGet = function(fromID){
         $scope.fromid = fromID;
         if($scope.fromid == 4)
         $('#TrainingModal').modal('show');
         if($scope.fromid == 1)
         $('#IDModal').modal('show');
         if($scope.fromid == 11)
         $('#bankManagment').modal('show');
         if($scope.fromid == 5)
         $('#medicalConfirm').modal('show');
         if($scope.fromid == 14)
         $('#apotropusConfirm').modal('show');
         if($scope.fromid == 6)
         $('#freeArmy').modal('show');
     }
     
     
     $scope.closeModal = function (fromID) {
         $scope.fromidClose = fromID
         if($scope.fromidClose == 4)
         $('#TrainingModal').modal('hide');
         if($scope.fromidClose == 1)
         $('#IDModal').modal('hide');
         if($scope.fromidClose == 11)
         $('#bankManagment').modal('hide');
         if($scope.fromidClose == 5)
         $('#medicalConfirm').modal('hide');
         if($scope.fromidClose == 14)
         $('#apotropusConfirm').modal('hide');
         if($scope.fromidClose == 6)
         $('#freeArmy').modal('hide');
     }
 
 
 
 
 
     $scope.myImage = '';
     $scope.myCroppedImage = '';
     $scope.croppedImage = '';
     $scope.infoDate = {}
 
     //camera
     $scope.uploadImg = function () {
         $rootScope.showLoader = true;
         $scope.request = {
             SessionKey: localStorage.getItem("userSessionKey"),
             IDNumber: localStorage.getItem("userId"),
             RequestID: 'fdh6758',
             Photo: $scope.myCroppedImage.slice(23)
         }
               
         server.request('/volunteer/photo', $scope.request, true)
             .then(function (data) {
                 $rootScope.showLoader = false;
                 $scope.showPopu();
                 $scope.croppedImage = $scope.myCroppedImage;
                 $scope.myImage = '';
                 $scope.sendButton = false;
                 for (i = 0; i < $scope.ListForms.length; i++) {
                     if ($scope.ListForms[i].FormID == fromIdForSend) {
                         // $scope.ListForms[i].Status = "Pending";
                     }
                 }
  
             })
 
           $rootScope.showLoader = false;
     }
     $scope.getImage = function () {
         $rootScope.showLoader = true;
         // camera.getAlbomImage()
         //     .then(function (imageData) {
         //         $scope.sendButton = true;
         //         $scope.myImage = imageData;
         //         $scope.show = false;
         //     });
 
         fileManager.openFile()
             .then(function (myFile) {
                 $scope.myImage = ''
                 $scope.sendButton = true;
                 $scope.show = false;
 
                 if (myFile.ext !== 'pdf')
                     $scope.myImage = myFile.base64File
                 $scope.myFile = myFile
             })
             .catch(function (err) {
 
             })
 
         $timeout(function () {
             $scope.showIcon = false;
             $rootScope.showLoader = false;
         }, 0);
     }
 
     $scope.captureImage = function () {
         camera.captureImageBase64()
             .then(function (imageData) {
 
                 $timeout(function () {
                     $('#slide-bottom-popup').modal('show');
                 }, 250).then(function () {
 
                     $timeout(function () {
                         $scope.sendButton = true;
                         $scope.myImage = imageData;
 
                     }, 250);
                 });
 
             });
     }
     $scope.showPopu = function () {
         $("#PhotoUploadedSuccessfully").modal('show');
     }
 
     $scope.CameraModalClosed = function () {
         $scope.fromid = 0;
         $scope.sendButton = false;
         $scope.myImage = '';
         $scope.myFile = null;
         $scope.sikDate = null;
         $("#_sikDays").val($scope.sikDate)
         $scope.sikDays = null;
         $("#_sikDate").val($scope.sikDays)
     }
 
     //	document.addEventListener("deviceready", onDeviceReady, false);
     //	function onDeviceReady() {
     //	    console.log(FileTransfer);
     //	    alert(FileTransfer);
     //	} 
 
 
     $scope.sendImg = function () {
 
          //Clean fields 
          $scope.CleanFields();
   
          $rootScope.showLoader = true; 
         if (fromIdForSend != 4) {
             try {
 
                 //debugger
                 if ($scope.fromid === 10 && (!$scope.sikDays || !$scope.sikDate)) {
 
                     $scope.CameraModalClosed();
                     $("#missingSikDetails").modal('show');
                     $rootScope.showLoader = false; 
                     return;
 
                 }
 
                // $rootScope.showLoader = true; 
                 
                 function win(r) {
 
                     $scope.myImage = '';
                     $scope.sendButton = false;
                     $scope.myFile = null;
                     $scope.sikDate = null;
                     $("#_sikDays").val($scope.sikDate)
                     $scope.sikDays = null;
                     $("#_sikDate").val($scope.sikDays)
 
                     // console.log("Code = " + r.responseCode);
                     // console.log("Response = " + r.response);
                     // console.log("Sent = " + r.bytesSent);
                     // console.log("Sent = " + r.bytesSent);
                     var res = JSON.parse(r.response);
 
                     if (res.PasswordStatus == "Valid") {
                         
                         $rootScope.showLoader = false;
                         //alert("קובץ נשלח בהצלחה");
                         //$scope.sendButton=false;
                         $scope.showPopu();
                         $timeout(function () {
                             $scope.closButton = false
                             for (i = 0; i < $scope.ListForms.length; i++) {
                                 if ($scope.ListForms[i].FormID == fromIdForSend) {
                                     // $scope.ListForms[i].Status = "Pending";
                                 }
                             }
                             
                             // Refresh the files state after successful upload
                             $scope.getListForms();
                             
                             // Make a server request for info data
                             var infoRequest = {
                                 SessionKey: localStorage.getItem("userSessionKey"),
                                 IDNumber: localStorage.getItem("userId"),
                                 RequestID: 'info_' + new Date().getTime()
                             };
                             
                             server.request('/volunteer/info', infoRequest, true)
                                 .then(function(infoData) {
                                     // Handle info data if needed
                                     console.log("Info data refreshed");
                                 });
                         }, 0)
 
                     }
                     
                 }
 
                 function fail(error) {
                     // $rootScope.showLoader = false;
                     $scope.myImage = '';
                     $scope.sendButton = false;
                     $scope.myFile = null;
                     $scope.sikDate = null;
                     $("#_sikDays").val($scope.sikDate)
                     $scope.sikDays = null;
                     $("#_sikDate").val($scope.sikDays)
                     //alert("An error has occurred: Code = " + error.code);
                     // console.log("upload error source " + error.source);
                     // console.log("upload error target " + error.target);
                 }
 
                 var uri = encodeURI(domain + "/volunteer/forms/send");
 
                 var options = new FileUploadOptions();
                 options.fileKey = "myFile";
                 if ($scope.myFile) {
                     options.fileName = new Date().getTime() + '.' + $scope.myFile.ext;
                 } else {
                     options.fileName = new Date().getTime() + ".jpg";
                 }
                 options.httpMethod = "POST";
                 options.mimeType = "multipart/form-data";
 
                 metaData = {
                     IDNumber: localStorage["userId"],
                     FormID: $scope.fromid,
                     SessionKey: localStorage["userSessionKey"]
                 };
 
                 if ($scope.fromid === 10) {
                    
                     var _day = $scope.sikDate.getDate() + '';
                     var _year = $scope.sikDate.getFullYear() + '';
                     var _month = $scope.sikDate.getMonth() + 1;
 
                     if (_month < 10) {
                         _month = '0' + _month;
                     } else {
                         _month = _month + '';
                     }
 
                     if (_day < 10) {
                         _day = '0' + _day;
                     } else {
                         _day = _day + '';
                     }
 
                     var sikDate = _year + _month + _day;
                     //Handle end of days
                     var _endDay = $scope.sikDays.getDate() + '';
                     var _endYear = $scope.sikDays.getFullYear() + '';
                     var _endMonth = $scope.sikDays.getMonth() + 1;
 
                     if(_endMonth < 10){
                         _endMonth = '0' + _endMonth;
                     }else{
                         _endMonth = _endMonth + '';
                     }
 
                     if(_endDay < 10){
                         _endDay = '0' + _endDay;
                     }else{
                         _endDay = _endDay + '';
                     }
                     var sikEndDate = _endYear + _endMonth + _endDay;
                     //End handle
 
                     //Calculate days
                     var time_difference = $scope.sikDays.getTime() - $scope.sikDate.getTime();  
                     var calcSickDays = time_difference / (1000 * 60 * 60 * 24) + 1 ;  
 
                     //var calcSickDays = sikEndDate - sikDate + 1;
 
                     metaData = {
                         IDNumber: localStorage["userId"],
                         FormID: $scope.fromid,
                         SessionKey: localStorage["userSessionKey"],
                         SikDate: sikDate,
                         SikDays: calcSickDays
                     }
 
                     /*
                     $scope.request = {
                         SessionKey: localStorage.getItem("userSessionKey"),
                         IDNumber: localStorage.getItem("userId"),
                         RequestID: '1260ddfd-4a03-4d1c-adc4-b80f580b714f',
                         Date: sikDate,
                         AbsenceToDate: sikEndDate,
                         EntranceTime: '',
                         ExitTime: '',
                         AfterHours: '',
                         AbsenceReason: 'מחלה',
                         WorkedOnHoliday: false
                     }
             
                     server.request('/volunteer/attn/update', $scope.request, true).then(function (data) {
 
                         if (data.Result == "Success") {
                             // console.log(data);
                             //$scope.backToId();
                             //alert("TEST");
                         }
                     });
                     */
                     //TEST
                     //End TEST
         
 
 
                 }
                 var params = {};
                 params.jsonData = JSON.stringify(metaData);
                 options.params = params;
 
                 console.log(metaData)
 
                 var ft = new FileTransfer();
                 if ($scope.myFile) {
                     ft.upload($scope.myFile.base64File, uri, win, fail, options);
                 }
                 else if ($scope.myImage) {
                     ft.upload($scope.myImage, uri, win, fail, options);
                 }
                 
             } catch (err) {
                 console.log(err)
                 $scope.myFile = null;
                 $scope.sikDate = null;
                 $("#_sikDays").val($scope.sikDate)
                 $scope.sikDays = null;
                 $("#_sikDate").val($scope.sikDays)
             }
         }
         else {
             $scope.uploadImg();
         }
 
         $rootScope.showLoader = false; 
     }
 
 
 
     $scope.nationalServicePopUp = function (){
         //$('#slide-bottom-popup').modal('show');
         $('#nationalService').modal('show');
     }
 
 
     $scope.goToOutSideLink = function(){         
           window.open(link,'_blank').focus();
     }
 
     $scope.goToOutSideLinkForm = function(URL){
         window.open(URL,'_blank').focus();
     }
     /*
     $scope.test2 = function(){window.open(link);}
     
     $scope.test3 = function(){
         var win = window.open(link, '_blank');
     if (win) {
         //Browser has allowed it to be opened
         win.focus();
     } else {
         //Browser has blocked it
         console.log('Please allow popups for this website');
     }}
     
     $scope.test4 = function(){window.open("https://forms.gov.il/globaldata/getsequence/gethtmlform.aspx?formType=<EMAIL>");}
     
     $scope.test5 = function(){
         var form = document.createElement("form");
     form.method = "GET";
     form.action = link;
     form.target = "_blank";
     document.body.appendChild(form);
     form.submit();}
     
     $scope.test6 = function(){window.open("https://forms.gov.il/globaldata/getsequence/gethtmlform.aspx?formType=<EMAIL>", '_system');}
     */
 
 }]);
 
 //Camera modal
 //haagudApp.directive('cameraModal', ['$state', '$timeout', 'generalDetails', function ($state, $timeout, generalDetails) {
 //    return {
 //        restrict: 'A',
 //        link: function (scope, elem, attrs) {
 //            $(".cameraModal").click(function () {
 //                setTimeout(function () {
 //                    $('#slide-bottom-popup').modal('show');
 //                    
 //                }, 250); // milliseconds
 //
 //            });
 //          
 //        }
 //    };
 //} ])
 
 
 // BY HEZI:
 
 function handleSubmit(){
 
    const formData = new FormData()
 
    const base64Data = 'data:image/png;base64,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'
     
    const file =  new File([base64Data], "foo.png", {
       type: "image/png",
     })
 
 
    formData.append('file', file)
    formData.append('jsonData', JSON.stringify({
      SessionKey: '4506193150',
      IDNumber: '27895051',
      FormID: '18',
 
    }))
    // use env variable

      fetch(  + '/volunteer/forms/send', {
      method: 'post',
      body: formData,
    }).then((res) => {
 
      console.log('DONE!')
    }, (err) => {
 
      console.erorr('!!!', err)
    })
 }
 
 
