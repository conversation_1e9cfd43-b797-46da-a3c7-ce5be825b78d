<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Mquestions extends CI_Model {
    
    const TABLE = 'answered_questions';
    const TABLE_QUESTIONS = 'questions';
    const TABLE_QUESTIONS_CAT = 'questions_categories';
    const TABLE_QUESTIONS_STATUS = 'answered_questions_status';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function get_questions_categori($step) {
        $this->db->select('*');
        $this->db->from(self::TABLE_QUESTIONS_CAT);
        $this->db->where('step', $step);
        
        $result = $this->db->get();
        
        if($result->num_rows() > 0) {
                $array = $result->row_array();
                //print_r($array);
                //die('sebas');
                $category_name = $array['name'];
                $count = $this->count_all();
            
                return array('where' => $category_name, 'total_steps' => $count);
        }
        else {
            return FALSE;
        }
    }
    
    public function count_all() {
        $this->db->select('*');
        $this->db->from(self::TABLE_QUESTIONS_CAT);
        return $this->db->count_all_results();
    }
    
    public function check_if_already_saved($id,$step) {
        $this->db->select('*');
        $this->db->from(self::TABLE);
        $this->db->where('id_questions', $id);
        $this->db->where('step_number', $step);
        
//        echo 'id: '.$id.' step: '.$step;
//        echo "number: ".$this->db->count_all_results();
//        die();
        
        return $this->db->count_all_results();
    }
    
    
    public function check_if_status($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE_QUESTIONS_STATUS);
        $this->db->where('id_questions', $id);
        
//        echo 'id: '.$id.' step: '.$step;
//        echo "number: ".$this->db->count_all_results();
//        die();
        
        return $this->db->count_all_results();
    }
    
       
    public function select_answers($id) {
        $this->db->select('*');
        $this->db->from(self::TABLE);
        
        $this->db->order_by('step_number ASC, id DESC');
        
        
        $this->db->where('id_questions', $id);
        $result = $this->db->get();
        
        if($result->num_rows() > 0) {
                return $result->result_array();
        }
        else {
            return FALSE;
        }
    }
    
    
    
    
    
    public function insert() {
        
        $this->db->select(self::TABLE_QUESTIONS . '.*');
        $this->db->from(self::TABLE_QUESTIONS);
        
        if($this->input->post('questions_step')) {
            $this->db->where(self::TABLE_QUESTIONS . '.step', $this->input->post('questions_step'));
            $result = $this->db->get();
            
            if($result->num_rows() > 0) {
                
                
                $is_already_saved = $this->check_if_already_saved($this->input->post('id_questions'),$this->input->post('step_number'));
                
                if(empty($is_already_saved)) {
                    foreach($result->result_array() as $row){
                        $post_name = 'inputId_'.$row['id'];
                        $post_size = 'inputsizeId_'.$row['id'];
                        $post_type = 'inputpost_typeId_'.$row['id'];

                        $params[]  = array (
                            'id_questions' => htmlspecialchars($this->input->post('id_questions')),
                            'step_number' => htmlspecialchars($this->input->post('step_number')),
                            'created_at' => date("Y-m-d H:i:s"),
                            'status' => 0,
                            'step' => $row['step'],
                            'question' => $row['question'],
                            'answer' => htmlspecialchars($this->input->post($post_name)),
                            'size_input' => htmlspecialchars($this->input->post($post_size)),
                            'input_type' => htmlspecialchars($this->input->post($post_type))
                            );
                    }
                    $this->db->insert_batch(self::TABLE, $params); 
                    $return = $this->db->insert_id();
                };
                $return = 'FALSE';
            }
        }
        
        return $return;

    }
    
    
    
    
    public function update_all() {
        
        $this->db->select(self::TABLE . '.*');
        $this->db->from(self::TABLE);
        $update = FALSE;
        $return = FALSE;
        
        if($this->input->post('id_questions')) {
            $this->db->where(self::TABLE . '.id_questions', $this->input->post('id_questions'));
            $result = $this->db->get();
            
            if($result->num_rows() > 0) {
                
                //$array = $result->result_array();
                //print_r($array); die();
                
                foreach($result->result_array() as $row){
                    $post_name = 'inputId_'.$row['id'];
                    
                    $post_value = $this->input->post($post_name);
                    $id = $row['id'];
                    

                    $params  = array (
                        'answer' => htmlspecialchars($post_value),
                        );
                    $update = $this->update($id,$params);
                }
                $return = TRUE;
            }
            
        }
        
        return $return;

    }
    
    
    public function update($id, $params) {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE, $params); 
        return $this->db->affected_rows();
    }
    
    public function delete($id) {
        $this->db->where('id', $id);
        $this->db->delete(self::TABLE);
        return $this->db->affected_rows();
    }
    
    public function check_marital_status($string=FALSE,$id,$step) {
        if($step > 1 and !empty($string)) {
            $array = explode(",",$string);

            $this->db->select('*');
            $this->db->from(self::TABLE_QUESTIONS_STATUS);
            $this->db->where('id_questions', $id);
            $result = $this->db->get();

            $marital_status = FALSE;

            if($result->num_rows() > 0) {
                $row = $result->row_array();
                $marital_status = $row['marital_status_id'];
//                echo "sebas: ".$marital_status;
//                die();
            }


            if(!empty($array)) {
                $flag = FALSE;
                foreach ($array as $value) {
                    if($value==$marital_status) {
                        $flag = TRUE;
                    }
                }
            }
            return $flag;
        }
        else {return TRUE;};
    }
    
    
    public function add_status_row() {
        
        $is_already_saved = $this->check_if_status('id_questions');

        if(empty($is_already_saved) ) {
            
                if($this->input->post('inputId_3') == 'רווק/ה') {$marital_status = 1;}
            elseif($this->input->post('inputId_3') == 'נשוי') {$marital_status = 2;}
            elseif($this->input->post('inputId_3') == 'גרוש/ה') {$marital_status = 3;}
            elseif($this->input->post('inputId_3') == 'פרוד' or $this->input->post('inputId_3') == 'בהליכי גירושין') {$marital_status = 4;}
            else {$marital_status = 3;};
            
            $post_name =  htmlspecialchars($this->input->post('inputId_11'));
            $post_surname = htmlspecialchars($this->input->post('inputId_10'));
            $full_name = $post_name.' '.$post_surname;

            $params = array (
                'id_questions' => htmlspecialchars($this->input->post('id_questions')),
                'created_at' => date("Y-m-d H:i:s"),
                'status' => 'נפתח תיק',
                'name' => $full_name,
                'marital_status_id' => $marital_status
                
            );
            
            $this->db->insert(self::TABLE_QUESTIONS_STATUS, $params); 
            $return = $this->db->insert_id();
        }
        else {
            $return = FALSE;
        }

        return $return;
    }
    
    public function get_days() {
        
        $startDate = date('Y-m-d');
        $endDate = date("Y-m-d", strtotime("+8 days"));
        
        
        
        $period = $this-> createDateRange($startDate, $endDate, $format = "D d.m");
        
        return $period;
    }
    
    public function createDateRange($startDate, $endDate, $format = "Y-m-d") {
        $begin = new DateTime($startDate);
        $end = new DateTime($endDate);

        $interval = new DateInterval('P1D'); // 1 Day
        $dateRange = new DatePeriod($begin, $interval, $end);

        $range = [];
        foreach ($dateRange as $date) {
            
            $date_format = $date->format($format);
            
            $date_format = str_replace('Sun', 'יום ראשון', $date_format);
            $date_format = str_replace('Mon', 'יום שני', $date_format);
            $date_format = str_replace('Tue', 'יום שלישי', $date_format);
            $date_format = str_replace('Wed', 'יום רביעי', $date_format);
            $date_format = str_replace('Thu', 'יום חמישי', $date_format);
            
            if ( !(strpos($date_format,'Fri') !== false) && !(strpos($date_format,'Sat') !== false) ) {
                $range[] = $date_format;
            };
        }

        return $range;
    }

    
    public function add_questions_robot() {
        $params = array (
            'created_at' => date("Y-m-d H:i:s"),
            'status' => 'פנייה חדשה',
            'name' => $this->input->post('name'),
            'phone' => $this->input->post('phone'),
            'email' => $this->input->post('email'),
            'branch' => $this->input->post('branch'),
            'debts' => $this->input->post('debts'),
            'opened' => $this->input->post('opened'),
            'process' => $this->input->post('process'),
            'date' => $this->input->post('date'),
            'time' => $this->input->post('time')
        );

        $this->db->insert('robot_questions', $params); 
        return $this->db->insert_id();
    }
    
    
    
}
    
    
    