<style>
    figure.otobus {
        display: inline-block;
        width: 80px;
        height: 80px;
        border: 1px solid #DEDEDE;
        margin: 4px 5px 0px;
        background: center center transparent;
        background-size: cover;
    }
</style>
<div class="panel panel-default">
    <!-- Default panel contents -->
    <div class="panel-heading">
        פרוייקטים
        <small>
            <?php showInfo($total, $limit, $page); ?>
        </small>
        
        <div class="fixed-action-btn" style="bottom: 45px; left: 24px;">
            <a href="<?php echo base_url('admin/object/projects/show' . getQS().'&return='.  current_url()); ?>" class="btn btn-primary pulse" 
               style="width: 100px;height: 100px;padding: 38px 0;border-radius: 50%;">
                הוסף חדש
            </a>
        </div>
        
        <form action="" method="get" class="form-inline pull-left">
<!--            <div class="form-group">
                <input type="text" name="from_date" value="<?php echo $from_date; ?>" class="form-control input-sm date" placeholder="מתאריך">
            </div>
            <div class="form-group">
                <input type="text" name="to_date" value="<?php echo $to_date; ?>" class="form-control input-sm date" placeholder="עד תאריך">
            </div>-->
            <div class="form-group">
                <input type="text" name="q" value="<?php echo $q; ?>" class="form-control input-sm" placeholder="טקסט חופשי">
            </div>
            
            
            <button type="submit" class="btn btn-sm btn-hover btn-default">חפש</button>
            <a href="<?php echo base_url('admin/' . $this->router->fetch_class()); ?>" class="btn btn-sm ">נקה חיפוש</a>
            <!--<a href="<?php //echo base_url('admin/projects/csv' . getQS()); ?>" class="btn btn-sm btn-hover btn-warning">יצא לאקסל</a>-->
        </form>
        <div style="clear: both;"></div>
    </div>
    <!-- Table -->
    <table class="table table-hover">
        <thead>
            <tr>

                <th><?php showOrder('id', 'id'); ?></th>
                <th><?php showOrder('created_at', 'תאריך עליה'); ?></th>
                <th><?php showOrder('sort', 'סדר באתר'); ?></th>
                <th><?php showOrder('user_id', 'הועלה'); ?></th>
                <th><?php showOrder('title', 'שם פרויקט'); ?></th>
                <th><?php showOrder('category', 'קטגוריה'); ?></th>
<!--                <th><?php //showOrder('', 'תמונות'); ?></th>-->
                <th class="text-center"><?php showOrder('status', 'סטאטוס'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if(isset($objects) && !empty($objects)) { foreach($objects as $row) { ?>
                <tr id="row<?php echo $row->Id(); ?>" role="row">
                    <td> 
                        <?php //echo $row->Datetime('created_at', "d/m/Y"); ?>
                        <span>#</span><?php echo $row->Arg('id'); ?>
                    </td>
                    <td>
                        <?php echo $row->Datetime('created_at'); ?><br/>
                    </td>
                    <td>
                        <?php echo $row->Arg('sort'); ?><br/>
                    </td>
                    
                    <td>
                        <?php echo $row->Arg('user_id'); ?><br/>
                    </td>
                    
                    <td>
                        <?php echo $row->Arg('title'); ?><br/>
                        <?php echo $row->Arg('phone'); ?><br/>
                        <?php echo $row->Arg('mail'); ?><br/>
                        <?php if(!empty($row->Arg('lead_3rdPersonHelp'))) {echo '<strong>'.'עבור אדם אחר'.'</strong>'.'<br/>';}; ?>
                        <?php echo $row->Arg('lead_place'); ?>                        
                    </td>
                    <td>
                        <?php foreach ($categories as $category) {
                                if($category->Arg('id') == $row->Arg('category') ) {
                                    echo $category->Arg('title').'<br/>';
                                }
                            }
                        ?>
                        <figure class="otobus" style="background-image: url('<?php echo $row->Img('image').'?v='.VERSION; ?>')"></figure>
                    </td>
<!--                    <td>
                        <?php //$gallery = $this->msite->getProjGallery($row->Arg('id'));
                              //$plans = $this->msite->getPlansGallery($row->Arg('id')); 
                        //foreach ($gallery as $key => $picture): ?>
                        <figure class="otobus" style="background-image: url('<?php //echo base_url() . picIMG . $picture['image'].'?v='.VERSION; ?>')"></figure>
                        <?php //endforeach; ?>
                    </td>-->
                    
                    <td class="text-center">
                        <div class="btn-group" role="group">
                            <a class="btn btn-info" href="<?php echo base_url('admin/object/projects/show/'.$row->Id().'?return='.  current_url())?>">ערוך פרטים</a>
                            <a class="btn btn-default" href="<?php echo base_url('admin/object/index/galleryProjects?Id='.$row->Id().'&return='.  current_url())?>">ערוך תמונות </a>
                        </div>

                        <div class="btn-group" role="group">
                            <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/projects/status/' . $row->Id()); ?>">
                                <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                            </button> 
                            <button type="button" data-url="<?php echo base_url('admin/' . $this->router->fetch_class() . '/destroy/' . $row->Id() . getQS()); ?>" class="btn btn-danger delete">
                                מחק
                            </button>
                        </div>

                    </td>
                </tr>
            <?php }} ?>
        </tbody>
    </table>
    <div class="row">
        <div class="col-md-12 text-center">
            <?php showPages($total, $limit, $page); ?>
        </div>
    </div>
</div>





