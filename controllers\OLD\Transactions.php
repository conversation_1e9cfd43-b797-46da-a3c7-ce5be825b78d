<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Transactions extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+OtzarHaaretz';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+OtzarHaaretz-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        //$this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        $this->load->model('msiteWs');
        $this->load->model('OtzarHaretz');
        
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    public function checkSms() {
        
        $this->load->model('OtzarHaretz');
        
        $phone = '0546464312';
        
        $message = 'העברה על סך ';
        $message .= '₪';
        $message .= '10.50';
        $message .= ' '.'ל-';
        $message .= 'אבי כהן חקלאי עוטף עזה'.',';
        $message .= ' '.' קופה: ';
        $message .= 'קופה 1 אבי'.' - ';
        $message .= ' הושלמה בהצלחה. ';
        $message .= 'מספר אסמכתא: ';
        $message .= '1A1143A10'.'.';
        
        
        
        
        //$this->OtzarHaretz->sendSMS($phone, $message, $from = 'OtzarHaretz', $param = FALSE);
        
    }
    
    
    public function makeTransaction($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','supplierId','cashierId','money'));
        
        
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if(!empty($jsonPosts['supplierId']) && !empty($jsonPosts['cashierId']) && !empty($jsonPosts['money']) ) {
                
                
                //$this->db->select('*');
                $this->db->select('phone');
                $this->db->from('cashiersSuppliers');
                $this->db->where('status', 1);
                $this->db->where('id', $jsonPosts['cashierId']);
                $result= $this->db->get();
                $cashierData = $result->row_array();
                
                
                
                
                $money = $this->OtzarHaretz->getUserMoney($jsonPosts);
            
                if( ( (float)$money >= (float)$jsonPosts['money'] ) && (float)$jsonPosts['money'] > 0 ) {
                
                    
                    $table = 'transactions';

                    
                    $future_id = $this->msite->get_max($table, 'id') + 1;
                    $tokenBuild = $jsonPosts['userId'].$jsonPosts['supplierId'].(int)$jsonPosts['money'].$future_id;
                    $token = $this->OtzarHaretz->CheckLen($tokenBuild,10,'start','0');

                    $data = array(
                        'lang' => NULL,
                        'sort' => $this->msite->get_max($table, 'sort') + 10,
                        'status' => 1,
                        'created_at' => date("Y-m-d H:i:s"),
                        'userId' => $jsonPosts['userId'],
                        'supplierId' => $jsonPosts['supplierId'],
                        'cashierId' => $jsonPosts['cashierId'],
                        'money' => $jsonPosts['money'],
                        'token' => $token
                    );

                    $insert = $this->db->insert($table, $data); 
                    $insert_id = $this->db->insert_id();

                    if(!empty($insert_id)) {
                        
                        $transaction = array(
                            'supplierId' => $jsonPosts['supplierId'],
                            'cashierId' => $jsonPosts['cashierId'],
                            'token' => $token,
                            'money' => $jsonPosts['money']
                        );
                                
                    $dataTransaction = $this->getTransactionDataDb( $transaction, $userId = $jsonPosts['userId'] );
                        
                        if($dataTransaction && isset($dataTransaction['userPhone']) ) {
                            
                            $message = 'העברה על סך ';
                            $message .= '₪';
                            $message .= $dataTransaction['money'];
                            $message .= ' '.'ל-';
                            $message .= $dataTransaction['name'].',';
                            $message .= ' '.' קופה: ';
                            $message .= $dataTransaction['cashier'].' - ';
                            
                            
                            
                            $endMessage = ' הושלמה בהצלחה. ';
                            $endMessage .= 'מספר אסמכתא: ';
                            $endMessage .= $dataTransaction['token'];
                            
                            
                            
                            $messageSupplier = $message;
                            $messageSupplier .= 'מלקוח: ';
                            $messageSupplier .= $dataTransaction['nameBuyer'].'.';
                            $messageSupplier .= $endMessage;
                            
                            if(!empty($dataTransaction['supplierPhone'])) {
                                
                                $phone = $dataTransaction['supplierPhone'];
                                $output['sms'] = $this->OtzarHaretz->sendSMS($phone, $messageSupplier, $from = 'OtzarHaretz', false );
                                
                            }
                            
                            if(isset($cashierData['phone']) && !empty($cashierData['phone'])) {
                                    
                                $phone = $cashierData['phone'];
                                $output['sms'] = $this->OtzarHaretz->sendSMS($phone, $messageSupplier, $from = 'OtzarHaretz', false );
                                    
                            }
                            
                            
                            
                            
                            if(!empty($dataTransaction['userPhone'])) {
                                
                                $messageClient = $message.$endMessage;
                                
                                $phone = $dataTransaction['userPhone'];
                                $output['sms'] = $this->OtzarHaretz->sendSMS($phone, $messageClient, $from = 'OtzarHaretz', false );
                                
                            }
                            
                            
                            
                            
                            if(!isset($output['sms'])) {
                                $output['sms'] = 'error';    
                            }

                            //$output['sms'] = 'desactivated';
                            
                            
                        
                        } else {
                            $output['sms'] = 'error';
                        }
                        
                        $output['insertToken'] = $token;

                    } else {

                        $output['error'] = 'insertError';

                    }
                    
                } else {
                    $output['error'] = 'no Money';
                }
                
            } else {
                
                $output['error'] = 'insertError';
            }
                

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function cancellTransaction($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('admin','superAdmin'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','rowId','pass','supplierId'));
        
        
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if(!empty($jsonPosts['rowId']) && !empty($jsonPosts['pass']) && !empty($jsonPosts['supplierId']) ) {
                
                
                //$this->db->select('*');
                $this->db->select('id,name');
                $this->db->from('suppliers');
                $this->db->where('id', $jsonPosts['supplierId']);
                $this->db->where('passwordMd5', md5($jsonPosts['pass']));
                $result= $this->db->get();
                $supplierCheck = $result->row_array();
                
                if(!empty($supplierCheck)) {
                    
                    $this->db->select('*');
                    $this->db->from('transactions');
                    $this->db->where('id', $jsonPosts['rowId']);
                    $result= $this->db->get();
                    $transaction = $result->row_array();

                    if(!empty($transaction)) {
                     
                        $table = 'transactionsCancelled';
                        
                        $data = array(
                            'lang' => NULL,
                            'sort' => '',
                            'status' => 1,
                            'created_at' => date("Y-m-d H:i:s"),
                            'dateTransaction' => $transaction['created_at'],
                            'clientId' => $transaction['userId'],
                            'userId' => $jsonPosts['userId'],
                            'supplierId' => $transaction['supplierId'],
                            'cashierId' => $transaction['cashierId'],
                            'money' => $transaction['money'],
                            'token' => $transaction['token'],
                            'is_SMSBuy' => $transaction['is_SMSBuy']
                        );

                        $insert = $this->db->insert($table, $data); 
                        $insert_id = $this->db->insert_id();
                        
                        $delette = false;
                        
                        if(!empty($insert_id)) {
                            
                            $this->db->where('id', $jsonPosts['rowId']);
                            $this->db->where('supplierId', $jsonPosts['supplierId']);
                            $delette = $this->db->delete('transactions');
                            
                        }
                        
                        if($delette) {
                            
                            $this->db->select('firstName,lastName,phone');
                            $this->db->from('leadsLandpage');
                            $this->db->where('id', $transaction['userId']);
                            $result= $this->db->get();
                            $client = $result->row_array();
                            
                            $message = 'שלום '.$client['firstName'].' '.$client['lastName'].'. ';
                            $message .= 'קיבלת זיכוי על סך '.' '.$transaction['money'].' ש"ח, ';
                            $message .= 'מחנות: '.$supplierCheck['name'].'.';
                            
                            $this->OtzarHaretz->sendSMS($client['phone'], $message, $from = 'OtzarHaretz', $param = FALSE);
                            
                            $output['ok'] = $delette;
                            
                        } else {
                            
                            $output['error'] = 'שגיאה';
                            
                        }
                        
                        
                    } else {
                        
                        $output['error'] = 'שגיאה';
                        
                    }
                    
                    
                } else {
                    
                    $output['error'] = 'סיסמה לא נכונה';
                    
                }
                
            } else {
                
                $output['error'] = 'שגיאה';
                
            }
                

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function getTransactionData($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','tokenTrans'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            //$this->db->select('*');
            $this->db->select('*');
            $this->db->from('transactions');
            $this->db->where('status', 1);
            $this->db->where('token', $jsonPosts['tokenTrans']);
            $result= $this->db->get();
            $transaction = $result->row_array();
            
            if(!empty($transaction) ) {
                
                $data = $this->getTransactionDataDb($transaction);
                
                if($data) {
                    
                    $output['transaction'] = $data;
                    
                } else {
                    $output['error'] = 'error';
                }
                
                
            } else {
                $output['error'] = 'error';
            }
                           

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function getAllUserTransactions($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //SuperAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','clientId'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if(!empty($jsonPosts['clientId'])) {
                $id = $jsonPosts['clientId'];
            } else {
                $id = $jsonPosts['userId'];
            }
            
            //$this->db->select('*');
            $this->db->select('*');
            $this->db->from('transactions');
            $this->db->where('status', 1);
            $this->db->where('userId', $id);
            $this->db->order_by('created_at', 'DESC');
            
            $result= $this->db->get();
            
            
            $transaction = $result->result_array();
            
            if(!empty($transaction) ) {
                
                foreach ($transaction as $value) {
                    $data[] = $this->getTransactionDataDb($value);
                }
                
                $output['data'] = $data;
                
            } else {
                
                
                $output['data'] = array();
                
            } 
                           

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
 
    
    private function getTransactionDataDb($transaction, $userId=false) {
        
        
        
        if($userId) {
            
            $this->db->select('phone,firstName,lastName');
            $this->db->from('leadsLandpage');
            $this->db->where('id', $userId);
            $result= $this->db->get();
            $user = $result->row_array();
            
        } else {
            $user = array();
        }
        
        $this->db->select('name,phone');
        $this->db->from('suppliers');
        $this->db->where('id', $transaction['supplierId']);
        $result= $this->db->get();
        $supplierData = $result->row_array();

        $this->db->select('name');
        $this->db->from('cashiersSuppliers');
        $this->db->where('id', $transaction['cashierId']);
        $result= $this->db->get();
        $cashierData = $result->row_array();

        if(!empty($supplierData) && !empty($cashierData) ) {

            $firstName = isset($user['firstName']) ? $user['firstName'] : ''; 
            $lastName = isset($user['lastName']) ? $user['lastName'] : ''; 
            
            $name = $firstName.' '.$lastName;
            
            $return = array(
                'id' => isset($transaction['id']) ? $transaction['id'] : '',
                'dateTransaction' => isset($transaction['created_at']) ? $transaction['created_at'] : '',
                'nameBuyer' => $name,
                'userPhone' => isset($user['phone']) ? $user['phone'] : '',
                'name' => $supplierData['name'],
                'token' => $transaction['token'],
                'cashier' => $cashierData['name'],
                'money' => $transaction['money'],
                'supplierPhone' => $supplierData['phone']
            );
            
            return $return;
            
        } else {

            return false;

        }
    
    }
    
    
    
    
    public function getAllMoneysSystemStart($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //SuperAdmin  //adminOnly  //userOnly
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized' || $this->input->get('sebas')==1) {
            
                $this->db->select('TotalPrice,OrderId,id');
                $this->db->from('leadsLandpage');
                $result= $this->db->get();
                $money = $result->result_array();

                $count1 = 0;

                foreach ($money as $value) {

                    $count1 = $count1 + $value['TotalPrice'];

                    $this->db->select('TotalPrice,OrderId');
                    $this->db->from('buys');
                    $this->db->where('userId', $value['id']);
                    $result= $this->db->get();
                    $moneyBuys2 = $result->result_array();

                    if(!empty($moneyBuys2)) {

                        foreach ($moneyBuys2 as $money2) {

                            if($money2['OrderId'] !== $value['OrderId']) {

                                $count1 = $count1 + $money2['TotalPrice'];

                            }

                        }

                    }


                }
                
                $output['money'] = $count1;
                
                           

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function getAllTransactionSystemEditUser ($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //SuperAdmin  //adminOnly  //userOnly
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','clientId'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            $this->db->select('id,created_at,TotalPrice,OrderId');
            $this->db->from('leadsLandpage');
            $this->db->where('id', $jsonPosts['clientId']);
            $result= $this->db->get();
            $user = $result->row_array();
            
            $this->db->select('id');
            $this->db->from('transactions');
            $this->db->where('userId', $user['id']);
            $result= $this->db->get();
            $transactionsCounts = $result->num_rows();
            
            
            $data = array(
                'userId' => $user['id']
                );
            

            $money = $this->OtzarHaretz->getUserMoney($data,true);
            $AllMoney = $this->OtzarHaretz->getAllBuysClient($user);
            
            $output['response'] = array(
                
                'transactions' => $money,
                'allMoney' => $AllMoney,
                'transactionsCount' => $transactionsCounts,
                'dateUser' => changeDateFormat($user['created_at'], 'Y-m-d H:i:s', 'd.m.Y')
                
            );
                           

        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
//    public function deletteDupicates() {
//        
//        
//        
//        $this->db->select('id,OrderId');
//        $this->db->from('leadsLandpage');
//        $result= $this->db->get();
//        $clients = $result->result_array();
//        echo count($clients);
//        
//        foreach ($clients as $value) {
//            $this->db->where('OrderId', $value['OrderId']);
//            $this->db->where('userId', $value['id']);
//            $this->db->delete('buys');
//        }
//        
//        echo "END";
//        
//    }
    
    
}