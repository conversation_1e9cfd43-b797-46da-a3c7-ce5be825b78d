/**
 * pdfOperations.js - ממשק מאוחד לפעולות PDF
 *
 * מאחד את כל הפעולות הקשורות ליצירת PDF:
 * - עיבוד מסמכים
 * - טיפול בחתימות
 * - יצירת PDF
 */

import {
  processForm101,
  processServicePeriod,
  processReceiptReport,
  fixDatesInDocument,
} from "./documentProcessors";
import {
  adjustSignatureForForm101,
  adjustSignatureForReceiptReport,
  addKnownSignatureImages,
  preserveViewSignatures,
} from "./signatureUtils";
import {
  createGlobalStyle,
  generatePdfFromElement,
  loadImageWithProxy,
  convertImageToBase64,
} from "./pdfUtils";
import {
  form101Template,
  servicePeriodTemplate,
  receiptReportTemplate,
  genericDocumentTemplate,
} from "./documentTemplates";
import {
  enrichDocumentData,
  getCurrentGregorianDate,
  getCurrentHebrewDate,
} from "./dateUtils";

/**
 * מעבד את תוכן המסמך ומחזיר HTML מעובד עם תבנית אחידה
 * @param {string} html - קוד ה-HTML המקורי של המסמך
 * @param {string} documentType - סוג המסמך (101, 102, 104)
 * @param {string} logo - הלוגו להוספה למסמך
 * @returns {string} ה-HTML המעובד של המסמך
 */
export const processDocumentContent = (html, documentType, logo) => {
  // יצירת אלמנט זמני לעיבוד ה-HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;

  // סימון אלמנטים שמכילים מספרי זהות
  const idPatterns = ["ת.ז.", "ת.ז", "מספר זהות", 'ת"ז'];
  const allElements = tempDiv.querySelectorAll("*");
  allElements.forEach((el) => {
    const text = el.textContent;
    if (idPatterns.some((pattern) => text.includes(pattern))) {
      el.setAttribute("data-contains-id", "true");
    }

    // חיפוש אלמנטים עם מספרי תעודת זהות
    if (
      /\b\d{5,9}\b/.test(text) &&
      (text.includes("ת.ז") ||
        text.includes("זהות") ||
        text.toLowerCase().includes("id") ||
        el.id.toLowerCase().includes("id") ||
        el.className.toLowerCase().includes("id"))
    ) {
      el.setAttribute("data-contains-id", "true");
    }
  });

  // תיקון תאריכים בכל המסמך
  fixDatesInDocument(tempDiv);

  // הסרת מסגרות מהתאריכים (גם בצפייה)
  if (documentType === "104") {
    // הוספת סגנון להסרת מסגרות תאריכים
    const dateFixStyle = document.createElement("style");
    dateFixStyle.textContent = `
      /* הסרת מסגרות מתאריכים בצפייה */
      .date-field, .issue-date, .service-date, .period-date,
      [class*="date"], [id*="date"], [class*="תאריך"], 
      td:has(time), div:has(time), span:has(time) {
        border: none !important;
        background-color: transparent !important;
        box-shadow: none !important;
        outline: none !important;
      }
      
      /* הסרת האלמנטים הדקורטיביים משני צדי התאריך */
      .date-field::before, .date-field::after,
      .issue-date::before, .issue-date::after,
      .service-date::before, .service-date::after,
      .period-date::before, .period-date::after,
      [class*="date"]::before, [class*="date"]::after {
        display: none !important;
        content: none !important;
      }
    `;
    tempDiv.appendChild(dateFixStyle);
  }

  // מחיקת לוגואים קיימים כדי למנוע כפילות
  const existingLogos = tempDiv.querySelectorAll(
    'img[src*="logo"], img[alt*="לוגו"], img[alt*="האגודה"]'
  );
  existingLogos.forEach((logoElement) => {
    if (logoElement.parentNode) {
      logoElement.parentNode.removeChild(logoElement);
    }
  });

  // מחיקת האלמנטים של בס"ד למניעת כפילות
  const besdElements = Array.from(tempDiv.querySelectorAll("*")).filter(
    (el) => el.textContent && el.textContent.trim() === 'בס"ד'
  );
  besdElements.forEach((element) => {
    if (element.parentNode) {
      element.parentNode.removeChild(element);
    }
  });

  // מחיקת אלמנטי תאריך נפרדים שעלולים להיות מוצגים שוב
  const dateHeaderElements = tempDiv.querySelectorAll(
    '.date-header, .document-date, [class*="date"]'
  );
  dateHeaderElements.forEach((element) => {
    // בודק אם זה רק תאריך או אם זה שדה דינמי חשוב
    const text = element.textContent.trim();
    const isDynamicDateField =
      text.includes("תאריך תחילת שירות") ||
      text.includes("תאריך סיום שירות") ||
      text.includes("תקופת הדוח");

    // מוחק רק אם זה תאריך כללי שלא קשור לשדות דינמיים
    if (
      !isDynamicDateField &&
      (text.match(/\d{1,2}\/\d{1,2}\/\d{4}/) || text.includes("תאריך"))
    ) {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    }
  });

  // סינון כותרות כפולות
  const titleElements = tempDiv.querySelectorAll("h1, h2, h3");
  titleElements.forEach((element) => {
    if (
      (element.textContent.includes("טופס 101") && documentType === "101") ||
      (element.textContent.includes("אישור תקופת שירות") &&
        documentType === "102") ||
      (element.textContent.includes("דוח תקבולים") && documentType === "104")
    ) {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    }
  });

  // הכנת אובייקט נתונים בסיסי שיועבר לתבנית
  let templateData = {
    formContent: "",
    certificateContent: "",
    reportContent: "",
    title: "",
  };

  // החזרת אלמנטים שסומנו כמכילים ת.ז. למצבם המקורי
  const idElements = tempDiv.querySelectorAll('[data-contains-id="true"]');
  idElements.forEach((el) => {
    // שומר את התוכן המקורי כדי שלא יעבור עיבוד נוסף
    el.setAttribute("data-skip-date-processing", "true");
  });

  // חילוץ נתונים מהמסמך המקורי לפי סוג המסמך
  if (documentType === "101") {
    // חילוץ תאריכי התחלה וסיום
    const startDateEl = tempDiv.querySelector(
      '.start-date, [data-field="start-date"]'
    );
    const endDateEl = tempDiv.querySelector(
      '.end-date, [data-field="end-date"]'
    );

    templateData = {
      startDate: startDateEl
        ? startDateEl.textContent.trim()
        : getCurrentGregorianDate(),
      endDate: endDateEl ? endDateEl.textContent.trim() : "",
      formContent: tempDiv.innerHTML,
      title: "טופס 101",
    };
  } else if (documentType === "102") {
    // חילוץ תאריכי תחילת וסיום שירות
    const startDateEl = tempDiv.querySelector(
      '.service-start, [data-field="service-start"]'
    );
    const endDateEl = tempDiv.querySelector(
      '.service-end, [data-field="service-end"]'
    );

    templateData = {
      startDate: startDateEl
        ? startDateEl.textContent.trim()
        : getCurrentGregorianDate(),
      endDate: endDateEl ? endDateEl.textContent.trim() : "",
      certificateContent: tempDiv.innerHTML,
      title: "אישור תקופת שירות",
    };
  } else if (documentType === "104") {
    // חילוץ תקופת הדוח
    const periodStartEl = tempDiv.querySelector(
      '.period-start, [data-field="period-start"]'
    );
    const periodEndEl = tempDiv.querySelector(
      '.period-end, [data-field="period-end"]'
    );

    templateData = {
      periodStart: periodStartEl ? periodStartEl.textContent.trim() : "",
      periodEnd: periodEndEl ? periodEndEl.textContent.trim() : "",
      reportContent: tempDiv.innerHTML,
      title: "דוח תקבולים",
    };

    // טיפול אגרסיבי בתאריכים בדוח תקבולים כבר בשלב הצפייה
    try {
      console.log("מבצע טיפול אגרסיבי בתאריכים לדוח תקבולים בשלב הצפייה");

      // עדכון כל התאריכים הלועזיים במסמך
      const currentDate = new Date();
      const formattedCurrentDate =
        currentDate.getDate().toString().padStart(2, "0") +
        "/" +
        (currentDate.getMonth() + 1).toString().padStart(2, "0") +
        "/" +
        currentDate.getFullYear();

      console.log(
        "מעדכן את כל התאריכים לתאריך הנוכחי בשלב הצפייה:",
        formattedCurrentDate
      );

      // החלפת טקסט תאריך בכל האלמנטים
      const allElements = tempDiv.querySelectorAll("*");
      allElements.forEach((el) => {
        // אם יש טקסט באלמנט
        if (el.childNodes && el.childNodes.length > 0) {
          // עובר על כל צמתי הטקסט
          el.childNodes.forEach((node) => {
            if (node.nodeType === 3) {
              // צומת טקסט
              const text = node.nodeValue;
              if (
                text &&
                /\d{1,2}\/\d{1,2}\/\d{4}/.test(text) &&
                !text.includes("ת.ז") &&
                !text.includes("מספר זהות")
              ) {
                // החלף את התאריך בתאריך הנוכחי
                node.nodeValue = text.replace(
                  /\d{1,2}\/\d{1,2}\/\d{4}/g,
                  formattedCurrentDate
                );
              }
            }
          });
        }

        // אם יש innerHTML
        if (el.innerHTML) {
          // וידוא שלא מדובר בת.ז. או שדה ID
          if (
            !/ת\.ז|ת"ז|מספר זהות|id="/.test(el.innerHTML) &&
            /\d{1,2}\/\d{1,2}\/\d{4}/.test(el.innerHTML)
          ) {
            el.innerHTML = el.innerHTML.replace(
              /\d{1,2}\/\d{1,2}\/\d{4}/g,
              formattedCurrentDate
            );
          }
        }
      });

      // עדכון ה-templateData עם התוכן המעודכן
      templateData.reportContent = tempDiv.innerHTML;
    } catch (error) {
      console.error(
        "שגיאה בטיפול האגרסיבי בתאריכים בדוח תקבולים בשלב הצפייה:",
        error
      );
    }
  } else {
    // מסמך כללי
    templateData = {
      content: tempDiv.innerHTML,
      title: "מסמך",
    };
  }

  // העשרת הנתונים עם תאריכים מעודכנים
  const enrichedData = enrichDocumentData(templateData);

  // בחירת התבנית המתאימה והפקת ה-HTML המלא
  let processedHtml = "";

  if (documentType === "101") {
    processedHtml = form101Template(enrichedData);
  } else if (documentType === "102") {
    processedHtml = servicePeriodTemplate(enrichedData);
  } else if (documentType === "104") {
    processedHtml = receiptReportTemplate(enrichedData);
  } else {
    processedHtml = genericDocumentTemplate(enrichedData);
  }

  // החלפת הפלייסהולדר של הלוגו
  return processedHtml.replace("{{LOGO_PLACEHOLDER}}", logo || "");
};

/**
 * מכין מסמך ליצירת PDF ומייצר אותו
 * @param {string} html - המסמך כ-HTML
 * @param {string} documentType - סוג המסמך (101, 102, 104)
 * @param {string} filename - שם הקובץ ליצירה
 * @param {string} logo - נתיב ללוגו
 * @param {Object} signatures - אובייקט המכיל נתיבים לחתימות
 * @param {Function} onComplete - פונקציית קולבק לאחר סיום היצירה
 * @param {Function} onError - פונקציית קולבק במקרה של שגיאה
 */
export const generateDocument = async (
  html,
  documentType,
  filename,
  logo,
  signatures,
  onComplete,
  onError
) => {
  try {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = html;

    // עיצוב בסיסי למסמך
    tempDiv.style.width = "190mm"; // רוחב דף A4
    tempDiv.style.padding = "10mm";
    tempDiv.style.height = "auto";
    tempDiv.style.overflow = "hidden";
    tempDiv.style.fontSize = "11px";
    tempDiv.style.lineHeight = "1.2";
    tempDiv.style.direction = "rtl";
    tempDiv.style.textAlign = "right";
    tempDiv.style.marginTop = "0";

    // בדיקה האם מדובר בתבנית החדשה
    const isNewTemplate = tempDiv.querySelector(".document-container");

    // אם זו אינה התבנית החדשה, יש להוסיף לוגו וכו'
    if (!isNewTemplate) {
      // בדוק אם יש צורך להוסיף לוגו
      const existingLogos = tempDiv.querySelectorAll(
        'img[src*="logo"], img[alt*="לוגו"]'
      );
      if (existingLogos.length === 0 && documentType !== "101") {
        const logoElement = document.createElement("div");
        logoElement.style.cssText = "text-align: center; margin-bottom: 20px;";
        logoElement.innerHTML = `<img src="${logo}" alt="לוגו השירות הלאומי" style="width: 150px; height: auto;" />`;
        tempDiv.insertBefore(logoElement, tempDiv.firstChild);
      }
    }

    // הוסף סגנון גלובלי למסמך
    const globalStyle = createGlobalStyle(documentType);
    tempDiv.appendChild(globalStyle);

    // מחק חתימות קיימות במידת הצורך
    if (documentType !== "102" && !isNewTemplate) {
      const allSignatures = tempDiv.querySelectorAll(
        '.signature-area, .signature-box, img[src*="yaron"], img[src*="aguda"], img[alt*="חתימה"], img[alt*="signature"]'
      );
      allSignatures.forEach((sig) => {
        if (sig.parentNode) {
          sig.parentNode.removeChild(sig);
        }
      });
    }

    // החלף תמונות חיצוניות בתמונות מקומיות
    const allImages = tempDiv.querySelectorAll("img");
    for (let i = 0; i < allImages.length; i++) {
      const img = allImages[i];
      const imgSrc = img.src.toLowerCase();

      if (imgSrc.includes("yaron") || imgSrc.includes("ירון")) {
        img.src = signatures.yaron;
      } else if (
        imgSrc.includes("aguda") ||
        imgSrc.includes("bbbb") ||
        imgSrc.includes("אגודה")
      ) {
        img.src = signatures.aguda;
      } else if (imgSrc.includes("logo") || imgSrc.includes("לוגו")) {
        img.src = signatures.logo;
      }
    }

    // טיפול ספציפי לכל סוג מסמך במידת הצורך
    if (documentType === "101" && !isNewTemplate) {
      adjustSignatureForForm101(tempDiv, signatures);
    } else if (documentType === "104" && !isNewTemplate) {
      const dummySignature = document.createElement("img");
      // הגדר את החתימה להיות קטנה יותר
      dummySignature.style.width = "60px";
      dummySignature.style.maxWidth = "40px";
      dummySignature.style.height = "auto";
      dummySignature.style.opacity = "0.85";

      // קרא לפונקציה שמטפלת בחתימה בדוח תקבולים
      adjustSignatureForReceiptReport(
        tempDiv,
        dummySignature,
        signatures.yaron
      );
    } else if (documentType !== "102" && !isNewTemplate) {
      await preserveViewSignatures(tempDiv, documentType, signatures);
    }

    // בדיקה סופית לפני יצירת PDF - האם יש תמונות חיצוניות שצריך להחליף
    const remainingExternalImages = Array.from(
      tempDiv.querySelectorAll("img")
    ).filter(
      (img) =>
        img.src.startsWith("http") &&
        !img.src.startsWith(window.location.origin)
    );

    if (remainingExternalImages.length > 0) {
      // החלף כל תמונה חיצונית נותרת בתמונה מקומית מתאימה
      remainingExternalImages.forEach((img) => {
        const imgSrc = img.src.toLowerCase();
        if (imgSrc.includes("yaron")) {
          img.src = signatures.yaron;
        } else if (imgSrc.includes("aguda")) {
          img.src = signatures.aguda;
        } else {
          img.src = signatures.logo; // ברירת מחדל
        }
      });
    }

    // טיפול אגרסיבי בעיות ספציפיות בדוחות תקבולים לפני יצירת ה-PDF
    if (documentType === "104") {
      try {
        console.log("מבצע טיפול אגרסיבי בדוח תקבולים לפני יצירת PDF");

        // 0. הסרת כפילויות של חתימות - מניעת הוספת חתימות מיותרות
        const signatureContainers = tempDiv.querySelectorAll(
          ".signature-area, .signature-box"
        );
        const signatureImages = tempDiv.querySelectorAll(
          'img[src*="yaron"], img[src*="signature"], img[src*="sign"], img[alt*="חתימה"]'
        );

        console.log(
          `נמצאו ${signatureContainers.length} מיכלי חתימה ו-${signatureImages.length} תמונות חתימה`
        );

        // איתור טקסט הברכה - זה יהיה המיקום שאחריו נרצה להוסיף את החתימה
        const blessingElements = Array.from(
          tempDiv.querySelectorAll("div, p, span, td")
        ).filter((el) => {
          const text = el.textContent.trim().toLowerCase();
          return (
            text.includes("בברכה") ||
            text.includes("בכבוד רב") ||
            text.includes("והצלחה")
          );
        });

        console.log(`נמצאו ${blessingElements.length} אלמנטי ברכה`);

        // נבדוק אם יש חתימה של אבי ויצמן
        let aviWeizmanSignature = null;
        signatureImages.forEach((img) => {
          const imgSrc = img.src.toLowerCase();
          const imgAlt = (img.alt || "").toLowerCase();

          if (
            imgSrc.includes("avi") ||
            imgSrc.includes("weizman") ||
            imgSrc.includes("ויצמן") ||
            imgSrc.includes("אבי") ||
            imgAlt.includes("ויצמן") ||
            imgAlt.includes("אבי")
          ) {
            console.log("נמצאה חתימה של אבי ויצמן:", img.src);
            aviWeizmanSignature = img;
          }
        });

        // 1. קודם נסיר את כל החתימות ומיכלי החתימה
        if (signatureContainers.length > 0) {
          signatureContainers.forEach((container) => {
            if (container.parentNode) {
              container.parentNode.removeChild(container);
            }
          });
        }

        if (signatureImages.length > 0) {
          signatureImages.forEach((img) => {
            if (img.parentNode) {
              img.parentNode.removeChild(img);
            }
          });
        }

        // 2. עדכון כל התאריכים הלועזיים במסמך
        const currentDate = new Date();
        const formattedCurrentDate =
          currentDate.getDate().toString().padStart(2, "0") +
          "/" +
          (currentDate.getMonth() + 1).toString().padStart(2, "0") +
          "/" +
          currentDate.getFullYear();

        console.log(
          "מעדכן את כל התאריכים לתאריך הנוכחי:",
          formattedCurrentDate
        );

        // החלפת טקסט תאריך בכל האלמנטים
        const allElements = tempDiv.querySelectorAll("*");
        allElements.forEach((el) => {
          // אם יש טקסט באלמנט
          if (el.childNodes && el.childNodes.length > 0) {
            // עובר על כל צמתי הטקסט
            el.childNodes.forEach((node) => {
              if (node.nodeType === 3) {
                // צומת טקסט
                const text = node.nodeValue;
                if (
                  text &&
                  /\d{1,2}\/\d{1,2}\/\d{4}/.test(text) &&
                  !text.includes("ת.ז") &&
                  !text.includes("מספר זהות")
                ) {
                  // החלף את התאריך בתאריך הנוכחי
                  node.nodeValue = text.replace(
                    /\d{1,2}\/\d{1,2}\/\d{4}/g,
                    formattedCurrentDate
                  );
                }
              }
            });
          }

          // אם יש innerHTML
          if (el.innerHTML) {
            // וידוא שלא מדובר בת.ז. או שדה ID
            if (
              !/ת\.ז|ת"ז|מספר זהות|id="/.test(el.innerHTML) &&
              /\d{1,2}\/\d{1,2}\/\d{4}/.test(el.innerHTML)
            ) {
              el.innerHTML = el.innerHTML.replace(
                /\d{1,2}\/\d{1,2}\/\d{4}/g,
                formattedCurrentDate
              );
            }
          }
        });

        // 3. הוספת החתימה במקום הנכון - מתחת לטקסט הברכה
        if (blessingElements.length > 0) {
          // בחר את אלמנט הברכה האחרון - כנראה זה הרלוונטי
          const blessingElement = blessingElements[blessingElements.length - 1];
          console.log(
            "מוסיף חתימה אחרי אלמנט הברכה:",
            blessingElement.textContent.trim()
          );

          // יצירת שורה חדשה עם פריסה רוחבית
          const signatureRow = document.createElement("div");
          signatureRow.style.cssText = `
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-top: 15px;
            position: relative;
            clear: both;
          `;

          // יצירת מיכל עבור החתימה שיוצב בצד שמאל
          const signatureContainer = document.createElement("div");
          signatureContainer.className = "signature-area";
          signatureContainer.style.cssText = `
            margin-left: 10px;
            align-self: flex-start;
            text-align: left;
            position: relative;
            z-index: 1;
          `;

          // יצירת תמונת החתימה
          const newSignature = document.createElement("img");

          // אם מצאנו חתימה של אבי ויצמן, השתמש בה
          if (aviWeizmanSignature) {
            newSignature.src = aviWeizmanSignature.src;
            newSignature.alt = "חתימת אבי ויצמן";
            console.log("משתמש בחתימת אבי ויצמן שנמצאה");
          } else {
            // אחרת, השתמש בחתימה שסופקה
            newSignature.src = signatures.yaron; // עדיין משתמשים בחתימה הקיימת
            newSignature.alt = "חתימה";
            console.log("משתמש בחתימת ברירת מחדל (ירון)");
          }

          newSignature.style.cssText = `
            width: 65px;
            height: auto;
            display: block;
            opacity: 0.85;
          `;

          // הוסף את החתימה למיכל
          signatureContainer.appendChild(newSignature);

          // הוסף את מיכל החתימה לשורה
          signatureRow.appendChild(signatureContainer);

          // יצירת אלמנט ריק עבור הצד הימני כדי לדחוף את החתימה שמאלה
          const spacerRight = document.createElement("div");
          spacerRight.style.cssText = `flex-grow: 1;`;
          signatureRow.insertBefore(spacerRight, signatureContainer);

          // מצא את האלמנט המתאים להוספת החתימה אחריו
          // אם אלמנט הברכה הוא חלק מטבלה, צריך למצוא את התא המתאים
          let targetParent = blessingElement;

          // בדוק אם אלמנט הברכה נמצא בתוך טבלה
          let isInTable = false;
          let parentElement = blessingElement.parentElement;
          while (parentElement && !isInTable) {
            if (
              parentElement.tagName === "TABLE" ||
              parentElement.tagName === "TD" ||
              parentElement.tagName === "TR"
            ) {
              isInTable = true;
              break;
            }
            parentElement = parentElement.parentElement;
          }

          if (isInTable) {
            // אם זה בטבלה, מצא את התא
            const tdParent = blessingElement.closest("td");
            if (tdParent) {
              // הוסף את שורת החתימה בתוך התא, אחרי אלמנט הברכה
              tdParent.appendChild(signatureRow);
            } else {
              // אם לא נמצא תא, הוסף אחרי אלמנט הברכה עצמו
              blessingElement.parentNode.insertBefore(
                signatureRow,
                blessingElement.nextSibling
              );
            }
          } else {
            // אם לא בטבלה, הוסף אחרי אלמנט הברכה
            blessingElement.parentNode.insertBefore(
              signatureRow,
              blessingElement.nextSibling
            );
          }

          console.log("החתימה נוספה מתחת לטקסט הברכה בצד שמאל");
        } else {
          console.log("לא נמצא טקסט ברכה - לא ניתן להוסיף חתימה במקום הנכון");
        }

        // 4. מניעת הוספת חתימות נוספות
        tempDiv.setAttribute("data-signatures-processed", "true");

        // 5. הוספת פוטר ליצירת קשר בתחתית המסמך
        try {
          console.log("מוסיף פוטר ליצירת קשר לתחתית דוח התקבולים");

          // בדיקה האם כבר קיים פוטר במסמך הסופי
          const existingFooter = tempDiv.querySelector(".contact-footer");
          if (existingFooter) {
            console.log("נמצא פוטר קיים במסמך - לא צריך להוסיף חדש");
          } else {
            // יצירת אלמנט הפוטר
            const footerElement = document.createElement("div");
            footerElement.className = "contact-footer";
            footerElement.style.cssText = `
              width: 100%;
              margin-top: 40px;
              padding-top: 15px;
              border-top: 1px solid #ccc;
              text-align: center;
              font-size: 10px;
              color: #666;
              position: relative;
              clear: both;
              direction: rtl;
            `;

            // מוסיף פוטר קבוע עם פרטי יצירת הקשר
            footerElement.innerHTML = `
              <div style="margin-bottom: 5px;">האגודה להתנדבות - שירות לאומי ע.ר. 580025708</div>
              <div style="margin-bottom: 5px;">רח' בית הדפוס 11, ירושלים | ת.ד 34616 ירושלים 9134601 | טלפון: 02-6521140 | פקס: 02-6525325</div>
              <div>דוא"ל: <EMAIL> | אתר: www.sherut-leumi.co.il</div>
            `;

            // הוסף את הפוטר לסוף המסמך
            tempDiv.appendChild(footerElement);
            console.log("פוטר יצירת קשר נוסף בהצלחה");
          }

          // הסרת מסגרות שחורות מסביב לתאריכים (בהורדה)
          try {
            // הוספת סגנון נוסף להסרת מסגרות תאריכים בהורדה
            const dateStyleFix = document.createElement("style");
            dateStyleFix.textContent = `
              /* הסרת מסגרות מתאריכים בהורדה בצורה אגרסיבית יותר */
              .date-field, .issue-date, .service-date, .period-date,
              [class*="date"], [id*="date"], [class*="תאריך"],
              span:has(time), div:has(time), time,
              *[data-content*="תאריך"], *[aria-label*="תאריך"] {
                border: none !important;
                background: transparent !important;
                box-shadow: none !important;
                -webkit-box-shadow: none !important;
                outline: none !important;
              }
              
              /* הסרת קישוטים שבצדדים */
              [class*="date"]::before, [class*="date"]::after,
              [id*="date"]::before, [id*="date"]::after,
              time::before, time::after,
              .date-field::before, .date-field::after,
              .issue-date::before, .issue-date::after {
                display: none !important;
                content: none !important;
                border: none !important;
                background: none !important;
              }
            `;
            tempDiv.appendChild(dateStyleFix);

            // טיפול ישיר באלמנטי תאריך
            const dateElements = tempDiv.querySelectorAll(
              '[class*="date"], [id*="date"], time, .date-field, .issue-date'
            );
            dateElements.forEach((el) => {
              el.style.border = "none";
              el.style.background = "transparent";
              el.style.boxShadow = "none";

              // בודק אם יש אלמנט הורה שמכיל את התאריך ומוריד גם ממנו
              let parent = el.parentElement;
              if (parent) {
                const parentClass = parent.className || "";
                if (parentClass.toLowerCase().includes("date")) {
                  parent.style.border = "none";
                  parent.style.background = "transparent";
                  parent.style.boxShadow = "none";
                }
              }
            });

            console.log("מסגרות תאריכים הוסרו בהצלחה");
          } catch (error) {
            console.error("שגיאה בהסרת מסגרות תאריכים:", error);
          }
        } catch (error) {
          console.error("שגיאה בהוספת פוטר ליצירת קשר:", error);
        }
      } catch (error) {
        console.error("שגיאה בטיפול האגרסיבי בדוח תקבולים:", error);
      }
    }

    // צור את ה-PDF
    generatePdfFromElement(
      tempDiv,
      filename,
      onComplete,
      onError,
      documentType
    );
  } catch (error) {
    console.error("שגיאה בהכנת PDF:", error);
    if (onError) onError(error);
  }
};
