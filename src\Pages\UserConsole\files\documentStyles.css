/* Document Styles for unified templates */

/* Basic reset and container style */
.document-container {
  font-family: "Rubik", "Arial Hebrew", Arial, sans-serif;
  direction: rtl;
  text-align: right;
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  line-height: 1.5;
  color: #222;
}

/* Document header with logo */
.document-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
  position: relative;
}

.header-right {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: 20px;
}

.logo-container {
  margin-bottom: 0;
  text-align: right;
}

.header-logo {
  max-width: 130px;
  height: auto;
}

.header-center {
  text-align: center;
  margin: 0;
}

.document-title {
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin: 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #ccc;
  color: #003366;
}

/* Document content */
.document-content {
  padding: 0 10px;
}

/* Specific document types styling */
/* Form 101 */
.form-details,
.certificate-details,
.report-details {
  margin: 20px 0;
}

.date-section {
  margin: 10px 0;
}

.date-field,
.service-date,
.period-date {
  font-weight: 500;
}

.service-dates,
.report-period {
  margin: 15px 0;
}

/* Responsive adjustments */
@media print {
  .document-container {
    padding: 0;
    font-size: 12px;
  }

  .header-logo {
    max-width: 100px;
  }

  .document-title {
    font-size: 20px;
  }
}

@media screen and (max-width: 768px) {
  .document-container {
    padding: 15px;
  }

  .header-logo {
    max-width: 110px;
  }

  .document-title {
    font-size: 20px;
  }
}

@media screen and (max-width: 480px) {
  .document-container {
    padding: 10px;
  }

  .header-logo {
    max-width: 90px;
  }

  .document-title {
    font-size: 18px;
  }
}

/* Common document elements */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

table,
th,
td {
  border: 1px solid #ddd;
}

th,
td {
  padding: 8px;
  text-align: right;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}

/* Ensure RTL for all document elements */
.document-container * {
  direction: rtl;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: bold;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

/* Document-specific styles */

/* Common date field styles */
.date-field,
.issue-date,
.service-date,
.period-date {
  font-family: Arial, sans-serif;
  direction: ltr; /* Force left-to-right for dates */
  display: inline-block;
  padding: 0 4px;
}

/* Form 101 specific styles */
.form-101 .date-section {
  margin: 10px 0;
}

.form-101 .date-field {
  font-weight: bold;
}

/* Service Period Certificate specific styles */
.service-period .certificate-details {
  margin: 15px 0;
}

.service-period .issue-date {
  color: #666;
}

.service-period .service-dates {
  margin-top: 10px;
}

.service-period .service-date {
  font-weight: bold;
}

/* Receipt Report specific styles */
.receipt-report .report-details {
  margin: 15px 0;
}

.receipt-report .issue-date {
  color: #666;
}

.receipt-report .report-period {
  margin-top: 10px;
}

.receipt-report .period-date {
  font-weight: bold;
}

/* עיצוב כללי למסמכים שמוצגים מהשרת */
.pdf-container {
  background: #fff;
  border-radius: 8px;
  padding: 32px 32px 24px 32px;
  max-width: 700px;
  margin: 0 auto;
  direction: rtl;
  font-family: "Assistant", Arial, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  font-size: 1.08rem;
}
.pdf-container h1,
.pdf-container h2 {
  text-align: center;
  color: #1a237e;
  margin-bottom: 24px;
  font-size: 2rem;
}
.pdf-container table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 18px;
  background: #fafbfc;
}
.pdf-container th,
.pdf-container td {
  border: 1px solid #d1d5db;
  padding: 10px 14px;
  text-align: right;
  font-size: 1rem;
}
.pdf-container th {
  background: #f1f5f9;
  font-weight: bold;
}
.pdf-container p,
.pdf-container div,
.pdf-container span {
  line-height: 1.7;
  color: #222;
}
.pdf-container ul,
.pdf-container ol {
  margin-right: 24px;
  margin-bottom: 16px;
}
.pdf-container img {
  max-width: 120px;
  height: auto;
  display: block;
  margin: 0 auto 12px auto;
}
.pdf-container .signature-area,
.pdf-container .signature-box {
  margin-top: 24px;
  text-align: right;
}
/* כפתורים במודל */
.filesModal .btnCont button,
.filesModal .btnCont .MuiButton-root {
  font-size: 1.1rem;
  padding: 10px 24px;
  border-radius: 8px;
}
/* רספונסיביות */
@media (max-width: 600px) {
  .pdf-container {
    padding: 12px 2vw 12px 2vw;
    font-size: 0.98rem;
  }
  .pdf-container h1,
  .pdf-container h2 {
    font-size: 1.2rem;
  }
  .pdf-container th,
  .pdf-container td {
    padding: 6px 4px;
    font-size: 0.95rem;
  }
}
