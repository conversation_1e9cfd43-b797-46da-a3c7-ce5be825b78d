<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Reports extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+OtzarHaaretz';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+OtzarHaaretz-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('OtzarHaretz');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        
        $this->load->model('OtzarHaretz');
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    
    public function reportMovments ($jPData = FALSE) {
        
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin','user'); //all //SuperAdmin  //adminOnly  //userOnly
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','startDate','endDate'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized' || $this->input->get('sebas')==1) {
            
            
            $this->db->select('*');
            $this->db->from('transactions');
            $this->db->where('status', 1);
            $this->db->order_by('created_at', 'DESC');
            
            $this->db->limit(100);
            
            
            $today = date("Y-m-d 00:00:00");
            $startDate = isset($jsonPosts['startDate']) && !empty($jsonPosts['startDate']) ? changeDateFormat($jsonPosts['startDate'].' 0:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;
            $endDate = isset($jsonPosts['endDate']) && !empty($jsonPosts['endDate']) ? changeDateFormat($jsonPosts['endDate'].' 24:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;

            $flag = array();

            if($startDate) {
                $this->db->where('created_at >=', $startDate );
            }

            if($endDate) {
                $this->db->where('created_at <=', $endDate );
            } 

            if( !$startDate && !$endDate ) {

                $this->db->where('created_at >=', $today );
            }
            
            
            $result= $this->db->get();
            $transaction = $result->result_array();
            
            if(!empty($transaction) ) {
                
                foreach ($transaction as $value) {
                    $data[] = $this->getTransactionDataDb($value, $value['userId']);
                }
                
                $output['data'] = $data;
                
            } else {
                $output['data'] = array();
            } 
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    
        
    }
    
    
    
    
    public function reportSuppliers($jPData = FALSE) {
        
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin','user'); //all //SuperAdmin  //adminOnly  //userOnly
        
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','startDate','endDate'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            
            $this->db->select('supplierId,money,created_at');
            $this->db->from('transactions');
            $this->db->where('status', 1);
            $this->db->order_by('created_at', 'DESC');
            
            $result= $this->db->get();
            $transaction = $result->result_array();
            
            if(!empty($transaction) ) {
                
                $data = array();
                $data = $this->getArraySuppliersReport($transaction);
                
                $output['data'] = $data['data'];
                
                
            } else {
                $output['data'] = array();
            } 
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    
        
    }
    
    
    
    private function getArraySuppliersReport($transaction = FALSE) {
        
        if($transaction) {
            
        
            foreach ($transaction as $value) {
                $sortedtransaction[$value['supplierId']][] = $value;
            }


            foreach ($sortedtransaction as $supplierTransactions) {

                //echo "<pre>";
                //print_r($value);die();

                $money = 0;
                $allMoney = 0;
                $moneyMonth = array();

                foreach ($supplierTransactions as $supplierTransaction) {
                    $allMoney = $allMoney + (float)$supplierTransaction['money'];
                    $dateKey = changeDateFormat($supplierTransaction['created_at'], 'Y-m-d H:i:s', 'n-y');
                    $moneyMonth[$dateKey][] = $supplierTransaction['money'];
                }

                $this->db->select('name,id,tz');
                $this->db->from('suppliers');
                $this->db->where('id', $supplierTransaction['supplierId']);
                $result= $this->db->get();
                $supplierName = $result->row_array();
                
                $data[] = array(
                    'id' => $supplierName['id'],
                    'tz' => $supplierName['tz'],
                    'name' => $supplierName['name'],
                    'allMoney' => $allMoney,
                    'money' => $moneyMonth
                );
                
            }
                
//        echo "<pre>";
//        print_r($data);
//        die();

        $year1 = '21';
        $year2 = '22';

        foreach ($data as $value) {


            $monyMonths = array();
            
            for ($index = 7; $index <= 22; $index++) {

                if($index > 12) {
                    
                    $newIndex = $index - 12;
                    
                    $key = $newIndex.'-'.$year2;
                    
                } else {
                    $key = $index.'-'.$year1;
                }
                
                

                if(isset($value['money'][$key])) {

                    $monyMonths[$key] = $this->sumThis($value['money'][$key]);

                }
//                else {
//                    $monyMonths[$key] = 0;
//                }

            }

            if(!empty($value['money'])) {

                $money = 0;
                foreach ($value['money'] as $key => $money) {

                    $money = $money + $value['money'];
                    $value['money'][$key] = $money;

                }


            }


            $finalData[] = array(
                'id' => $value['id'],
                'name' => $value['name'],
                'tz' => $value['tz'],
                'allMoney' => $value['allMoney'],
                'monyMonths' => $monyMonths

            );
                    
        }
            
            $return['data'] = $finalData;
            
            return $return;
            
        } else {
            
            return false;
            
        }

        
        
        
    }

    


    private function sumThis($moneyArray) {
        
        
        if(!empty($moneyArray)) {
            
            $count = 0;
            foreach ($moneyArray as $value) {
                
                
                $count = $count + $value;
                
            }
            
            return $count;
            
        } else {
            
            return 0;
            
        }
        
        
    }

    















    public function csvReportMovments() {
        
        
        //let token = md5(user.id + user.phone);
        //let csvDownload = ConstantsNames.base_url + 'Suppliers/csv?token=' + token + '&id=' +  md5(user.id);
        
        $token = $this->input->get('token');
        $id = $this->input->get('id');
        
        //https://otzarhaaretz.wdev.co.il/api/reports/csvReportMovments?token=d69fe15b473b7c6d7ae09e60a4dd586a&id=c4ca4238a0b923820dcc509a6f75849b&startDate=NaN-NaN-NaN&endDate=NaN-NaN-NaN
        
        $jsonPosts['startDate'] = (!empty($this->input->get('startDate')) &&  $this->input->get('startDate') != 'NaN-NaN-NaN') ? $this->input->get('startDate') : '';
        $jsonPosts['endDate'] = (!empty($this->input->get('endDate')) && $this->input->get('endDate') != 'NaN-NaN-NaN') ? $this->input->get('endDate') : '';
                
        $this->db->select('id,phone');
        $this->db->from('app_Users');
        $this->db->where('MD5(id)', $id);
        $this->db->where('status', 1);
        
        $result= $this->db->get();
        $user = $result->row_array();
        
        if( md5($user['id'].$user['phone']) !== $token ) {die('ERROR');}
        
            $this->db->select('*');
            $this->db->from('transactions');
            $this->db->where('status', 1);
            $this->db->order_by('created_at', 'DESC');
            
            
            $today = date("Y-m-d 00:00:00");
            $startDate = isset($jsonPosts['startDate']) && !empty($jsonPosts['startDate']) ? changeDateFormat($jsonPosts['startDate'].' 0:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;
            $endDate = isset($jsonPosts['endDate']) && !empty($jsonPosts['endDate']) ? changeDateFormat($jsonPosts['endDate'].' 24:00:00', 'Y-m-d H:i:s', 'Y-m-d H:i:s') : false;

            $flag = array();

            if($startDate) {
                $this->db->where('created_at >=', $startDate );
            }

            if($endDate) {
                $this->db->where('created_at <=', $endDate );
            } 

            if( !$startDate && !$endDate ) {

                $this->db->where('created_at >=', $today );
            }
            
            
            $result= $this->db->get();
            $transaction = $result->result_array();
            
            if(!empty($transaction) ) {
                
                $csv = array();
        
                $csv[] = array (
                    '1' => 'זיהוי',
                    '2' => 'תאריך ושעה',
                    '3' => 'שם ספק',
                    '4' => 'שם הלקוח',
                    '5' => 'אסמכתא',
                    '6' => 'שם קופה',
                    '7' => 'סכום (₪)'
                );
                
                
                foreach ($transaction as $value) {
                    
                    $data = $this->getTransactionDataDb($value, $value['userId']);
                    
                    $csv[] = array (
                        '1' => $data['id'],
                        '2' => changeDateFormat($data['dateTransaction'], 'Y-m-d H:i:s', 'H:i d/m/Y'),
                        '3' => $data['name'],
                        '4' => $data['nameBuyer'],
                        '5' => '"'.$data['token'].'"',
                        '6' => $data['cashier'],
                        '7' => $data['money']
                    );
                    
                }
                
                $data = $csv;
                $month = date('M');  //date('m');
                $filename = $month.'_'.date('Y').'_'.rand(1,9999);
                
                header('Content-Encoding: UTF-8'); 
                header('Content-type: text/csv; charset=UTF-8');
                header('Content-Disposition: attachment; filename='.$filename.'.csv');
                header("Pragma: no-cache");
                header("Expires: 0");

                $handle = fopen('php://output', 'w');
                fwrite($handle, "\xEF\xBB\xBF");


                foreach ($data as $data_array) {
                    fputcsv($handle, $data_array);
                }
                    fclose($handle);
                exit;
                
                //echo "<pre>";
                //print_r($csv);
                
            }
        
        
        
        
        
    }
    
    
    
    
    
    
    
    
    
     
     public function csvReportSupplier() {
        
        
        //let token = md5(user.id + user.phone);
        //let csvDownload = ConstantsNames.base_url + 'Suppliers/csv?token=' + token + '&id=' +  md5(user.id);
        
        $token = $this->input->get('token');
        $id = $this->input->get('id');
        
        $jsonPosts['startDate'] = $this->input->get('startDate');
        $jsonPosts['endDate'] = $this->input->get('endDate');
                
        $this->db->select('id,phone');
        $this->db->from('app_Users');
        $this->db->where('MD5(id)', $id);
        $this->db->where('status', 1);
        
        $result= $this->db->get();
        $user = $result->row_array();
        
        //print_r($user);
        
        if( md5($user['id'].$user['phone']) !== $token ) {die('ERROR');}
        
            
            $this->db->select('supplierId,money,created_at');
            $this->db->from('transactions');
            $this->db->where('status', 1);
            $this->db->order_by('created_at', 'DESC');
            
            $result= $this->db->get();
            $transaction = $result->result_array();
            
            if(!empty($transaction) ) {
                
                $data = array();
                $data = $this->getArraySuppliersReport($transaction);
            }
        
            if(!empty($data) ) {
                
                $csv = array();
        
                $csv[] = array (
                    '1' => 'שם ספק',
                    '1a' => 'ח"פ',
                    '2' => '09/21',
                    '3' => '10/21',
                    '4' => '11/21',
                    '5' => '12/21',
                    '6' => '01/22',
                    '7' => '02/22',
                    '8' => '03/22',
                    '9' => '04/22',
                    '10' => '05/22',
                    '11' => '06/22',
                    '12' => '07/22',
                    '13' => '08/22',
                    '14' => '09/22',
                    '15' => '10/22',
                    '16' => 'סה"כ  (₪)');
                
                //echo "<pre>";
                //print_r($data);die();
                
                foreach ($data['data'] as $value) {
                    
                    $csv[] = array (
                        '1' => $value['name'],
                        '1a' => $value['tz'],
                        '2' => isset($value['monyMonths']['9-21']) ? $value['monyMonths']['9-21'] : '',
                        '3' => isset($value['monyMonths']['10-21']) ? $value['monyMonths']['10-21'] : '',
                        '4' => isset($value['monyMonths']['11-21']) ? $value['monyMonths']['11-21'] : '',
                        '5' => isset($value['monyMonths']['12-21']) ? $value['monyMonths']['12-21'] : '',
                        '6' => isset($value['monyMonths']['1-22']) ? $value['monyMonths']['1-22'] : '',
                        '7' => isset($value['monyMonths']['2-22']) ? $value['monyMonths']['2-22'] : '',
                        '8' => isset($value['monyMonths']['3-22']) ? $value['monyMonths']['3-22'] : '',
                        '9' => isset($value['monyMonths']['4-22']) ? $value['monyMonths']['4-22'] : '',
                        '10' => isset($value['monyMonths']['5-22']) ? $value['monyMonths']['5-22'] : '',
                        '11' => isset($value['monyMonths']['6-22']) ? $value['monyMonths']['6-22'] : '',
                        '12' => isset($value['monyMonths']['7-22']) ? $value['monyMonths']['7-22'] : '',
                        '13' => isset($value['monyMonths']['8-22']) ? $value['monyMonths']['8-22'] : '',
                        '14' => isset($value['monyMonths']['9-22']) ? $value['monyMonths']['9-22'] : '',
                        '15' => isset($value['monyMonths']['10-22']) ? $value['monyMonths']['10-22'] : '',
                        '16' => $value['allMoney']);
                    
                }
                
                $data = $csv;
                $month = date('M');  //date('m');
                $filename = $month.'_'.date('Y').'_'.rand(1,9999);
                
                header('Content-Encoding: UTF-8'); 
                header('Content-type: text/csv; charset=UTF-8');
                header('Content-Disposition: attachment; filename='.$filename.'.csv');
                header("Pragma: no-cache");
                header("Expires: 0");

                $handle = fopen('php://output', 'w');
                fwrite($handle, "\xEF\xBB\xBF");


                foreach ($data as $data_array) {
                    fputcsv($handle, $data_array);
                }
                    fclose($handle);
                exit;
                
                //echo "<pre>";
                //print_r($csv);
                
            }
        
        
        
        
        
    }
     
     
     
     
     
     
     
     
     
    
    
    private function getTransactionDataDb($transaction, $userId=false) {
        
        if($userId) {
            
            $this->db->select('phone,firstName,lastName');
            $this->db->from('leadsLandpage');
            $this->db->where('id', $userId);
            $result= $this->db->get();
            $user = $result->row_array();
            
        } else {
            $user = array();
        }
        
        $this->db->select('name,phone');
        $this->db->from('suppliers');
        $this->db->where('id', $transaction['supplierId']);
        $result= $this->db->get();
        $supplierData = $result->row_array();

        $this->db->select('name');
        $this->db->from('cashiersSuppliers');
        $this->db->where('id', $transaction['cashierId']);
        $result= $this->db->get();
        $cashierData = $result->row_array();

        if(!empty($supplierData) && !empty($cashierData) ) {

            $firstName = isset($user['firstName']) ? $user['firstName'] : ''; 
            $lastName = isset($user['lastName']) ? $user['lastName'] : ''; 
            
            $name = $firstName.' '.$lastName;
            
            $return = array(
                'id' => isset($transaction['id']) ? $transaction['id'] : '',
                'dateTransaction' => isset($transaction['created_at']) ? $transaction['created_at'] : '',
                'nameBuyer' => $name,
                'userPhone' => isset($user['phone']) ? $user['phone'] : '',
                'name' => $supplierData['name'],
                'token' => $transaction['token'],
                'cashier' => $cashierData['name'],
                'money' => $transaction['money'],
                'supplierPhone' => $supplierData['phone']
            );
            
            return $return;
            
        } else {

            return false;

        }
    
    }
    
    
       
}