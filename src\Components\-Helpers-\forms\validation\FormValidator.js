import React, { Component } from "react";
import { ValidateData } from "./validation";
import { ValidationContext } from "./ValidationContext";
//import { Button } from 'react-bootstrap';

export class FormValidator extends Component {

    

    constructor(props) {
        super(props);
        this.state = {
            errors: {},
            dirty: {},
            formSubmitted: false,
            getMessagesForField: this.getMessagesForField
        }
    }

    static getDerivedStateFromProps(props, state) {
        state.errors = ValidateData(props.data, props.rules);
        if (state.formSubmitted && Object.keys(state.errors).length === 0) {
            let formErrors = props.validateForm(props.data);
            if (formErrors.length > 0) {
                
                state.errors.form = formErrors;
            }
        }
        return state;
    }

    get formValid() {
        return Object.keys(this.state.errors).length === 0;
    }

    handleChange = (ev) => {

        

        let name = ev.target.name;
        this.setState(state => state.dirty[name] = true, ()=>{
            this.props.isOK(this.state.errors);
        });
        
    }

    getButtonClasses() {
        return this.state.formSubmitted && !this.formValid 
            ? 'secondary' : 'success';
    }


    getMessagesForField = (field) => {
        return (this.state.formSubmitted || this.state.dirty[field]) ?
            this.state.errors[field] || [] : []
    }

    render() {

       

        return <React.Fragment>


            <ValidationContext.Provider value={ this.state }>
                <div onChange={ this.handleChange }>
                    { this.props.children }            
                </div>
            </ValidationContext.Provider>
            
            {/* <Button 
                className = "submitValidationFormBtn"
                style={{width : '100%' }}
                disabled={this.state.formSubmitted && !this.formValid}
                variant={this.getButtonClasses()}
                size="lg"
                onClick={this.handleClick}>{this.props.btnText}</Button> */}
            
            
        </React.Fragment>
    }
}
