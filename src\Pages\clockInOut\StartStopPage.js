import React, { useEffect, useState } from "react";
import { Form } from "react-bootstrap"; //npm install react-bootstrap@next bootstrap@5.1.0
import { map } from "lodash";
import {
  checkhasPerutDivuach,
  checkTextAreaReportContent,
  getClockApiMaakavIn,
  getClockApiMaakavOut,
  getClockApiMaakavStatus,
} from "./clockFunctions";

import loader from "../../img/preLoader.gif";
import note from "../../img/icons/clockPage/note.svg";
import icoReports from "../../img/icons/clockPage/icoReports.svg";

import ReactStopwatch from "react-stopwatch"; //npm i react-stopwatch    https://www.npmjs.com/package/react-stopwatch --   https://codesandbox.io/s/jolly-glade-e0yy8k?file=/src/App.js:23-68

import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

import { toast } from "react-toastify";

import moment from "moment"; //npm install moment
import { getTypeRecordsList } from "./editClockFunctions";
import { getSafeUserData } from "../../context/AuthContext";

export default function StartStopPage(props) {
  const { setSelectedPage } = props;

  const [loading, setLoading] = useState(false);
  const [startButtonLoading, setStartButtonLoading] = useState(false);

  const [reportTypeList, setReportsTypesList] = useState(false);

  const [selectedReportType, setSelectedReportType] = useState(false);
  const [textAreaReport, setTextAreaReport] = useState(false);
  const [percentWorked, setPercentWorked] = useState(false);

  //const [responseApiTemp, setResponseApiTemp] = useState(false);

  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);

  const [MaakavStatus, setMaakavStatus] = useState(false);
  const [showSelectError, setShowSelectError] = useState(false);

  const [lastExit, setLastExit] = useState(false);

  const [timeWorked, setTimeWorked] = useState({ hours: '00', minutes: '00' });

  const [elapsedTime, setElapsedTime] = useState("00:00:00");

  let userJ = getSafeUserData();

  //reportTypesList
  useEffect(() => {
    if (!loading && !reportTypeList) {
      setReportsTypesList({
        data: {
          Items: getTypeRecordsList(),
        },
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* setMaakavStatus */
  useEffect(() => {
    loadMaakavStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    console.log('MaakavStatus log', MaakavStatus);
  }, [MaakavStatus]);

  function loadMaakavStatus() {
    const sendObj = { IDNumber: userJ.IDNO, SessionKey: userJ.SessionKey };
    getClockApiMaakavStatus(
      "/api/v2/volunteer/MaakavStatus",
      sendObj,
      setLoading,
      setMaakavStatus,
      setSelectedReportType,
      setTextAreaReport
    );
  }

  //After API set clock if IN
  useEffect(() => {
    if (MaakavStatus?.timeAllValues?.utcTime) {
      const updateElapsedTime = () => {
        const israelTZ = 'Asia/Jerusalem';
        let now = moment().tz(israelTZ);
        let initialDateObject = moment.utc(MaakavStatus.timeAllValues.utcTime).tz(israelTZ);
        const duration = moment.duration(now.diff(initialDateObject));
        
        const hours = Math.floor(duration.asHours());
        const minutes = Math.floor(duration.asMinutes()) % 60;
        const seconds = Math.floor(duration.asSeconds()) % 60;

        setElapsedTime(
          `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        );

        // Update progress circle
        const percent = Math.min(Math.round((duration.asHours() * 100) / 8), 100);
        setPercentWorked(percent > 0 ? percent : 5);
      };

      // Update immediately
      updateElapsedTime();

      // Then update every second
      const timer = setInterval(updateElapsedTime, 1000);
      return () => clearInterval(timer);
    }
  }, [MaakavStatus]);

  const startHandler = () => {
    if (startButtonLoading) return;

    if (!selectedReportType) {
      setShowSelectError(true);
      toast.error("יש לבחור סוג דיווח", {
        toastId: "selectedReportType",
      });

      //console.log('startStopHandler');
    } else if (
      checkhasPerutDivuach(selectedReportType, textAreaReport) &&
      !textAreaReport
    ) {
      setShowSelectError(true);
      toast.error("יש למלא פירוט דיווח", {
        toastId: "checkhasPerutDivuach",
      });
    } else if (
      !checkTextAreaReportContent(selectedReportType, textAreaReport)
    ) {
      //setShowSelectError(true);
      return false;
    } else {
      setShowSelectError(false);

      if (MaakavStatus?.api?.IsCurrIn) {
        toast.error("יש לעשות יציאה קודם", {
          toastId: "MaakavStatus",
        });
      } else {
        setStartButtonLoading(true);
        
        const sendObj = {
          IDNumber: userJ.IDNO,
          SessionKey: userJ.SessionKey,
          Typ: selectedReportType,
          MoreInfo: textAreaReport ? textAreaReport : '',
        };

        //console.log('Maakavin >', sendObj);

        getClockApiMaakavIn(
          "/api/v2/volunteer/Maakavin",
          sendObj,
          setLoading,
          (status) => {
            setMaakavStatus(status);
            setStartButtonLoading(false);
          },
          setSelectedReportType,
          setTextAreaReport
        );
      }
    }
  };

  //console.log('maakavStatus', MaakavStatus);

  const [showPulse, setShowPulse] = useState(false);

  const stopHandler = () => {
    if (MaakavStatus?.api?.IsCurrIn) {
      const sendObj = { IDNumber: userJ.IDNO, SessionKey: userJ.SessionKey };

      getClockApiMaakavOut(
        "/api/v2/volunteer/MaakavOut",
        sendObj,
        setLoading,
        setMaakavStatus,
        setSelectedReportType,
        setTextAreaReport,
        setPercentWorked,
        setLastExit
      );
    } else {
      setShowPulse("animate__animated animate__tada animate__repeat-2");
      setTimeout(() => {
        setShowPulse(false);
      }, 4000);

      toast.error("יש לעשות כניסה קודם או לפתוח דיווח ידני");
    }
  };

  useEffect(() => {
  
    //console.log('selectedReportType', selectedReportType);
    
    if(selectedReportType === '2') {

      setTextAreaReport('הכשרת שרות לאומי');

    } else if(selectedReportType === '3') {

      setTextAreaReport('נוכחות מחוץ למקום השרות');

    } else {

      setTextAreaReport('');
      
    }
    
  }, [selectedReportType])
  

  return (
    <div className="StartStopPage animate__animated animate__fadeIn">
      <header className="clear">
        <h1>נוכחות</h1>
        {/* <p>תודה שבחרת להתנדב איתנו!</p> */}
      </header>

      <img
        src={loader}
        alt="loader"
        className={
          !loading
            ? "loader"
            : "loader active animate__animated animate__fadeIn"
        }
      />

      {/* reportType SELECT */}
      {reportTypeList?.data?.Items && (
        <div className="selectCont">
          <img src={note} alt="note" />

          <Form.Control
            as="select"
            className={"chooseReportSelect"}
            isInvalid={showSelectError && !selectedReportType}
            disabled={MaakavStatus?.api?.IsCurrIn}
            /*isValid={formik.touched.fullname && !formik.errors.fullname}  */
            //type={ config.typeInput }
            //name='reportType'
            onChange={(e) => setSelectedReportType(e.target.value)}
            value={selectedReportType}
            //onFocus={this.populateSelect}
          >
            <option value="">סוג דיווח</option>

            {map(reportTypeList.data.Items, (item) => {
              return (
                <option value={item.id} key={item.id}>
                  {item.name}
                </option>
              );
            })}
          </Form.Control>
        </div>
      )}

      {checkhasPerutDivuach(selectedReportType, textAreaReport) && (
        <div className="textAreaCont">
          <Form.Control
            as="textarea"
            className={"textAreaReport"}
            disabled={MaakavStatus?.api?.IsCurrIn}
            isInvalid={
              showSelectError &&
              checkhasPerutDivuach(selectedReportType, textAreaReport) &&
              !textAreaReport
            }
            /*isValid={formik.touched.fullname && !formik.errors.fullname}  */
            //type={ config.typeInput }
            //name='reportType'
            onChange={(e) => setTextAreaReport(e.target.value)}
            value={
              textAreaReport && textAreaReport !== "False" ? textAreaReport : ""
            }
            placeholder="פירוט דיווח"
            //onFocus={this.populateSelect}
          />
        </div>
      )}

      <div
        className={`buttonsDown ${
          MaakavStatus?.api?.IsCurrIn ? "isIn" : "isOut"
        }`}
      >
        <button
          className={`start ${
            selectedReportType && !startButtonLoading
              ? "animate__animated animate__bounceIn"
              : "disabled"
          }`}
          onClick={startHandler}
          disabled={startButtonLoading}
        >
          <span>{startButtonLoading ? "מתחבר..." : "כניסה"}</span>
        </button>
        <button className="stop" onClick={stopHandler}>
          <span>יציאה</span>
        </button>
      </div>

      <button
        className="border-1 hover:border-1 hover:border-blue-600 flex items-center mx-auto justify-center rounded-md py-2 px-4 bg-white shadow-md hover:bg-gray-50 transition-all"
        onClick={() => setSelectedPage("reportsPage")}
      >
        <img src={icoReports} alt="icoReports" />
        <span>דיווחי נוכחות</span>
      </button>

      {/* START CLOCK:  */}
      {MaakavStatus?.api?.IsCurrIn ? (
        <div className={`playStopBtn ${!selectedReportType && "disabled"} `}>
          <div className="buttonCont stopBtn">
            {MaakavStatus?.api?.IsCurrIn && (
              <div className="clock">
                <div className="clockNumbers">{elapsedTime}</div>
                <p>
                  {MaakavStatus?.weekday}, {MaakavStatus?.day}
                </p>
                <div className="showInTime">
                  <h3>התקבלה כניסה בשעה</h3>
                  <span>{MaakavStatus?.time}</span>
                </div>
              </div>
            )}

            <CircularProgressbar
              value={percentWorked || 0}
              text=""
              strokeWidth={4}
              styles={buildStyles({
                pathTransitionDuration: 0.5,
                pathColor: '#1BD591',
                trailColor: "white",
                backgroundColor: "#3e98c7",
                pathTransition: 'stroke-dashoffset 0.5s ease 0s',
                strokeLinecap: 'round',
                transform: `rotate(0.25turn)`,
                transformOrigin: 'center center',
              })}
            />
          </div>
        </div>
      ) : (
        /* STOP CLOCK: */
        <div
          className={`playStopBtn ${
            !selectedReportType && !lastExit ? "disabled" : ""
          } `}
        >
          <div className="buttonCont stopBtn">
            <div className="clock">
              <div className="clockNumbers">00:00:00</div>
              <p>
                {moment().locale('he').format('dddd')}{" "}
                {moment().format('DD/MM/YY')}
              </p>
              {lastExit && (
                <div className="lastExit">
                  <p>יציאה אחרונה ב-</p>
                  <strong>{lastExit}</strong>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
