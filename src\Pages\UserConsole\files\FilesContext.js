/**
 * FilesContext - קונטקסט React לניהול מצב הקבצים
 * 
 * מאפשר שיתוף נתונים וחיבור בין קומפוננטים שונים
 * מנהל מצב הטעינה ונתונים משותפים של קבצים ברמת האפליקציה
 */
import React, { createContext, useState, useContext } from 'react';

// Create a context for the files functionality
export const FilesContext = createContext();

// Custom hook to use the files context
export const useFilesContext = () => {
  return useContext(FilesContext);
};

// Provider component that wraps the files components and provides the context values
export const FilesProvider = ({ 
  children, 
  infoUser = false,
  setInfoUser,
  loading = false,
  setLoading,
  responseData = false,
  setResponseData
}) => {
  // No internal state - use the state from the parent component
  
  // Values to be provided to consumers
  const value = {
    infoUser,
    setInfoUser,
    loading,
    setLoading,
    responseData,
    setResponseData
  };

  return (
    <FilesContext.Provider value={value}>
      {children}
    </FilesContext.Provider>
  );
}; 
