<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Welcome extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+asd';
        $this->data['current_language'] = 'he';
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
        
//        $this->load->helper('cookie');
//        if($this->input->get('language')) {
//            $cookie = array(
//                'name'   => 'current_language',
//                'value'  => $this->input->get('language'),
//                'expire' =>  86500,
//                'secure' => false
//            );
//            set_cookie($cookie); 
//            redirect(base_url());
//        } else if($lang = get_cookie('current_language', false)) {
//            $this->data['current_language'] = $lang;
//        } else {
//            $lang_array = explode('_', locale_accept_from_http($this->input->server('HTTP_ACCEPT_LANGUAGE')));
//            $langs = $this->config->item('available_lang');
//            $lang = isset($lang_array[0]) && isset($langs[$lang_array[0]]) ? $lang_array[0] : 'he';
//            $cookie = array(
//                'name'   => 'current_language',
//                'value'  => $lang,
//                'expire' =>  86500,
//                'secure' => false
//            );
//            set_cookie($cookie); 
            //$this->data['current_language'] = 'he';
            //$this->data['current_language'] = $lang;
        //}
        
        $this->data['platform'] = "desktop";
        
        
        $this->data['folderView'] = $this->folderView = $this->data['current_language'] . '/' . $this->data['platform'] . '/';
        
        if($is_error) {
            set_status_header($is_error);
            $this->data['controller'] = 'welcome';
            $this->data['method'] = 'error404';
            $this->data['param'] = $param;
        } else {
            $this->data['controller'] = $this->router->fetch_class();
            $this->data['method'] = $this->router->fetch_method();
            $this->data['param'] = $param;
        }
        
        $this->data['settings'] = $this->msite->get_settings();
        
        $this->data['pages'] = $this->msite->get_all_pages(0);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        $this->data['body_class'] = $this->data['controller'] . '-' . $this->data['method'];
        
        
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['welcome_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id(),$this->data['current_language']);
        $seo_id = $this->msite->get_seo_id($this->data['controller'], $this->data['method'], $this->data['param']);
        $this->data['page']->Set('seo', $this->msite->get_seo($seo_id));
        $this->data['alert'] = $this->session->flashdata('alert', false);
        
        $this->data['accessibility'] = 
                $this->session->has_userdata('accessibility') ? 
                $this->session->userdata('accessibility') : 
                array('zoom' => 0, 'accessibility' => false, 'display' => false, 'links' => false, 'texts' => false);
        
        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    
    
    public function error404() {
        
        set_status_header(404);
        die('not Found');
        $this->_loader(FALSE, 404);
        
        $this->data['view'] = 'error/index';
        $this->load->view($this->folderView . 'index', $this->data);
    }
    
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
        
//        if( !$this->input->get('code') or $this->input->get('code') != md5($this->data['code']) ) {
//            die('error');
//        }
        
        $this->load->model('msiteWs');
        
        
        $this->load->helper('cookie');
        $this->data['current_language'] = 'he';
        
        if($is_error) {
            set_status_header($is_error);
            $this->data['controller'] = 'welcome';
            $this->data['method'] = 'error404';
            $this->data['param'] = $param;
        } else {
            $this->data['controller'] = $this->router->fetch_class();
            $this->data['method'] = $this->router->fetch_method();
            $this->data['param'] = $param;
        }
        
        
        $this->data['settings'] = $this->msiteWs->get_settings();
        //$this->data['pages'] = $this->msite->get_all_pages(0);
        
        //print_r($this->data['pages']);die();
        //$this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        
        
        //$page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['first_index'];
        //$this->data['page'] = $this->msite->get_page_with_objects($page->Id(),$this->data['current_language']);
        
        
        //$seo_id = $this->msite->get_seo_id($this->data['controller'], $this->data['method'], $this->data['param']);
        //$this->data['page']->Set('seo', $this->msite->get_seo($seo_id));
        $this->data['alert'] = $this->session->flashdata('alert', false);
        
        $this->data['accessibility'] = 
                $this->session->has_userdata('accessibility') ? 
                $this->session->userdata('accessibility') : 
                array('zoom' => 0, 'accessibility' => false, 'display' => false, 'links' => false, 'texts' => false);
        

        
        
    }
    
    public function index($param = FALSE) {
        $this->_loaderWS($param);
        
        
        $this->data = $output['welcome'] = 'welcome';
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    public function googleSheets($param=false) {
        
        //https://sherut-leumi.wdev.co.il/api/welcome/googleSheets
        echo "test";
        
    }
    
}





