import React from "react";

import bulletLev from "../../img/icons/menuIcons/lev.svg";
import userData from "../../img/icons/menuIcons/pratim.svg";
import exit from "../../img/icons/menuIcons/exit.svg";
import mekomotSherut from "../../img/icons/menuIcons/mekomotSherut.svg";
import doccumentIco from "../../img/icons/menuIcons/doccumentIco.svg";
import clockInOutIndex from "../../img/icons/menuIcons/timer.svg";
import contact from "../../img/icons/menuIcons/envelope.svg";
import digitalCard from "../../img/icons/menuIcons/digitalCard.svg";

const bulletIcons = {
  bulletLev,
  userData,
  files: doccumentIco,
  logOff: exit,
  sherutPlaces: mekomotSherut,
  clockInOutIndex,
  contact,
  digitalCard,
};

const BulletsMenu = ({ name, sayarotNumber }) => {
  const icon = bulletIcons[name];

  if (!icon) return null;

  return (
    <div className="inline-block">
      <div className="relative">
        {name === "bulletLev" && (
          <div className="absolute -top-1 -right-4 bg-[#f5325c] border-2 border-[#fbadbe] rounded-full text-xs h-6 w-6 text-center pt-0.5">
            {sayarotNumber}
          </div>
        )}
        <img className="w-[30px] mr-0 ml-5" src={icon} alt={`${name} icon`} />
      </div>
    </div>
  );
};

export default BulletsMenu;
