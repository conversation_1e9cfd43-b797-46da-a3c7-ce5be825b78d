<?php if($objects) { ?>
<div class="row">
    <div class="col-md-12">
        <h4>
            <?php if($this->input->get('return')): ?>
            <a href="<?php echo $this->input->get('return') ? $this->input->get('return') : base_url('admin/ObjectAdmin/index/' . $migration['table'] . '/' . getQS()); ?>" class=""><span class="glyphicon glyphicon-chevron-right"></span> חזור</a>
            <br/><br/>
            <?php endif;?>
            <?php echo $migration['explain']; ?>
            <small>
                <?php showInfo($total, $limit, $page); ?>
            </small>
        </h4>
    </div>
    
    <?php if($this->input->get('Id')):?>
        
        <?php foreach($objects as $row): echo $row->TextPreview($migration['description']); if($row->TextPreview($migration['title']) == $this->input->get('Id')) { ?>
    
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="thumbnail text-center" style="padding: 0;background-color: whitesmoke;">
                    <div class="absolute-action-btn" style="top: 10px; left: 10px;">
                        <button type="button" class="btn pulse-hover btn-default duplicate" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/duplicate/' . $row->Id() . '/' . getQS()); ?>">
                            שכפל
                        </button> 
                    </div>
                    <div class="absolute-action-btn" style="top: 10px; right: 10px;">
                        <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/status/' . $row->Id() . '/' . getQS()); ?>">
                            <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                        </button> 
                    </div>


                    <?php if(isset($migration['image']) && !empty($migration['image'])) { ?>
                    <div class="wrapper-img" style="height: 160px; overflow: hidden;">
                        <img class="materialboxed" style="width: 100%;margin-top: 0px;" src="<?php echo $row->Img($migration['image']) ? $row->Img($migration['image']) : base_url(IMG . 'admin/small_logo.jpg'); ?>">
                    </div>
                    <?php } ?>
                    <div class="caption">
                        <?php if(isset($migration['title']) && !empty($migration['title'])) { ?>
                        <h3 style="height: 24px; overflow: hidden;text-overflow: ellipsis;margin-top: 0;"><?php echo $row->Arg($migration['title']); ?></h3>
                        <?php } ?>
                        <?php if(isset($migration['description']) && !empty($migration['description'])) { ?>
                        <div style="height: 60px; overflow: hidden;text-overflow: ellipsis;"><?php echo $row->TextPreview($migration['description']); ?></div>
                        <?php } ?>

                        <hr/>
                        <div class="input-group">
                            <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $row->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/sort/' . $row->Id() . '/'); ?>">
                            <div class="input-group-btn">
                                <a class="btn btn-default pulse-hover" href="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/show/' . $row->Id() . '/' . getQS()); ?>" role="button">עריכה</a>

                                <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/destroy/' . $row->Id() . '/' . getQS()); ?>">
                                    מחק
                                </button> 
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <?php }; endforeach; ?>
    <?php else: ?>
    
    <?php foreach($objects as $row) { ?>
    
    <div class="col-lg-3 col-md-4 col-sm-6">
        <div class="thumbnail text-center" style="padding: 0;background-color: whitesmoke;">
            <div class="absolute-action-btn" style="top: 10px; left: 10px;">
                <button type="button" class="btn pulse-hover btn-default duplicate" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/duplicate/' . $row->Id() . '/' . getQS()); ?>">
                    שכפל
                </button> 
            </div>
            <div class="absolute-action-btn" style="top: 10px; right: 10px;">
                <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/status/' . $row->Id() . '/' . getQS()); ?>">
                    <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                </button> 
            </div>
            
            
            <?php if(isset($migration['image']) && !empty($migration['image'])) { ?>
            <div class="wrapper-img" style="height: 160px; overflow: hidden;">
                <img class="materialboxed" style="width: 100%;margin-top: 0px;" src="<?php echo $row->Img($migration['image']) ? $row->Img($migration['image']) : base_url(IMG . 'admin/small_logo.jpg'); ?>">
            </div>
            <?php } ?>
            <div class="caption">
                <?php if(isset($migration['title']) && !empty($migration['title'])) { ?>
                <h3 style="height: 24px; overflow: hidden;text-overflow: ellipsis;margin-top: 0;"><?php echo $row->Arg($migration['title']); ?></h3>
                <?php } ?>
                <?php if(isset($migration['description']) && !empty($migration['description'])) { ?>
                <div style="height: 60px; overflow: hidden;text-overflow: ellipsis;"><?php echo $row->TextPreview($migration['description']); ?></div>
                <?php } ?>
                
                <hr/>
                <div class="input-group">
                    <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $row->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/sort/' . $row->Id() . '/'); ?>">
                    <div class="input-group-btn">
                        <a class="btn btn-default pulse-hover" href="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/show/' . $row->Id() . '/' . getQS()); ?>" role="button">עריכה</a>
                        
                        <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/destroy/' . $row->Id() . '/' . getQS()); ?>">
                            מחק
                        </button> 
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    <?php } ?>
    <?php endif;?>
    
    <div class="col-md-12 text-center">
        <?php showPages($total, $limit, $page); ?>
    </div>
</div>
<?php } ?>

<div class="fixed-action-btn" style="bottom: 45px; left: 24px;">
    <a href="<?php echo base_url('admin/ObjectAdmin/'.$migration['table'].'/show' . getQS()); ?>" class="btn btn-primary pulse" 
       style="width: 100px;height: 100px;padding: 38px 0;border-radius: 50%;">
        הוסף חדש
    </a>
</div>
