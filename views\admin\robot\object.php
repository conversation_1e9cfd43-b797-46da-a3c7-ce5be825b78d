<?php echo form_open('admin/seo/update/' . $seo->Id(), array("class" => "ajax")); ?>
<div class="modal-dialog" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="exampleModalLabel">SEO: דף - <?php echo $seo->Arg('title'); ?></h4>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="control-label">כותרת הדף:</label>
                <input type="text" class="form-control" name="title" value="<?php echo $seo->Arg('title'); ?>">
            </div>
            <?php if($langs = $this->config->item('available_lang'))foreach($langs as $lkey => $lang) { ?>
            <div class="form-group">
                <label class="control-label">כותרת הדף:<?php echo $lang; ?></label>
                <input type="text" class="form-control" name="<?php echo $lkey; ?>_title" value="<?php echo $seo->Arg($lkey . '_title'); ?>">
            </div>
            <?php } ?>
            <div class="form-group">
                <label class="control-label">תיאור הדף:</label>
                <textarea class="form-control" name="description" rows="6"><?php echo $seo->Arg('description'); ?></textarea>
            </div>
            <?php if($langs = $this->config->item('available_lang'))foreach($langs as $lkey => $lang) { ?>
            <div class="form-group">
                <label class="control-label">תיאור הדף:<?php echo $lang; ?></label>
                <textarea class="form-control" name="<?php echo $lkey; ?>_description" rows="6"><?php echo $seo->Arg($lkey . '_description'); ?></textarea>
            </div>
            <?php } ?>
            <div class="form-group">
                <label class="control-label">תמונה:</label>
                <input type="text" dir="ltr" class="form-control" name="image" value="<?php echo $seo->Arg('image'); ?>">
            </div>
            <div class="form-group">
                <label class="control-label">כתובת קנונית:</label>
                <input type="text" dir="ltr" class="form-control" name="canonical" value="<?php echo urldecode($seo->Arg('canonical')); ?>">
            </div>
            <div class="form-group">
                <label class="control-label">כתובת ידידותית:</label>
                <input type="text" class="form-control" name="friendly" value="<?php echo $seo->Arg('friendly'); ?>">
            </div>
            <?php if($langs = $this->config->item('available_lang'))foreach($langs as $lkey => $lang) { ?>
            <div class="form-group">
                <label class="control-label">כתובת ידידותית:<?php echo $lang; ?></label>
                <input type="text" class="form-control" name="<?php echo $lkey; ?>_friendly" value="<?php echo $seo->Arg($lkey . '_friendly'); ?>">
            </div>
            <?php } ?>
            <div class="form-group">
                <label class="control-label">רובוט:</label>
                <select class="form-control" name="robots">
                    <option value="INDEX, FOLLOW" <?php echo $seo->Arg('robots') === "INDEX, FOLLOW" ? "selected" : ""; ?>>INDEX, FOLLOW</option>
                    <option value="NOINDEX, FOLLOW" <?php echo $seo->Arg('robots') === "NOINDEX, FOLLOW" ? "selected" : ""; ?>>NOINDEX, FOLLOW</option>
                    <option value="INDEX, NOFOLLOW" <?php echo $seo->Arg('robots') === "INDEX, NOFOLLOW" ? "selected" : ""; ?>>INDEX, NOFOLLOW</option>
                    <option value="NOINDEX, NOFOLLOW" <?php echo $seo->Arg('robots') === "NOINDEX, NOFOLLOW" ? "selected" : ""; ?>>NOINDEX, NOFOLLOW</option>
                </select>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">בטל</button>
            <button type="submit" class="btn btn-primary">שמור</button>
        </div>
    </div>
</div>
<?php echo form_close(); ?>