
<div class="row">
    <div class="col-md-12">
        <h4> 
            <small>
                <?php showInfo($total, $limit, $page); ?>
            </small>
        </h4>
    </div>

    <?php if(isset($category_id)) { ?>
    <div class="col-md-4">
        <div class="thumbnail text-center" style="padding: 0;background-color: whitesmoke;">
            <a class="btn btn-default pulse-hover" style="width: 100%;min-height: 265px;line-height: 250px;font-size: 64px;"
               href="<?php echo base_url('admin/gallery/images/?category_id=' . $category_id); ?>" role="button">
                <span class="glyphicon glyphicon-plus"></span>
            </a>
        </div>
    </div>
    <?php } ?>
    
    <?php if(isset($gallery) && $gallery) { ?>
    <?php foreach($gallery as $row) { ?>
    <div class="col-md-4">
        <div class="thumbnail text-center" style="padding: 0;background-color: whitesmoke;">
            <div class="absolute-action-btn" style="top: 10px; left: 10px;">
                <button type="button" class="btn pulse-hover btn-default duplicate" data-url="<?php echo base_url('admin/object/gallery/duplicate/' . $row->Id() . '/' . getQS()); ?>">
                    שכפל
                </button> 
            </div>
            <div class="absolute-action-btn" style="top: 10px; right: 10px;">
                <button type="button" class="btn pulse-hover <?php echo $row->Arg('status') > 0 ? "btn-success" : "btn-warning"; ?> status" data-url="<?php echo base_url('admin/object/gallery/status/' . $row->Id() . '/' . getQS()); ?>">
                    <?php echo $row->Arg('status') > 0 ? "פעיל" : "לא פעיל"; ?>
                </button> 
            </div>

            <div class="wrapper-img" style="height: 120px; overflow: hidden;">
                <img class="" style="width: 100%;margin-top: 0px;" src="<?php echo $row->Img('image') ? $row->Img('image') : base_url(IMG . 'admin/small_logo.jpg'); ?>">
            </div>
            <div class="caption">
                <h3 style="height: 24px; overflow: hidden;text-overflow: ellipsis;margin-top: 0;"><?php echo $row->Arg('he_title'); ?></h3>
                <div style="height: 60px; overflow: hidden;text-overflow: ellipsis;"><?php echo $row->Arg('he_place'); ?></div>

                <div class="input-group">
                    <input type="number" dir="ltr" class="form-control sorting" name="sort" value="<?php echo $row->Number('sort'); ?>" placeholder="סדר" data-url="<?php echo base_url('admin/object/gallery/sort/' . $row->Id() . '/'); ?>">
                    <div class="input-group-btn">
                        <a class="btn btn-default pulse-hover" href="<?php echo base_url('admin/gallery/images/' . $row->Id() . '/' . getQS()); ?>" role="button">עריכה</a>

                        <button type="button" class="btn pulse-hover btn-danger delete" data-url="<?php echo base_url('admin/object/gallery/destroy/' . $row->Id() . '/' . getQS()); ?>">
                            מחק
                        </button> 
                    </div>
                </div>

            </div>
        </div>
    </div>
    <?php } ?>

    <div class="col-md-12 text-center">
        <?php showPages($total, $limit, $page); ?>
    </div>
    <?php } ?>
</div>
