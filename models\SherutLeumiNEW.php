
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class SherutLeumi extends CI_Model {
    public function apiClientGetMekSherutNEW() {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://vu-apiws-d.azurewebsites.net/api/v2/Data/mekomotsherut',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        return json_decode($response, true)['items'] ?? [];
    }

    public function populateMSherutNEW() {
        $mekSherutItems = $this->apiClientGetMekSherutNEW();
        if (empty($mekSherutItems)) return false;

        $this->db->empty_table('mekSherutTemp');
        foreach ($mekSherutItems as $item) {
            $this->db->insert('mekSherutTemp', [
                'id' => $item['ID'],
                'MOSADNA' => $item['MOSADNA'],
                'TEKENB' => (int)$item['AvailBValue'],
                'TEKENH' => (int)$item['AvailHValue'],
                'TEKENP' => (int)$item['AvailPValue'],
                'MAS_NOTES' => $item['MAS_NOTES'],
                'YEAR' => $item['YEAR'],
                'City_Key' => $item['City']['Key'],
                'City_Value' => $item['City']['Value']
            ]);
        }
        return true;
    }
}
?>
