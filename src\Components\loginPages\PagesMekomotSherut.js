import React from 'react';
import { Routes, Route } from 'react-router-dom'; // removed BrowserRouter

import MekomotSherutIndex from '../../Pages/MekomotSherut/MekomotSherutIndex';

const PagesMekomotSherut = (props) => {
    // Add debug logging
    console.log("🏢 PagesMekomotSherut rendered - PUBLIC PAGE (no auth required)", props);

    try {
        return (
            <React.Fragment>
                <Routes>
                    {/* Use relative paths for nested routes */}
                    <Route path="/" element={<MekomotSherutIndex {...props} page='sherutPlacesIndex' />} />
                    <Route path="/*" element={<MekomotSherutIndex {...props} page='sherutPlacesIndex' />} />
                </Routes>
            </React.Fragment>
        );
    } catch (error) {
        console.error("❌ PagesMekomotSherut render error:", error);
        return (
            <div style={{
                padding: '20px',
                textAlign: 'center',
                direction: 'rtl'
            }}>
                <h3 style={{ color: '#e74c3c' }}>שגיאה בטעינת מקומות השירות</h3>
                <p>אירעה שגיאה בטעינת הדף</p>
                <button onClick={() => window.location.reload()}>רענן דף</button>
            </div>
        );
    }
};

export default PagesMekomotSherut;
