pre.active4d code {
  background:none;
  color:#000;
  font-size:1.1em
}

pre.active4d .DiffHeader {
   background-color: #656565;
   color: #FFFFFF;
}
pre.active4d .Operator {
}
pre.active4d .InheritedClass {
}
pre.active4d .TypeName {
   color: #21439C;
}
pre.active4d .Number {
   color: #A8017E;
}
pre.active4d .EmbeddedSource {
   background-color: #ECF1FF;
}
pre.active4d {
   background-color: #FFFFFF;
   color: #000000;
}
pre.active4d .DiffInsertedLine {
   background-color: #98FF9A;
   color: #000000;
}
pre.active4d .LibraryVariable {
   color: #A535AE;
}
pre.active4d .Storage {
   color: #FF5600;
}
pre.active4d .InterpolatedEntity {
   color: #66CCFF;
}
pre.active4d .line-numbers {
   background-color: #BAD6FD;
   color: #000000;
}
pre.active4d .LocalVariable {
   color: #6392FF;
}
pre.active4d .DiffLineRange {
   background-color: #1B63FF;
   color: #FFFFFF;
}
pre.active4d .BlockComment {
   color: #D33435;
}
pre.active4d .TagName {
   color: #016CFF;
}
pre.active4d .FunctionArgument {
}
pre.active4d .BuiltInConstant {
   color: #A535AE;
}
pre.active4d .LineComment {
   color: #D33535;
}
pre.active4d .DiffDeletedLine {
   background-color: #FF7880;
   color: #000000;
}
pre.active4d .NamedConstant {
   color: #B7734C;
}
pre.active4d .CommandMethod {
   color: #45AE34;
}
pre.active4d .TableField {
   color: #0BB600;
}
pre.active4d .PlainXmlText {
   color: #000000;
}
pre.active4d .Invalid {
   background-color: #990000;
   color: #FFFFFF;
}
pre.active4d .LibraryClassType {
   color: #A535AE;
}
pre.active4d .TagAttribute {
   color: #963DFF;
}
pre.active4d .Keyword {
   color: #006699;
}
pre.active4d .UserDefinedConstant {
}
pre.active4d .String {
   color: #666666;
}
pre.active4d .DiffUnchangedLine {
   color: #5E5E5E;
}
pre.active4d .TagContainer {
   color: #7A7A7A;
}
pre.active4d .FunctionName {
   color: #21439C;
}
pre.active4d .Variable {
   color: #0053FF;
}
pre.active4d .DateTimeLiteral {
   font-weight: bold;
   color: #66CCFF;
}

/* bshaffer custom styles */
.code-container {
  position: relative;
}

.code-container .langspec {
  display: inline;
  position: absolute;
  top: 0;
  right: 5px;
  text-transform: lowercase;
  font-size: 9px;
  color: #999;
}

.anchor {
  width: 20px;
  display: block;
  float: left;
  height: 20px;
  margin-left: -20px;
  cursor: pointer;
}