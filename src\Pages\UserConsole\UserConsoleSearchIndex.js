import React, { useState, useEffect, Fragment } from "react";
import { getSafeUserData } from "../../context/AuthContext";
import { RestUrls } from "../../Components/-Helpers-/config";
import axios from "axios";
import SearchUp from "./search/SearchUp";
import SearchResults from "./search/SearchResults";
import SearchCategory from "./search/SearchCategory";
import MySayarot from "./search/MySayarot";
import InfiniteScroll from "react-infinite-scroller";
import { animateScroll as scroll } from "react-scroll";
import goUp from "../../img/sherut-leumi/svg/goUp.svg";
import loader from "../../img/preLoader.gif";
import vPic from "../../img/sherut-leumi/svg/sherutPlaces/v.svg";
import { Form } from "react-bootstrap";
import { getAllUrlParams } from "./../../Components/-Helpers-/UrlParameters";
import TextDayShowSayarot from "./texts/TextDayShowSayarot";
import dayjs from "dayjs";

const UserConsoleSearchIndex = ({ changeMenuright, siteInfo }) => {
  const environment = process.env.REACT_APP_ENVIRONMENT;
  const API_BASE_URL = environment === 'dev' ? process.env.REACT_APP_API_BASE_URL_DEV : process.env.REACT_APP_API_BASE_URL;
  // Initialize state from URL parameters and localStorage
  const urlQuery = getAllUrlParams(window.location.href);
  console.log('URL Parameters:', {
    urlQuery,
    fullUrl: window.location.href
  });

  const initialRakazId = urlQuery.rakazid || localStorage.getItem("rakazid") || "";
  const initialSayeretId = urlQuery.sayeretid || localStorage.getItem("sayeretid") || "";
  const initialCity = urlQuery.cityid || "";
  const initialFreeSearch = urlQuery.freetext || "";

  // Clear localStorage values after reading
  localStorage.setItem("rakazid", "");
  localStorage.setItem("sayeretid", "");

  // State declarations using hooks
  const [loading, setLoading] = useState(false);
  const [freeSearch, setFreeSearch] = useState(initialFreeSearch);
  const [rakaz, setRakaz] = useState(initialRakazId);
  const [sayeretId, setSayeretId] = useState(initialSayeretId);
  const [city, setCity] = useState(initialCity);
  const [orderBy, setOrderBy] = useState("location");
  const [searchFilters, setSearchFilters] = useState(false);
  const [allItems, setAllItems] = useState([]);
  const [items, setItems] = useState([]);
  const [hasMoreItems, setHasMoreItems] = useState(true);
  const [nextHref, setNextHref] = useState(0);
  const [pageName, setPageName] = useState("sayarot");
  const [showGreenOk, setShowGreenOk] = useState(false);
  const [noSayarotMessage, setNoSayarotMessage] = useState("");
  const itemsPerPage = 20;
  const [StartDateRegistrationSayerot, setStartDateRegistrationSayerot] = useState(null);

  // Load initial data on component mount
  useEffect(() => {
    console.log('Component mounted, current state:', {
      rakaz,
      sayeretId,
      city,
      freeSearch
    });

    // If we have any initial search parameters, trigger the search
    if (initialRakazId || initialCity || initialFreeSearch) {
      updateStateFromSearch({
        rakaz: initialRakazId,
        city: initialCity,
        freeSearch: initialFreeSearch,
        sayeretId: initialSayeretId
      });
    } else if (initialRakazId) {
      setNoSayarotMessage("אין לרכזת סיירות");
    }
  }, []);

  // Update search state
  const updateStateFromSearch = async (searchState) => {
    console.log('==================== FILTER UPDATE ====================');
    console.log('1. Received New Search State:', searchState);
    
    // Immediately update UI state to show we're processing the search
    setLoading(true);
    setItems([]); // Clear current results immediately
    setNoSayarotMessage("טוען תוצאות..."); // Show loading message in Hebrew
    
    try {
      const userJ = getSafeUserData();
      const sessionKey = userJ?.SessionKey;

      const API = API_BASE_URL + "/api/v2/Data/sayarot";
      const sendObj = {
        token: RestUrls.Code,
        page: 1,
        pageSize: itemsPerPage,
        filterParams: {
          SayertId: searchState.sayeretId || "",
          RakazId: searchState.rakaz || "",
          CityId: searchState.city || "",
          FreeText: searchState.freeSearch || "",
          OrderBy: searchState.orderBy || "location",
          MinDate: dayjs().add(1, 'day').format('YYYYMMDD'),
        },
        SessionKey: sessionKey,
      };

      // Update filter state before API call
      setFreeSearch(searchState.freeSearch || "");
      setRakaz(searchState.rakaz || "");
      setCity(searchState.city || "");
      setOrderBy(searchState.orderBy || "location");

      console.log('2. Sending Search Request:', {
        filters: sendObj.filterParams
      });

      const response = await axios.post(API, JSON.stringify(sendObj), {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      const data = response?.data;
      if (!data || !Array.isArray(data.Sayarot)) {
        setItems([]);
        setNoSayarotMessage(searchState.rakaz ? "אין לרכזת סיירות" : "אין תוצאות לחיפוש זה");
        setHasMoreItems(false);
        return;
      }
   
      const mappedItems = data.Sayarot.map(item => ({
        ...item,
        ID: item.ID?.toString(),
        name: item.Name || "לא צוין",
        city: item.City?.Value || "לא צוין",
        rakaz: item.Rak?.Value || "לא צוין",
        Image: item.Image || `${RestUrls.pagesPictures}/search/defaultImage.jpg`
      }));

      // Filter out sayarot scheduled for today
      const today = dayjs().format('YYYYMMDD');
      const filteredItems = mappedItems.filter(item => {
        return !item.SyrDay || item.SyrDay > today;
      });

      setStartDateRegistrationSayerot(data.StartDateRegistrationSayerot);

      // Update results state with filtered items
      setItems(filteredItems);
      setNextHref(data.NextPage);
      setHasMoreItems(data.NextPage !== null);
      
      // Set appropriate message based on filtered results
      if (filteredItems.length === 0) {
        setNoSayarotMessage(searchState.rakaz ? "אין לרכזת סיירות" : "אין תוצאות לחיפוש זה");
      } else {
        setNoSayarotMessage(
          `נמצאו ${filteredItems.length} מתוך ${data.TotalItems || filteredItems.length} תוצאות${data.NextPage !== null ? ' (גלול למטה לתוצאות נוספות)' : ''}`
        );
      }

      console.log('3. Search Complete:', {
        resultsCount: filteredItems.length,
        totalItems: data.TotalItems,
        hasMore: data.NextPage !== null,
        nextPage: data.NextPage
      });
      
    } catch (err) {
      console.error('Error in search:', err);
      setItems([]);
      setNoSayarotMessage("שגיאה בטעינת נתונים - אנא נסי שוב");
      setHasMoreItems(false);
    } finally {
      setLoading(false);
      console.log('==================== END FILTER UPDATE ====================\n');
    }
  };

  // Handle select change
  const handleChangeSelect = (event) => {
    console.log('=== handleChangeSelect START ===');
    console.log('New order by value:', event.target.value);
    
    const newOrderBy = event.target.value;
    
    // Instead of setting state and calling loadItems separately,
    // we'll use updateStateFromSearch directly with the new sort
    updateStateFromSearch({
      freeSearch,
      rakaz,
      city,
      sayeretId,
      orderBy: newOrderBy
    });
    
    console.log('=== handleChangeSelect END ===');
  };

  // Load more items for infinite scroll
  const loadItems = async () => {
    if (loading) return;
    
    const debugState = {
      currentFilters: {
        freeSearch,
        rakaz,
        city,
        sayeretId,
        orderBy
      },
      paginationState: {
        nextHref,
        hasMoreItems,
        itemsPerPage
      },
      currentItems: items.length,
      loading
    };
    
    console.log('==================== LOADING MORE ITEMS ====================');
    console.log('1. Current Application State:', JSON.stringify(debugState, null, 2));
    
    // If there are no more pages to load, stop infinite scroll
    if (nextHref === null) {
      setHasMoreItems(false);
      return;
    }
    
    setLoading(true);
    
    try {
      const userJ = getSafeUserData();
      const sessionKey = userJ?.SessionKey;

      const API = API_BASE_URL + "/api/v2/Data/sayarot";
      const sendObj = {
        token: RestUrls.Code,
        page: nextHref === 0 ? 1 : nextHref + 1,
        pageSize: itemsPerPage,
        filterParams: {
          SayertId: sayeretId || "",
          RakazId: rakaz || "",
          CityId: city || "",
          FreeText: freeSearch || "",
          OrderBy: orderBy || "location",
          MinDate: dayjs().add(1, 'day').format('YYYYMMDD'),
        },
        SessionKey: sessionKey,
      };

      console.log('2. Sending Search Request:', {
        filters: sendObj.filterParams,
        pagination: { page: sendObj.page, pageSize: sendObj.pageSize }
      });

      const response = await axios.post(API, JSON.stringify(sendObj), {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      const data = response?.data;
      if (!data || !Array.isArray(data.Sayarot)) {
        setHasMoreItems(false);
        if (nextHref === 0) {
          setNoSayarotMessage(rakaz ? "אין לרכזת סיירות" : "אין תוצאות לחיפוש זה");
        }
        return;
      }

      // Transform the results to match the expected format
      const mappedItems = data.Sayarot.map(item => ({
        ...item,
        ID: item.ID?.toString(),
        name: item.Name || "לא צוין",
        city: item.City?.Value || "לא צוין",
        rakaz: item.Rak?.Value || "לא צוין",
        Image: item.Image || `${RestUrls.pagesPictures}/search/defaultImage.jpg`
      }));

      // Filter out sayarot scheduled for today
      const today = dayjs().format('YYYYMMDD');
      const filteredItems = mappedItems.filter(item => {
        return !item.SyrDay || item.SyrDay > today;
      });

      // Set the registration start date from the API response
      if (data.StartDateRegistrationSayerot) {
        console.log('Setting StartDateRegistrationSayerot:', data.StartDateRegistrationSayerot);
        setStartDateRegistrationSayerot(data.StartDateRegistrationSayerot);
      }

      // Update items based on pagination with filtered items
      if (nextHref === 0) {
        setItems(filteredItems);
      } else {
        setItems(prev => [...prev, ...filteredItems]);
      }

      // Update pagination state
      // If NextPage is null, there are no more pages to load
      setNextHref(data.NextPage);
      setHasMoreItems(data.NextPage !== null);

      // Update message with filtered results count
      let resultsCount = items.length + filteredItems.length;
      let currentlyShowing = resultsCount;
      setNoSayarotMessage(
        `מציג ${currentlyShowing} מתוך ${data.TotalItems || resultsCount} תוצאות${data.NextPage !== null ? ' (גלול למטה לתוצאות נוספות)' : ''}`
      );
      
      console.log('3. Loaded more items:', {
        newItemsCount: filteredItems.length,
        totalNow: resultsCount,
        hasMore: data.NextPage !== null
      });
      
    } catch (error) {
      console.error('Error loading items:', error);
      setNoSayarotMessage("אירעה שגיאה בטעינת התוצאות");
      setHasMoreItems(false);
    } finally {
      setLoading(false);
    }
  };

  // Load initial data from API
  const loadInitialData = async () => {
    console.log('Loading initial data with rakaz:', rakaz);
    if (!hasMoreItems || loading) {
      console.log('Skipping initial load:', { hasMoreItems, loading });
      return;
    }
    
    setLoading(true);
    try {
      const userJ = getSafeUserData();
      const sessionKey = userJ?.SessionKey;

      const API = API_BASE_URL + "/api/v2/Data/sayarot";
      const sendObj = {
        token: RestUrls.Code,
        page: nextHref,
        pageSize: itemsPerPage,
        filterParams: {
          SayertId: sayeretId,
          RakazId: rakaz,
          cityId: city,
          freeSearch,
          orderBy: orderBy || "location",
          MinDate: dayjs().add(1, 'day').format('YYYYMMDD'),
        },
        SessionKey: sessionKey,
      };

      console.log('Loading initial data with payload:', sendObj);

      const response = await axios.post(API, JSON.stringify(sendObj), {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      const data = response?.data;
      if (!data || !Array.isArray(data.Sayarot)) {
        console.log('Invalid initial data format received:', data);
        setHasMoreItems(false);
        if (rakaz) {
          setNoSayarotMessage("אין לרכזת סיירות");
        }
        return;
      }

      // Process and set the initial data
      const mappedItems = data.Sayarot.map(item => ({
        ...item,
        ID: item.ID?.toString(),
        name: item.Name || "לא צוין",
        city: item.City?.Value || "לא צוין",
        rakaz: item.Rak?.Value || "לא צוין",
        Image: item.Image || `${RestUrls.pagesPictures}/search/defaultImage.jpg`
      }));

      // Filter out sayarot scheduled for today
      const today = dayjs().format('YYYYMMDD');
      const filteredItems = mappedItems.filter(item => {
        return !item.SyrDay || item.SyrDay > today;
      });

      setItems(filteredItems);
      setNextHref(data.NextPage);
      setHasMoreItems(data.NextPage !== null);

      console.log('Initial data loaded:', {
        itemsCount: filteredItems.length,
        nextPage: data.NextPage,
        totalPages: data.TotalPages,
        hasMore: data.NextPage !== null
      });
    } catch (err) {
      console.error('Error loading initial data:', err);
      alert("שגיאה בטעינת נתונים");
      setHasMoreItems(false);
    } finally {
      setLoading(false);
    }
  };

  // Handle menu change and show sign
  const changeMenuRightAndShowSign = (id) => {
    changeMenuright(id);

    if (!id.isActive) {
      setShowGreenOk(id.name);
      setTimeout(() => setShowGreenOk(false), 4000);
    }
  };

  const isMobile = siteInfo?.isMobile || false;
  const topPic = RestUrls.pagesPictures + "/search/searchTop.jpg?v=2";
  const m_topPic = RestUrls.pagesPictures + "/register/m_registerTop.jpg?v=5";

  const loadingElement = (
    <div className="loading-container">
      <img
        src={loader}
        key={1231}
        alt="טוען נתונים..."
        className="loaderInfiniteScroll animate__animated animate__fadeIn"
      />
      <div className="loading-text">טוען נתונים...</div>
    </div>
  );

  return (
    <>
      <div className="SearchSection">
        {showGreenOk && (
          <div className="messaggePopUp animate__animated animate__fadeInDown">
            <div className="messaggeCont">
              <img src={vPic} alt="תודה שנרשמת לסיירת" />
              <span>תודה שנרשמת לסיירת {showGreenOk}</span>
            </div>
          </div>
        )}

        <img
          className="goUpPic animate__animated animate__bounceIn"
          src={goUp}
          alt="לעלות"
          onClick={() => scroll.scrollToTop({ duration: 0 })}
        />

        {isMobile ? (
          <a href="https://sherut-leumi.co.il/" target="_self" rel="noopener noreferrer">
            <img className="topPic full_width" src={m_topPic} alt="top" />
          </a>
        ) : (
          <img className="topPic full_width" src={topPic} alt="top" />
        )}

        <div className="SearchContainer">
          <h1>הסיירות של האגודה להתנדבות – בחרי את הסיירת שלך</h1>
          <div className="SearchCategoty">
            <SearchCategory
              currentPage={pageName}
              changePage={(name) => {
                console.log('Changing page to:', name);
                setPageName(name);
                setFreeSearch("");
                setRakaz("");
                setCity("");
                setItems([]);
                setHasMoreItems(true);
                setNextHref(0);
                setNoSayarotMessage("");
              }}
            />
          </div>

          {pageName === "mySayarot" ? (
            <div className="pageMySayarot">
              <MySayarot changeMenuright={changeMenuright} />
            </div>
          ) : (
            <div className="pageSayarot">
              <div className="SearchUp">
                <SearchUp
                  currentState={{ freeSearch, rakaz, city, orderBy }}
                  updateSearch={(searchState) => {
                    console.log('Updating search with:', searchState);
                    updateStateFromSearch(searchState);
                  }}
                />
              </div>

              <div className="bannerCall">
                <Fragment>
                  <TextDayShowSayarot isMobile={isMobile} step="step2" />
                </Fragment>
              </div>

              <div className="orderBy">
                <Form.Control
                  as="select"
                  className="customSelectInput"
                  name="orderBy"
                  onChange={handleChangeSelect}
                  value={orderBy}
                >
                  <option value="location">מיון לפי ישוב (א-ב)</option>
                  <option value="coordinator">מיון לפי רכזת (א-ב)</option>
                  <option value="date">מיון לפי תאריך</option>
                  <option value="spots">מיון לפי מקומות שנותרו</option>
                </Form.Control>
              </div>

              <InfiniteScroll
                pageStart={0}
                loadMore={loadItems}
                hasMore={hasMoreItems}
                loader={loadingElement}
                threshold={100}
              >
                <div className="SearchResults">
                  {loading && items.length === 0 ? loadingElement : 
                   items.length > 0 ? (
                    <SearchResults
                      activeSayeretId={sayeretId}
                      changeMenuright={changeMenuRightAndShowSign}
                      sayarotData={items}
                      StartDateRegistrationSayerot={StartDateRegistrationSayerot}
                    />
                  ) : (
                    <h2 className="noResults">{noSayarotMessage || "אין תוצאות..."}</h2>
                  )}
                </div>
              </InfiniteScroll>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default UserConsoleSearchIndex;
