import React, { useState, useEffect } from "react";
import { getSafeUserData } from "../../context/AuthContext";
import CustomFloatInput from "../../Components/-Helpers-/forms/CustomFloatInput";
import { RestUrls } from "../../Components/-Helpers-/config";
import { animateScroll as scroll } from "react-scroll";
import { NavLink } from "react-router-dom";
import loader from "../../img/preLoader.gif";
import getDataFromApi from "../../Components/-Helpers-/api/getDataFromApi";
import ModalDefaul from "../../Components/-Helpers-/ModalDefaul";
import { getAllUrlParams } from "./../../Components/-Helpers-/UrlParameters";
import { toast } from "react-toastify";
import Spinner from 'react-bootstrap/Spinner';
import { Button } from "@mui/material";

const Login = ({ siteInfo }) => {
  const environment = process.env.REACT_APP_ENVIRONMENT;
  const API_BASE_URL = environment === 'dev' ? process.env.REACT_APP_API_BASE_URL_DEV : process.env.REACT_APP_API_BASE_URL;
  const [state, setState] = useState({
    IDNO: "",
    Password: "",
    loading: false,
    responseLogin: false,
    checkInputs: false,
    checkInputsPage: false,
    checkRadio1: false,
    checkRadio2: false,
    btnSendClass: "success"
  });

  const updateValue = (newValue) => {
    setState(prev => ({ ...prev, ...newValue }));
  };

  const sendForm = (sendRules) => {
    setState(prev => ({
      ...prev,
      checkInputs: true,
      checkInputsPage: true,
      checkRadio1: true,
      checkRadio2: true
    }));

    if (sendRules) {
      setState(prev => ({ ...prev, loading: true }));

      const sendObj = { ...state };
      delete sendObj.responseLogin;
      // use env variable
      console.log(API_BASE_URL, "API_BASE_URLS");
      fetch(`${API_BASE_URL}/api/v2/Volunteer/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sendObj),
      })
        .then((response) => response.json())
        .then((getData) => {
          setState(prev => ({ ...prev, responseLogin: getData, loading: false }));
          
          console.log("API Response:", getData);
          console.log("IDNO from response:", getData.IDNO);

          if (getData.Result === "Success") {
            // Store user data exactly as needed in the specified format
            const userData = {
              IDNO: getData.IDNO || state.IDNO || state.idno || state.userId || "",
              Category: getData.Category,
              FirstName: getData.FirstName,
              LastName: getData.LastName,
              Sex: getData.Sex,
              InService: getData.InService,
              AttendanceReportOnline: getData.AttendanceReportOnline || false,
              SessionKey: getData.SessionKey,
              ImageUrl: getData.ImageUrl || null,
              Sayarot: getData.Sayarot || []
            };
            
            // Store the userData object
            localStorage.setItem("userData", JSON.stringify(userData));
            console.log(userData, "userData stored");
            
            // Log what's actually in localStorage after storing
            console.log("Stored in localStorage:", getSafeUserData());
            
            // Store session key separately for components that expect it
            localStorage.setItem("sessionKey", getData.SessionKey);
            
            if (getData.sayeretId || getData.Sayarot?.length > 0) {
              localStorage.setItem("sayeretid", getData.sayeretId || (getData.Sayarot?.length > 0 ? getData.Sayarot[0] : ""));
            }

            const { InService, Category, FirstName, LastName, IDNO, Sex } = getData;

            if (window.ReactNativeWebView !== undefined) {
              try {
                fetch(
                  "https://webhooks.wdev.co.il/api/aguda-logs",
                  {
                    method: "POST",
                    headers: {
                      Accept: "application/json",
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                      InService,
                      Category,
                      FirstName,
                      LastName,
                      IDNO,
                      Sex,
                    }),
                  }
                )
                  .then(newLog => console.log(newLog, "new log"))
                  .catch(error => console.error("Error logging to webhook:", error));
              } catch (error) {
                console.error("Error logging to webhook:", error);
              }
            }

            if (InService === false && Category !== "2") {
              window.open("/userConsole/data", "_self");
            } else if (InService === false && Category === "2") {
              window.open("/userConsole/data", "_self");
            } else if (InService === true) {
              window.open("/userConsole/clockInOut", "_self");
            } else {
              window.open("/userConsole/data", "_self");
            }
          } else if (getData.IsBusinessError !== undefined) {
            if (getData.IsBusinessError === true) {
              // Business error - show the specific error message
              toast.error(getData.ErrorMessage || "שגיאת משתמש");
            } else if (getData.ErrorMessage) {
              // System error - show generic message
              toast.error("שגיאת מערכת");
            }
          }
        })
        .catch((error) => {
          console.error("Error in login:", error);
          setState(prev => ({ ...prev, loading: false }));
          
          // Check for business errors in the error response
          if (error.response?.data?.IsBusinessError === true && error.response?.data?.ErrorMessage) {
            toast.error(error.response.data.ErrorMessage);
          } else {
            toast.error("שגיאת מערכת");
          }
        });
    } else {
      setState(prev => ({ ...prev, btnSendClass: "danger" }));
    }
  };

  useEffect(() => {
    const urlQuery = getAllUrlParams(window.location.href);
    const rakazId = urlQuery.rakazid || false;
    const sayeretId = urlQuery.sayeretid || false;

    if (rakazId) {
      localStorage.setItem("rakazid", rakazId);
    } else if (sayeretId) {
      localStorage.setItem("sayeretid", sayeretId);
    }
  }, []);

  const sendRules = state.IDNO && state.Password;
  const isMobile = siteInfo?.isMobile || false;
  const m_picTop = `${RestUrls.pagesPictures}register/m_registerTop.jpg?v=4`;
  const bgDesktop = `${RestUrls.pagesPictures}`;

  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-gray-50 px-4 py-6 md:py-12 lg:py-16 rtl">
      <div className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl bg-white shadow-lg rounded-xl overflow-hidden transition-all animate__animated animate__fadeIn">
        {/* Loader overlay */}
        {state.loading && !state.responseLogin && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <Spinner animation="border" role="status">
        <span className="visually-hidden">Loading...</span>
      </Spinner>
          </div>
        )}

        <div className="flex flex-col lg:flex-row">
          {/* Top image */}
          <div className="w-full lg:w-2/5 h-40 md:h-48 lg:h-auto bg-blue-600 relative overflow-hidden">
            <img 
              className="w-full h-full object-cover" 
              src={`${bgDesktop}register/bg.jpg?v=2`} 
              alt="רקע" 
            />
            {isMobile && (
              <img 
                className="absolute inset-0 w-full h-full object-cover z-10" 
                src={m_picTop} 
                alt="top" 
              />
            )}
          </div>

          <div className="w-full lg:w-3/5 px-6 sm:px-8 md:px-10 lg:px-12 py-8 md:py-10 lg:py-12">
            {/* Header section */}
            <header className="text-center mb-8 lg:mb-10">
              <h1 className="boldTypeFamily text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-2">התחברות</h1>
              <p className="text-gray-600 md:text-lg">ברוכים השבים לאגודה להתנדבות</p>
            </header>

            {/* Form Fields */}
            <form onSubmit={(e) => {
              e.preventDefault();
              sendForm(sendRules);
            }}>
              <div className="inputs space-y-5 md:space-y-6">
                <div className="line">
                  <CustomFloatInput
                    name="IDNO"
                    updateValue={updateValue}
                    value={state.IDNO}
                    placeholder="תעודת זהות"
                    cssClass="w-full text-base md:text-lg"
                    validationRules={{ required: true }}
                    typeInput="number"
                    checkInputs={state.checkInputs}
                    checked={() => setState(prev => ({ ...prev, checkInputs: false }))}
                  />
                </div>

                <div className="line">
                  <CustomFloatInput
                    name="Password"
                    updateValue={updateValue}
                    value={state.Password}
                    placeholder="סיסמה"
                    cssClass="w-full text-base md:text-lg"
                    validationRules={{ required: true }}
                    typeInput="password"
                    checkInputs={state.checkInputs}
                    checked={() => setState(prev => ({ ...prev, checkInputs: false }))}
                  />
                  <div className="singIn reSendPass flex justify-end mt-2 md:mt-3">
                    <NavLink
                      className="jumpPage text-blue-600 text-sm md:text-base hover:text-blue-800 transition-colors"
                      role="menuitem"
                      onClick={() => scroll.scrollTo(0)}
                      to="/reSendPassNew"
                    >
                      <span>
                        <strong>שכחתי סיסמה</strong>  
                      </span>
                    </NavLink>
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="text-center mt-8 md:mt-10">
                <Button 
                  variant="contained"
                  disabled={state.loading}
                  type="submit"
                >
                  התחברות
                </Button>
                
                {window.ReactNativeWebView === undefined && (
                  <div className="singIn mt-6 md:mt-8">
                    <NavLink
                      className="jumpPage text-gray-700 hover:text-blue-600 transition-colors text-base md:text-lg"
                      role="menuitem"
                      onClick={() => scroll.scrollTo(0)}
                      to="/register"
                    >
                      <span>
                        עדין לא רשומים? <strong className="text-blue-600">לחצו להרשמה (נרשמים חדשים)</strong>
                      </span>
                    </NavLink>
                  </div>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>

      {state.responseLogin?.error && (
        <ModalDefaul
          variant="error"
          params={{ title: "שגיאה", text: state.responseLogin.error }}
          callBack={() => setState(prev => ({ ...prev, responseLogin: false }))}
        />
      )}
    </div>
  );
};

export default Login;
