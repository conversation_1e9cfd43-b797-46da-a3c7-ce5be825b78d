import React, { useState, useEffect } from "react";
import axios from "axios";
import { RestUrls } from "../../../Components/-Helpers-/config";

const AvatarMenuPic = ({ user }) => {
  const [avatarPicture, setAvatarPicture] = useState(null);

  useEffect(() => {
    getAvatarImage({ idno: user.IDNO, token: RestUrls.Code }, setAvatarPicture);
  }, [user]);

  if (!avatarPicture?.base64Pic) {
    return <DefaultPic />;
  }

  return (
    <div className="inline-block w-16 h-16 overflow-hidden rounded-full mb-2">
      <img src={avatarPicture.base64Pic} alt="תמונת אווטאר" className="w-full h-full object-cover" />
    </div>
  );
};

const DefaultPic = () => {
  const bgDesktop = RestUrls.img;

  return (
    <figure className="inline-block mb-2">
      <div
        className="w-16 h-16 rounded-full bg-cover bg-center"
        style={{
          backgroundImage: `url('${bgDesktop}default/noUser.png')`,
        }}
      />
    </figure>
  );
};

const getAvatarImage = async (sendObj, setAvatarPicture) => {
  try {
    const response = await axios.post(
      `${RestUrls.baseApiUrl}users/getAvatarImage`,
      sendObj,
      {
        headers: { "Content-Type": "application/json" },
      }
    );
    setAvatarPicture(response.data);
  } catch (error) {
    console.error("Error fetching avatar image:", error);
  }
};

export default AvatarMenuPic;
