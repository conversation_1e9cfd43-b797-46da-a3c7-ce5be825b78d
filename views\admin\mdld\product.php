<div class="row">
    <div class="col-md-3">
        <?php $this->load->view('admin/store/categories'); ?>
    </div>
    <div class="col-md-9">
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="<?php echo ! $this->input->get('tab') ? "active" : ""; ?>">
                <a href="<?php echo current_url(); ?>" aria-controls="product" role="tab">מוצר</a>
            </li>
            <li role="presentation" class="<?php echo $this->input->get('tab') === 'field' ? "active" : ""; ?>">
                <a href="<?php echo current_url() . '?tab=field'; ?>" aria-controls="field" role="tab">שדות</a>
            </li>
            <li role="presentation" class="<?php echo $this->input->get('tab') === 'extra' ? "active" : ""; ?>">
                <a href="<?php echo current_url() . '?tab=extra'; ?>" aria-controls="extra" role="tab">תוספות</a>
            </li>
            <li role="presentation" class="<?php echo $this->input->get('tab') === 'gallery' ? "active" : ""; ?>">
                <a href="<?php echo current_url() . '?tab=gallery'; ?>" aria-controls="gallery" role="tab">גלריה</a>
            </li>
        </ul>
        
        
        <!-- Tab panes -->
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane <?php echo ! $this->input->get('tab') ? "active" : ""; ?>" id="product" style="padding-top: 20px;">
                <?php if(isset($migration)){ ?>
                <?php $this->load->view('admin/store/object'); ?>
                <?php } ?>
            </div>
            <div role="tabpanel" class="tab-pane <?php echo $this->input->get('tab') === 'field' ? "active" : ""; ?>" id="field" style="padding-top: 20px;">
                <?php $this->load->view('admin/store/fields'); ?>
            </div>
            <div role="tabpanel" class="tab-pane <?php echo $this->input->get('tab') === 'extra' ? "active" : ""; ?>" id="extra" style="padding-top: 20px;">
                <?php $this->load->view('admin/store/extras'); ?>
            </div>
            <div role="tabpanel" class="tab-pane <?php echo $this->input->get('tab') === 'gallery' ? "active" : ""; ?>" id="gallery" style="padding-top: 20px;">
                <?php $this->load->view('admin/store/gallery'); ?>
            </div>
        </div>
    </div>
</div>
