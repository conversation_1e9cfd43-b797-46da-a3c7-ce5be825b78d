<?php defined('BASEPATH') OR exit('No direct script access allowed');

class App extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!sherut-leumi!wd+=111@$%+';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('sherutLeumi');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('token'));
           if( $postCode['token'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    public function getCities4Select($jPData = FALSE) {
        
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('all'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas') ) { return $this->output ->set_status_header(403); }
        
        $this->db->select('keyName as id, valueName as name');
        $this->db->from('citiesTemp');
        $this->db->order_by('valueName ASC');
        //$this->db->where('status', 1);
        $result= $this->db->get();
        $cities = $result->result_array();
//        
//        array('id' => $value['Key'],'name' => $value['Value'] )
                
        //$output['options'] = $this->sherutLeumi->ApiClientGet($url = 'v2/data/cities');
        $output['options'] = $cities;
        
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
public function getSchools4Select($jPData = FALSE) {
    $output['funcName'] = $this->router->fetch_method();
    
    $this->db->select('keyName as id, valueName as name, placeName, schoolCode');
    $this->db->from('schoolsTemp');
    $this->db->order_by('valueName ASC');
    $result = $this->db->get();
    $schools = $result->result_array();
    
    $return = array();
    
    foreach ($schools as $value) {
        if(!empty($value['placeName'])) {
            $name = $value['placeName'].' - '.$value['name'];
        } else {
            $name = $value['name'];
        }
        
        $valueReturn = array(
            'id' => $value['id'],
            'name' => $name
        );
        
        $return[] = $valueReturn;
    }
    
    $output['options'] = $return;
    
    $this->data = $output;
    
    return $this->output
            ->set_status_header(!isset($header) ? 200 : $header)
            ->set_content_type('application/json')
            ->set_output(json_encode($this->data));
}

      public function getCities4SelectDev($jPData = FALSE) {
        
        
        $this->_loaderWS();$output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        $pageAutoriced = array('all'); 
        $jsonPosts = $this->msiteWs->getPostFromJson(array('auth','token','credential'));
        $checkPageAuth = $this->msiteWs->checkPageAuth($pageAutoriced,$jsonPosts['auth']);
        if(!$checkPageAuth && !$this->input->get('sebas') ) { return $this->output ->set_status_header(403); }
        
        $this->db->select('keyName as id, valueName as name');
        $this->db->from('citiesTempDev');
        $this->db->order_by('valueName ASC');
        //$this->db->where('status', 1);
        $result= $this->db->get();
        $cities = $result->result_array();
//        
//        array('id' => $value['Key'],'name' => $value['Value'] )
                
        //$output['options'] = $this->sherutLeumi->ApiClientGet($url = 'v2/data/cities');
        $output['options'] = $cities;
        
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
public function getSchools4SelectDev($jPData = FALSE) {
    $output['funcName'] = $this->router->fetch_method();
    
    $this->db->select('keyName as id, valueName as name, placeName, schoolCode');
    $this->db->from('schoolsTempDev');
    $this->db->order_by('valueName ASC');
    $result = $this->db->get();
    $schools = $result->result_array();
    
    $return = array();
    
    foreach ($schools as $value) {
        if(!empty($value['placeName'])) {
            $name = $value['placeName'].' - '.$value['name'];
        } else {
            $name = $value['name'];
        }
        
        $valueReturn = array(
            'id' => $value['id'],
            'name' => $name
        );
        
        $return[] = $valueReturn;
    }
    
    $output['options'] = $return;
    
    $this->data = $output;
    
    return $this->output
            ->set_status_header(!isset($header) ? 200 : $header)
            ->set_content_type('application/json')
            ->set_output(json_encode($this->data));
}

    public function getSchoolsLand($jPData = FALSE) {
        
        
        $schoolsDb = $this->sherutLeumi->apiClientGetSchools();
        
        $output =  $this->msiteWs->sortArray($schoolsDb,$keyName = 'name',$order = SORT_ASC, $is_integer = false);
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    public function getSayarot($param = false) {
        
        $allSayarot = $this->sherutLeumi->apiClientGetSayarot($url = 'v2/data/sayarot');
        
        echo "<pre>";
        print_r($allSayarot);
        
    }
    
    
    public function getMekSherut($param = false) {
        
        $allSayarot = $this->sherutLeumi->apiClientGetMekSherut($url = 'v2/data/MekomotSherut');
        
        echo "<pre>";
        print_r($allSayarot);
        
    }
    
    
    
    
}