<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Sms extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+OtzarHaaretz';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+OtzarHaaretz-web';
        $this->data['current_language'] = 'he';
        $this->load->model('msiteWs');
        $this->load->model('OtzarHaretz');
        $this->load->helper('text');
        
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
//        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        
        $this->load->model('OtzarHaretz');
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    
    public function sendUserPassToUsers($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('all'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','id','userType'));
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            if( !empty($jsonPosts['id']) && !empty($jsonPosts['userType']) ) {
                
                
//                $output['ok'] = 'ok - '.$jsonPosts['id'].' - '.$jsonPosts['userType'];
                
                if($jsonPosts['userType'] == 'client') {
                    
                    $response = $this->sendPassToAllUsers($jsonPosts['id'],$this->data['code']);
                    
                    if($response) {
                        $output['ok'] = 'ok - '.$jsonPosts['id'].' - '.$jsonPosts['userType'];
                    } else {
                        $output['error'] = 'שגיאה';
                    }
                    
                }
                
                elseif($jsonPosts['userType'] == 'supplier') {
                    
                    $response = $this->sendPassToAllSuppliers($jsonPosts['id'],$this->data['code']);
                    
                    if($response) {
                        $output['ok'] = 'ok - '.$jsonPosts['id'].' - '.$jsonPosts['userType'];
                    } else {
                        $output['error'] = 'שגיאה';
                    }
                    
                };
                
            } else {
                
                $output['error'] = 'שגיאה';
                
            }
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
            
    
    
    
    
    public function sendSmsCustom ($jPData = FALSE) {
        
        $message = 'היי, שמי סבסטיאן מ-';
        $message .= "WaveProject, ";
        $message .= "לגבי האפליקציה של אוצר הארץ".". ";
        $message .= "לכניסה לאפליקציה:";
        $message .= "https://app.otzar-haretz.co.il/".' ';
        $message .= "שם משתמש: "."0524802321".' | ';
        $message .= "סיסמה: "."13777".' | ';
        $message .= "לשאילות: "."03-381-6663".' | ';
        $message .= "בהצלחה.";
        
        //$message = 'היי, שמי סבסטיאן מ-';
        
        //
        
        //echo $this->OtzarHaretz->sendSMS('0524802321', $message, $from = 'WaveProject', false );
    }
    
    
    
    public function getSms($jPData = FALSE) {
        
        // זה המספר – **********

        $values = $this->getDataFromSMS();
        
        //$values['sender'] = '972546464312';
        //$values['content'] = '33.33'; //supplier.kupa
        
        $clientData = $this->getClientData($values['sender']);
        
        if(!empty($clientData)) {
            
            
            $step = $this->checkStep($values,$clientData);
            
            if($step['step'] == 'start') {
            
                $this->startSms($values,$clientData);

            } else if ( $step['step'] == 'step1') {
                
                $this->smsStep1($values,$clientData,$step['log']);
                
            }
            
        } else {
            
            $message = "אין לקוח רשום במספר טלפון זה.";
            $message .= 'לבירורים חייגו: ';
            $message .= '9273*';
            
            //$phone = $values['sender'];
            $phone = $values['sender'];
            
            $this->OtzarHaretz->sendSMS($phone, $message, $from = '**********', false );
            //die($message);
            
        }
        
        
    }
    
    
    public function smsStep1($values,$clientData,$log) {
        
        //$values['content'] = '5';
        
        $cashTransfer = (float)$values['content'];
        
        $clientData['userId'] = $clientData['id'];
        $moneyBank = $this->OtzarHaretz->getUserMoney($clientData);
        
        
        if( $cashTransfer == 0 ) {
            
            $params['status'] = 0;
            
            $this->db->where('id', $log['id']);
            $this->db->update('smsReceivedLog', $params); 
            
            $message = 'העסקה התבטלה. הקלידו קוד SMS להתחלה.';
            
            
        
        } else if( $cashTransfer <=  $moneyBank && $cashTransfer > 0 ) {
            
                        
            $insert = $this->insertSmsLogStart($clientData,'','',$values,'done',$log);
            
            $transactionId = $this->insertTransaction($log,$cashTransfer);
            
            
            $params['transactionId'] = $transactionId;
            
            $this->db->where('id', $insert);
            $this->db->update('smsReceivedLog', $params); 
            
            //$message = 'תודה. הפעולה נקלטה בהצלחה.';
            $message = '';
            
            //$message = 'OK';
            
            
        } else {
            
            $message = 'אין לך מספיק כסף היום לעברה.';
            $message .= ' הקלידו סכום אחר או 0 לביטול.';
        }
        
        if(!empty($message)) {
            $this->OtzarHaretz->sendSMS($clientData['phone'], $message, $from = '**********', false );
        }
        
        //echo "<pre style='direction:RTL; font-size: 18px;'>";
        //echo $message.'<br/><br/>';
        //print_r($log);
        
        //die('<br/>'.'DIE: smsStep1');
        
    }
    
    
    
    public function insertTransaction($log,$cashTransfer) {
        
        if(!empty($cashTransfer)) {
            
        //$this->db->select('*');
                $this->db->select('phone');
                $this->db->from('cashiersSuppliers');
                $this->db->where('status', 1);
                $this->db->where('id', $log['cashierId']);
                $result= $this->db->get();
                $cashierData = $result->row_array();
                
                
                $jsonPosts['userId'] = $log['clientId'];
                $money = $this->OtzarHaretz->getUserMoney($jsonPosts);
                
            
                if( ( (float)$money >= (float)$cashTransfer ) && $cashTransfer > 0 ) {
                
                    
                    $table = 'transactions';

                    $future_id = $this->msite->get_max($table, 'id') + 1;
                    $tokenBuild = $log['clientId'].$log['supplierId'].(int)$cashTransfer.$future_id;
                    $token = $this->OtzarHaretz->CheckLen($tokenBuild,10,'start','0');

                    $data = array(
                        'lang' => NULL,
                        'sort' => $this->msite->get_max($table, 'sort') + 10,
                        'status' => 1,
                        'created_at' => date("Y-m-d H:i:s"),
                        'userId' => $log['clientId'],
                        'supplierId' => $log['supplierId'],
                        'cashierId' => $log['cashierId'],
                        'money' => $cashTransfer,
                        'token' => $token,
                        'is_SMSBuy' => 1
                    );

                    $insert = $this->db->insert($table, $data); 
                    $insert_id = $this->db->insert_id();

                    if(!empty($insert_id)) {
                        
                        $transaction = array(
                            'supplierId' => $log['supplierId'],
                            'cashierId' => $log['cashierId'],
                            'token' => $token,
                            'money' => $cashTransfer
                        );
                                
                    $dataTransaction = $this->getTransactionDataDb( $transaction, $userId = $log['clientId'] );
                        
                        if($dataTransaction && isset($dataTransaction['userPhone']) ) {
                            
                            $message = 'העברה על סך ';
                            $message .= '₪';
                            $message .= $dataTransaction['money'];
                            $message .= ' '.'ל-';
                            $message .= $dataTransaction['name'].',';
                            $message .= ' '.' קופה: ';
                            $message .= $dataTransaction['cashier'].' - ';
                            
                            
                            
                            $endMessage = ' הושלמה בהצלחה. ';
                            $endMessage .= 'מספר אסמכתא: ';
                            $endMessage .= $dataTransaction['token'];
                            
                            
                            
                            $messageSupplier = $message;
                            $messageSupplier .= 'מלקוח: ';
                            $messageSupplier .= $dataTransaction['nameBuyer'].'.';
                            $messageSupplier .= $endMessage;
                            
                            if(!empty($dataTransaction['supplierPhone'])) {
                                
                                $phone = $dataTransaction['supplierPhone'];
                                $output['sms'] = $this->OtzarHaretz->sendSMS($phone, $messageSupplier, $from = '**********', false );
                                
                            }
                            
                            if(isset($cashierData['phone']) && !empty($cashierData['phone'])) {
                                    
                                $phone = $cashierData['phone'];
                                $output['sms'] = $this->OtzarHaretz->sendSMS($phone, $messageSupplier, $from = '**********', false );
                                    
                            }
                            
                            
                            
                            
                            if(!empty($dataTransaction['userPhone'])) {
                                
                                $messageClient = $message.$endMessage;
                                
                                $phone = $dataTransaction['userPhone'];
                                $output['sms'] = $this->OtzarHaretz->sendSMS($phone, $messageClient, $from = '**********', false );
                                
                            }
                            
                            
                            return $insert_id;
                            
                        
                        } else {
                            return "error1";
                        }
                        
                        //$output['insertToken'] = $token;

                    } else {

                        return "error2";

                    }
                    
                } else {
                    return "error3";
                }
                
            } else {
                
                return "error4";
            }
            
                
    }
    
    
    
    
    
    
    public function checkStep($values,$clientData) {
        
        //$now = date("Y-m-d H:i:s");
        
        $today = date("Y-m-d 00:00:00");
        
        
        $this->db->select('*');
        $this->db->from('smsReceivedLog');
        $this->db->order_by('created_at', 'DESC');
        $this->db->where('clientId', $clientData['id']);
        $this->db->where('created_at >= ', $today);
        
        $this->db->where('status', 1);
        
        $result= $this->db->get();
        $smsLog = $result->row_array();
        
        
        $return = array(
            'step' => "start",
            'log' => array()
        );

        if(empty($smsLog)) {
            $return['step'] = "start";
            
        } else if ( $smsLog['step'] == 1 ) {
            
            $return['step'] = 'step1';
            $return['log'] = $smsLog;
            
        } else if ( $smsLog['step'] == 'done' ) {
            
            $return['step'] = "start";
            
        }
        
        return $return;
        
    }
    
    
    
    public function startSms($values, $clientData) {
        
        if(isset($values['content'])) {
            $supplierData = $this->getSupplierData($values['content']);
        } 
        
        
        
        if(!$supplierData) {
            
            $error = 'קוד SMS ששלחת לנו לא תקין'.'. ';
            $error .= 'אולי שכחת לרשום את הנקודה?'.'. ';
            $error .= 'הקוד אמור להיראות ככה: ';
            $error .= '00.00'.' ';
            $error .= 'נא לבדוק ולשלוח שנית.';
            
            if($clientData) {
                
                $message = 'שלום '.$clientData['firstName'].' '.$clientData['lastName'].'. ';
                $message .= $error;
                
            } else {
                $message = $error;
            }
            
        } 
        

        if( !empty($clientData) && $supplierData ) {
            
            $clientData['userId'] = $clientData['id'];
            $money = $this->OtzarHaretz->getUserMoney($clientData);
            
            $insert = $this->insertSmsLogStart($clientData,$supplierData,$money,$values);
            
            if(!empty($insert)) {
                
                $message = 'שלום '.$clientData['firstName'].' '.$clientData['lastName'].'. ';
                $message .= 'מקומך: '; 

                $message .= $supplierData['supplier']['name']. ' | '.'קופה: '.$supplierData['cashier']['name'].'. ';
                $message .= 'יתרתך להיום: '.$money.'. ';
                $message .= 'כמה כסף יש להעביר?';
                
                 $message .= ' הקלידו 0 לביטול.';
                
                
            } else {
                
                $message = 'שגיאה,'.' נא לנסות שנית.';
                
            }
            
        }
        
        
        //echo "<pre style='direction:RTL; font-size: 18px;'>";
        //echo $message.'<br/><br/>';
        $phone = $clientData['phone'];
        $this->OtzarHaretz->sendSMS($phone, $message, $from = '**********', false );
        
        //print_r($supplierData);
        //print_r($clientData);
        
        //print_r($money);
        
        
        
        
    }
    
    
    
    
    public function insertSmsLogStart($clientData,$supplierData,$money,$values,$step = false,$log = false) {
        
        
        $data = array(
            
            'lang' => NULL,
            'sort' => '',
            'status' => 1,
            'created_at' => date("Y-m-d H:i:s"),
            'clientId' => $clientData['id'],
            'msgId' => isset($values['msgId']) ? $values['msgId'] : '',
            'sender' => isset($values['sender']) ? $values['sender'] : '',
            'recipient' => isset($values['recipient']) ? $values['recipient'] : '',
            'segments' => isset($values['segments']) ? $values['segments'] : '',
            'content' => isset($values['content']) ? $values['content'] : '',
            'receivedTime' => isset($values['receivedTime']) ? $values['receivedTime'] : '',
            'step' => !$step ? '1' : $step,
            'money' => $money,
            'supplierId' => isset($supplierData['supplier']['id']) ? $supplierData['supplier']['id'] : '',
            'cashierId' => isset($supplierData['cashier']['id']) ? $supplierData['cashier']['id'] : '',
            'logId' => isset($log['id']) ? $log['id'] : ''
        );

        $insert = $this->db->insert('smsReceivedLog', $data); 
        $insert_id = $this->db->insert_id();
        
        return $insert_id;
        
        
    }
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    public function getSupplierData($code) {
        
        $arrayCode = explode('.', $code);
        
        if( isset($arrayCode[0]) && isset($arrayCode[1]) && count($arrayCode) == 2 ) {
            
            $this->db->select('*');
            $this->db->from('suppliers');
            $this->db->where('id', $arrayCode[0]);
            $result= $this->db->get();
            $supplier = $result->row_array();
            
            if(!empty($supplier)) {
                
                $this->db->select('*');
                $this->db->from('cashiersSuppliers');
                $this->db->where('id', $arrayCode[1]);
                $result= $this->db->get();
                $cashier = $result->row_array();
                
                if(!empty($cashier)) {
                    
                    return array(
                        'supplier' => $supplier,
                        'cashier' => $cashier
                    );
                    
                } else {
                    
                    return false;
                    
                }
                
                
            } else {
                
                return false;
                
            }
            
        }
        
        else {
            
            return false;
            
        }
        
        //print_r($arrayCode);
               
    }
    
    
    
    
    public function getClientData($phoneClient) {
        
        
        $sender = substr($phoneClient, 3);
        $sender = '0'.$sender;
        
        $this->db->select('id,firstName,lastName,created_at,TotalPrice,OrderId,phone');
        $this->db->from('leadsLandpage');
        $this->db->where('phone', $sender);
        $result= $this->db->get();
        $user = $result->row_array();
        
        return $user;
        
        
    }
    
    
    
    public function getDataFromSMS() {
        
        $smsGetData = array('msgId','sender','recipient','segments','content','receivedTime');
        
        foreach ($smsGetData as $inputName) {
            $values[$inputName] = !empty($this->input->post($inputName, true)) ?  $this->input->post($inputName, true) : '';
        }
        
        return $values;
        
        
    }

    

























    public function showNoSMSPhone ($param=false) {
        
        if($this->input->get('code') != '0223jc!u') {
            die('error');
        }
        
        $this->load->model('OtzarHaretz');
        
        //echo 'funcName: ' . $this->router->fetch_class().' - '.$this->router->fetch_method();
        
        $this->db->select('id,phone,firstName,lastName');
        $this->db->from('leadsLandpage');
        //$this->db->where('id', '4487');
        
        
        $result= $this->db->get();
        $users = $result->result_array();
        
        
        $counter = 0;
        
        $noCell = array();
        
        foreach ($users as $key => $value) {
            
            $phone= $this->OtzarHaretz->returnNoCellsPhone($value['phone']); 

            //print_r($phone);die();
            
            if(!empty($phone)) {

               $noCell[] = array(
                   'id' => $value['id'],
                   'name' => $value['firstName'].' '.$value['lastName'],
                   'phone' => $value['phone']
               );

            }
             
            
        }
        
        echo "<pre>";
        echo 'COUNT: '.count($noCell).' / '.$result->num_rows().'<br/><br/>';
        
        print_r($noCell);
        
        echo "</pre>";
        
    }
    
    
    
    public function showduplicaPhones ($param=false) {
        
        if($this->input->get('code') != '0223jc!u') {
            die('error');
        }
        
        $this->load->model('OtzarHaretz');
        $this->db->select('id,phone,firstName,lastName,email,user');
        $this->db->from('leadsLandpage');
        
        $this->db->where('id !=', '1');
        $this->db->where('id !=', '2');
        $this->db->where('status', 1);
        
        $result= $this->db->get();
        $users = $result->result_array();
        
        $duplicates = array();
        
        foreach ($users as $key => $value) {
            
            $count = 1;
            foreach ($users as $key => $value2) {
                
                if( $value['phone'] == $value2['phone'] ) {
                    
                    if($count >= 2) {
                        
                        $duplicates[$value['phone']][] = array(
                            'id' => $value['id'],
                            'name' => $value['firstName'].' '.$value['lastName'],
                            'phone' => $value['phone'],
                            'email' => $value['email'],
                            'user' => $value['user'],
                        );
                        
                    }
                    
                    $count++;
                }
                
                
            }
        }
        
        echo '<pre>COUNT: '.count($duplicates).'<br/><br/>';
        
        print_r($duplicates);
        
        echo "</pre>";
        
    }
    
    
    
    public function showduplicaOrders ($param=false) {
        
        if($this->input->get('code') != '0223jc!u') {
            die('error');
        }
        
        $this->load->model('OtzarHaretz');
        $this->db->select('id,phone,firstName,lastName,email,OrderId');
        $this->db->from('leadsLandpage');
        
        $this->db->where('id !=', '1');
        $this->db->where('id !=', '2');
        
        $result= $this->db->get();
        $users = $result->result_array();
        
        $duplicates = array();
        
        foreach ($users as $key => $value) {
            
            $count = 1;
            foreach ($users as $key => $value2) {
                
                if( $value['OrderId'] == $value2['OrderId'] ) {
                    
                    if($count >= 2) {
                        
                        $duplicates[$value['OrderId']][] = array(
                            'id' => $value['id'],
                            'name' => $value['firstName'].' '.$value['lastName'],
                            'phone' => $value['phone'],
                            'email' => $value['email']
                        );
                        
                    }
                    
                    $count++;
                }
                
                
            }
        }
        
        echo '<pre>COUNT: '.count($duplicates).'<br/><br/>';
        
        print_r($duplicates);
        
        echo "</pre>";
        
    }
    
    
    
    private function getTransactionDataDb($transaction, $userId=false) {
        
        
        
        if($userId) {
            
            $this->db->select('phone,firstName,lastName');
            $this->db->from('leadsLandpage');
            $this->db->where('id', $userId);
            $result= $this->db->get();
            $user = $result->row_array();
            
        } else {
            $user = array();
        }
        
        $this->db->select('name,phone');
        $this->db->from('suppliers');
        $this->db->where('id', $transaction['supplierId']);
        $result= $this->db->get();
        $supplierData = $result->row_array();

        $this->db->select('name');
        $this->db->from('cashiersSuppliers');
        $this->db->where('id', $transaction['cashierId']);
        $result= $this->db->get();
        $cashierData = $result->row_array();

        if(!empty($supplierData) && !empty($cashierData) ) {

            $firstName = isset($user['firstName']) ? $user['firstName'] : ''; 
            $lastName = isset($user['lastName']) ? $user['lastName'] : ''; 
            
            $name = $firstName.' '.$lastName;
            
            $return = array(
                'id' => isset($transaction['id']) ? $transaction['id'] : '',
                'dateTransaction' => isset($transaction['created_at']) ? $transaction['created_at'] : '',
                'nameBuyer' => $name,
                'userPhone' => isset($user['phone']) ? $user['phone'] : '',
                'name' => $supplierData['name'],
                'token' => $transaction['token'],
                'cashier' => $cashierData['name'],
                'money' => $transaction['money'],
                'supplierPhone' => $supplierData['phone']
            );
            
            return $return;
            
        } else {

            return false;

        }
    
    }
 
    
    public function sendPassToAllUsers ($userId = false, $siteCode = false) {
        
       
        $this->load->model('OtzarHaretz');
        
        
        if(!$userId) {
            
            //die('MANUAL SEND - NOT ALLOW');
        
            //https://otzarhaaretz.wdev.co.il/api/sms/sendPassToAllUsers?code=0223jc!u

            if($this->input->get('code') != '0223jc!u') {
                die('error');
            }

            

            $this->db->select('id,user,phone,firstName,lastName,email');

            //echo "<br/><br/>".date("H:i:s")."<br/><br/>";
            $this->db->limit(100);
            $tableName = 'leadsLandpage';
            //$this->db->where('id', 1);
            
            $this->db->from($tableName);

            $this->db->where('smsSent', 0);
            $this->db->where('status', 1);

            $result= $this->db->get();
            $users = $result->result_array();
        
        } else {
            
            if($siteCode != $this->data['code']) {
                
                die('error');
                
            }
            
            $this->db->select('id,user,phone,firstName,lastName,email');
            $this->db->limit(1);
            
            $tableName = 'leadsLandpage';

            $this->db->from($tableName);
            $this->db->where('id', $userId);

            $result= $this->db->get();
            $users = $result->result_array();
            
            
        }
        
        if(!empty($users)) {
            
            $this->load->helper('string');
            
            foreach ($users as $value) {
                
                $random = random_string('numeric', 4);
                
                $newData = array (
                    'password' => md5($random)
                );
                
                $this->db->where('id', $value['id']);
                $update = $this->db->update($tableName, $newData); 
                
                //echo ' - '.$value['email'];
                
                if($update) {
                    
                    //$url = $this->OtzarHaretz->getBitlyUrl('https://app.otzar-haretz.co.il/?username='.$value['user']);
                    
                    $url = 'https://bit.ly/2WJ5Ihk';
                    
                    $phone = $value['phone'];
                    
                    
                    
                    $message = 'שלום '.$value['firstName'].' '.$value['lastName'].'. ';
                    $message .= 'להלן פרטי הגישה שלך למערכת מימוש השוברים של אוצר הארץ, להתחברות לחצו כאן >> ';
                    $message .= $url;
                    
                    $message .= ' | '."שם משתמש: ".$value['user'].' | ';
                    $message .="סיסמה: ".$random;
                    
                    $message .= ' | '.'למרכז ההדרכה>> '.'https://bit.ly/38B9WKc';
                    
                    $smsResponse = $this->OtzarHaretz->sendSMS($phone, $message, $from = 'OtzarHaretz', $param = FALSE);
                    $smsResponse = json_decode($smsResponse, TRUE);
                    
                    //email email !!!! ----------------------------------------------------
                    
                    $value['textTitle'] = 'שלום '.$value['firstName'].' '.$value['lastName'];
                    $value['text'] = "ברוכים הבאים לאוצר הארץ שמיטה לכתחילה".'<br/><br/>';
                     
                    $value['textR'] = 'כעת, יש באפשרותכם לממש את השוברים באחת החנויות של אוצר הארץ, ולהנות מתוצרת אוצר הארץ שמיטה לכתחילה.'.'<br/>';
                    $value['textR'] .= 'לצורך מימוש השוברים עבור פירות וירקות המהודרים בכל שנת השמיטה בקלות ובנוחות, פיתחנו מערכת יחודית, במאמץ רב ומחשבה מרובה.'.'<br/><br/>';
                    
                    $value['textR'] .= 'בברכת שנה טובה לכל בית ישראל.'.'<br/><br/>';
                    
                    $value['textC'] = 'להלן פרטי הגישה האישיים שלכם למערכת >> '.'<br/><br/>';
                    
                    $value['urlApp'] = $url;
                    
                    $value['textD'] = '<br/><br/>'.'לנוחיותכם, קישור למרכז ההדרכה של המערכת >>'.'<br/>';
                    $aUrl = 'https://bit.ly/38B9WKc';
                    
                    $value['textD'] .= '<a href="'.$aUrl.'" >'.$aUrl.'</a>'.'<br/><br/>';

                    $value['password'] = $random;

                    $html_body = $this->bodyMail($value);
                    $to_emails = $value['email'];
                    $subject = $value['firstName'].' '.''.'פרטי הגישה שלך למערכת אוצר הארץ';

                    $sendMail = $this->send_emailPHPMailer($to_emails, $subject,$html_body);
                    // ----------------------  END SEND MAIL!! ---------------------------------
                    
                    
                    if(isset($smsResponse['success'])) {
                        
                        
                        $newData = array (
                            'smsSent' => 1
                        );

                        $this->db->where('id', $value['id']);
                        $update = $this->db->update($tableName, $newData);
                        
                        
                    } else {
                        
                        $newData = array (
                            'smsSent' => 1,
                            'smsSentError' => 1
                        );
                        
                        $this->db->where('id', $value['id']);
                        $update = $this->db->update($tableName, $newData); 
                        
                    }
                    
                    //echo $message.'<br/><br/>';
                    
                }
                
                if($userId) {
                    return true;
                }
                
                
            }
            
            
            
        } else {
            if($userId) {
                return false;
            } else {
                echo "--- NO USERS ---";
            }
            
        }
        
        if(!$userId) {
            $this->db->select('id');
            //$this->db->from('leadsLandpage');
            $this->db->from($tableName);
            $this->db->where('smsSent', 1);
            $result= $this->db->get();

            echo "<pre>";
            echo "<br/><br/>".date("H:i:s")."<br/><br/>";
            echo "Sent to:".count($users);
            echo "<br/><br/>".'All Sents:'.$result->num_rows();
            //print_r($users);
        }
        
    }
    
    
    
    
    
    public function sendPassToAllSuppliers ($userId = false, $siteCode = false) {
        
        $this->load->model('OtzarHaretz');
        
        if(!$userId) {
        
            //81dc9bdb52d04dc20036dbd8313ed055

            //https://otzarhaaretz.wdev.co.il/api/sms/sendPassToAllSuppliers?code=0223jc!u

            die('MANUAL SEND - NOT ALLOW');
            
            if($this->input->get('code') != '0223jc!u') {
                die('error');
            }



            $this->db->select('id,username,phone,name');

            //$this->db->from('suppliers');
            $this->db->limit(20);
            $tableName = 'suppliers';

            $this->db->from($tableName);

            $this->db->where('smsSent', 0);
            //$this->db->where('id !=', '2');

            $result= $this->db->get();
            $users = $result->result_array();
            
        } else {
            
            if($siteCode != $this->data['code']) {
                die('error');
            }
            
            $this->db->select('id,username,phone,name');
            $this->db->limit(1);
            $tableName = 'suppliers';
            $this->db->from($tableName);

            $this->db->where('id', $userId);

            $result= $this->db->get();
            $users = $result->result_array();
            
            
        }
        
        if(!empty($users)) {
            
            $this->load->helper('string');
            
            foreach ($users as $value) {
                
                $random = random_string('numeric', 4);
                
                $newData = array (
                    'passwordMd5' => md5($random)
                );
                
                $this->db->where('id', $value['id']);
                $update = $this->db->update($tableName, $newData); 
                
                if($update) {
                    
                    $url = $this->OtzarHaretz->getBitlyUrl('https://app.otzar-haretz.co.il/supplier?username='.$value['username']);
                    
                    $phone = $value['phone'];
                    
                    $message = 'שלום '.$value['name'].'. ';
                    $message .= 'להלן פרטי הגישה שלך למערכת התשלומים של אוצר הארץ, להתחברות לחצו כאן >> ';
                    $message .= $url;
                    
                    $message .= ' | '."שם משתמש: ".$value['username'].' | ';
                    $message .="סיסמה: ".$random;
                    
                    $message .= ' | '.'לבירורים נוספים: '.'9273*';
                    
                    $smsResponse = $this->OtzarHaretz->sendSMS($phone, $message, $from = 'OtzarHaretz', $param = FALSE);
                    $smsResponse = json_decode($smsResponse, TRUE);
                    
                    if(isset($smsResponse['success'])) {
                        
                        
                        $newData = array (
                            'smsSent' => 1
                        );

                        $this->db->where('id', $value['id']);
                        $update = $this->db->update($tableName, $newData);
                        
                        if(!empty($users)) {
                            return true;
                        }
                        
                    } else {
                        
                        $newData = array (
                            'smsSent' => 1,
                            'smsSentError' => 1
                        );
                        
                        $this->db->where('id', $value['id']);
                        $update = $this->db->update($tableName, $newData);
                        
                        if(!empty($users)) {
                            return false;
                        }
                        
                    }
                    
                    //echo $message.'<br/><br/>';
                    
                }
                
                
            }
            
            
            
        } else {
            if(!empty($users)) {
                
                return false;
                
            } else {
                echo "--- NO USERS ---";
            }
            
        }
        
        if(!empty($users)) {
            $this->db->select('id');
            //$this->db->from('suppliers');
            $this->db->from($tableName);
            $this->db->where('smsSent', 1);
            $result= $this->db->get();

            echo "<pre>";
            echo "Sent to:".count($users);
            echo "<br/><br/>".'All Sents:'.$result->num_rows();
            //print_r($users);
        }
        
    }
    
    
    
    
    
    public function csvSupplierCodesSMSSystem() {
        
        //https://otzarhaaretz.wdev.co.il/api/sms/csvSupplierCodesSMSSystem?code=0223jc!u
            
        if($this->input->get('code') != '0223jc!u' && $this->input->get('code') != md5($this->data['code'])) {
            die('error');
        }
        
        $this->load->model('OtzarHaretz');

        $this->db->select('id,name,phone,ownerName,address,comments,status,agent,email,tz');
        $this->db->from('suppliers');
        $this->db->order_by('name', 'ASC');
        
        $result= $this->db->get();
        $suppliersDb = $result->result_array();
            

        $data = array();

        if(!empty($suppliersDb)) {

            
            foreach ($suppliersDb as $supplier) {
                
                $supplierData = array (
                        'id' => $supplier['id'],
                        'status' => $supplier['status'],
                        'name' => $supplier['name'],
                        'tz' => $supplier['tz'],
                        //'phone' => str_replace("+972", "", $supplier['phone'])
                );
                
                $this->db->select('id,name,supplierId');
                $this->db->from('cashiersSuppliers');
                $this->db->where('supplierId', $supplier['id'] );
                $this->db->where('status', 1 );
                $result= $this->db->get();
                $cashiers = $result->result_array();
                
                if(!empty($cashiers)) {
                    
                    $cashierData = array();
                    foreach ($cashiers as $cashier) {
                        
                        
                        $value = 'קוד SMS קופה'.' '.$cashier['name'].': '.$supplier['id'].'.'.$cashier['id'];
                        array_push($supplierData,$value);
                    }
                }
                
                
                $data[] = $supplierData;
                
                
            }

        }
        
        //echo "<pre>";
        //print_r($data);die('sebas');
        
        $dateFile = date('d_m_Y');  //date('m');
        $filename = 'supplierCodeSMSSystem_'.rand(111,999);
        
        $csv = array();
        
        $csv[] = array (
            '',
            '',
            'טלפון לשליחת SMS: '.'**********'
        );
        
        $csv[] = array (
            ' '
        );
        
        $csv[] = array (
            'id' => '#',
            'status' => 'סטטוס',
            'name' => 'שם',
            'tz' => 'ת.ז.',
            //'phone' => 'טלפון'
        );
        
        $excellData = array_merge($csv,$data);
        
        
        
        header('Content-Encoding: UTF-8'); 
        header('Content-type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename='.$filename.'.csv');
        header("Pragma: no-cache");
        header("Expires: 0");

        $handle = fopen('php://output', 'w');
        fwrite($handle, "\xEF\xBB\xBF");
        

        foreach ($excellData as $data_array) {
            fputcsv($handle, $data_array);
        }
            fclose($handle);
        exit;
        
        
        
        
    }
    
    
    private function bodyMail($params) {
        
         //table lead style
        
        
        $td_style_title="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 22px;
                  padding: 20px 0 5px;color:#009eb3;";  
        
        $td_style_1="background-color: rgb(235, 235, 235); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;";  

        $td_style_2="background-color: rgb(213, 213, 213); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;padding:10px 0;";
        
        $td_style_p ="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 14px;
                  padding:0px 0;color: #100f15;"; 
        
        $td_style_pR ="text-align: right;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:regular;font-size: 14px;
                  padding:0px 20px;color: #100f15;"; 
        
        $imgTop = base_url().IMG.'mailImg/top.jpg?v='.VERSION;
        $imgDown = base_url().IMG.'mailImg/down-noEnter.jpg?v='.VERSION;
        $logo = base_url().IMG.'mailImg/logo.jpg?v='.VERSION;
        
        $tableData = "<table style='background: #f3f3f3;margin:0;padding:0;' align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' >";
        
        $tableData .= "<tbody>";
        $tableData .= "<tr><td colspan=2 style='$td_style_title'>{$params['textTitle']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['text']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_pR'>{$params['textR']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['textC']}</td></tr>";
        
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['urlApp']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>קישור</span>
                </td>
            </tr>";
        
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['user']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>שם משתמש</span>
                </td>
            </tr>";
                
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['password']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>סיסמה</span>
                </td>
            </tr>";
                
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['textD']}</td></tr>";
                
        $tableData .= "</tbody>";
        $tableData .="</table>";
       

        $body_mail= "
        <div style='text-align: center;text-direction:rtl; font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;'>
        <br/><br/>";
        
        $body_mail .= "<table align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' style='width: 345px;'>
        <tbody>";
        
        $body_mail .= "<tr><td style='padding: 0px;' border='0' cellpadding='0' cellspacing='0'><a style='padding: 0;margin:0;display:block;' href='https://site.otzar-haretz.co.il'><img style='display: inherit;' src='$imgTop' border='0'></a></td></tr>";
        $body_mail .= "<tr><td style='padding: 0px 10px' >$tableData</td></tr>";
                
        $body_mail .= "<tr>
                <td style='text-align:center'>
                  <a style='padding: 0;' href='https://waveproject.co.il/'>
                  <img style='display: inherit;' src='$imgDown' border='0'>
                  </a>
                </td></tr>";
        
        $body_mail .= "<tr>
            <td style='text-align:center'>
              <a style='padding: 0;' href='https://waveproject.co.il/'>
              <img style='display: inherit;' src='$logo' border='0'>
              </a>
            </td></tr>";
                
                
                
        $body_mail .= "</tbody></table><br/><br/></div>";
        
        return $body_mail;  
    }
    
    
    public function send_emailPHPMailer($to, $subject,$html_body) {
        
        //echo "a";die();
        
        // Load PHPMailer library
        $this->load->library('phpmailer_lib');
        
        // PHPMailer object
        $mail = $this->phpmailer_lib->load();
        
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );
        
        
        $mail->isSMTP(); 
        //$mail->SMTPDebug = 2;                                     
        $mail->Host = 'in-v3.mailjet.com'; 
        $mail->SMTPAuth = true;                              
        $mail->Username = 'fe313beca5ed02d82af856c9a94e0e6b';                 
        $mail->Password = '53edd86632c33b0ef4ceaae3eeb1dfe5';                           
        $mail->SMTPSecure = 'ssl';                            
        $mail->Port = 465;   

        $mail->CharSet = 'UTF-8';                            
        $mail->setFrom('<EMAIL>', 'אוצר הארץ');
        $mail->addAddress($to); 
        
        //$mail->addAddress('<EMAIL>'); 
        //$mail->addReplyTo('<EMAIL>', 'no-reply');
        
        $mail->isHTML(true);                                  
        $mail->Subject = $subject;
        $mail->Body = $html_body;
        $mail->send();
        
        
    }
    
    
    
    public function send_email($to_emails, $subject,$html_body) {
        
        //NO NO NO NO NO NO
        
        //https://codeigniter.com/userguide3/libraries/email.html
        //https://stackoverflow.com/questions/1555145/sending-email-with-gmail-smtp-with-codeigniter-email-library
        
        $this->load->library("email");
        
        $config['protocol'] = 'SMTP';
        $config['smtp_host'] = 'in-v3.mailjet.com';
        $config['smtp_port'] = 465;
        $config['smtp_user'] = 'fe313beca5ed02d82af856c9a94e0e6b';
        $config['smtp_pass'] = '53edd86632c33b0ef4ceaae3eeb1dfe5';
        $config['_smtp_auth']   = TRUE;
        $config['mailtype']  = 'html';
        $config['charset']   = 'UTF-8';
        
        $this->email->initialize($config);
        $this->email->from('<EMAIL>', 'אוצר הארץ');
        $this->email->to($to_emails);
        

        $this->email->subject($subject);
        $this->email->message($html_body);
        $response['success'] = $this->email->send();
        
        //echo "<br/><br/>Sent Mail?";
        print_r($this->email->print_debugger());
        
        
//        $this->output->set_status_header('200');
//            
//        return $this->output
//                ->set_content_type('application/json')
//                ->set_output(json_encode($response));
    }
    
    
    
    
    public function testSebasNode($jPData = FALSE) {
        
        $this->_loaderWS();
        $header = 200;
        //$header = 403;
        //set_status_header(404);
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        //$pageAutoriced = array('all'); //all //superAdmin  //adminOnly  //userOnly
        //$jsonPosts = $this->msiteWs->getPostFromJson(array('userId','token','userCredential','id','userType'));
        //$checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        //if($checkUserCredentials != 'unauthorized') {
            
            //$output['a'] = 'aaa';
            
        //}
        
//        else {
//            $header = 403; //Forbidden
//            //$output = $checkUserCredentials;
//        }
        
        $this->data = $output;
         
        return $this->output
                ->set_status_header(!isset($header) ? 200 : $header)
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    
    
    
    
}