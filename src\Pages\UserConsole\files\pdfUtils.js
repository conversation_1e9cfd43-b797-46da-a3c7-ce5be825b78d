/**
 * pdfUtils.js - עזרים לעבודה עם קבצי PDF
 *
 * מכיל פונקציות שעוזרות ליצירת קבצי PDF, טעינת תמונות ל-PDF והמרות
 */
import html2pdf from "html2pdf.js";
import documentStyles from "./documentStyles.css?raw";

/**
 * קונפיגורציה מתקדמת של html2pdf
 * @param {string} filename - שם הקובץ לשמירה
 * @returns {Object} אובייקט קונפיגורציה
 */
export const getPdfOptions = (filename, documentType) => {
  // הגדרות מיוחדות לטופס 101 כדי לוודא שהוא יתאים לעמוד אחד
  if (documentType === "101") {
    return {
      margin: [2, 2, 2, 2], // שוליים צרים יותר לטופס 101
      filename: filename || "document.pdf",
      image: { type: "jpeg", quality: 0.95 },
      html2canvas: {
        scale: 1.8, // סקאלה מותאמת לטופס 101
        useCORS: true,
        logging: false,
        backgroundColor: "#fff",
        scrollY: 0,
      },
      jsPDF: {
        unit: "mm",
        format: "a4",
        orientation: "portrait",
        compress: true,
        precision: 16,
        putOnlyUsedFonts: true,
        floatPrecision: "smart",
        autoPaging: false, // מבטל דפדוף אוטומטי
        pagesplit: false, // מנסה להימנע מפיצול
      },
      pagebreak: { mode: ["avoid-all", "css", "legacy"] }, // הימנעות מפיצול עמודים בכל מחיר
    };
  }

  // הגדרות רגילות לשאר המסמכים
  return {
    margin: [3, 3, 3, 3],
    filename: filename || "document.pdf",
    image: { type: "jpeg", quality: 0.95 },
    html2canvas: {
      scale: 2,
      useCORS: true,
      logging: false,
      backgroundColor: "#fff",
      scrollY: 0,
    },
    jsPDF: {
      unit: "mm",
      format: "a4",
      orientation: "portrait",
      compress: true,
      precision: 16,
      putOnlyUsedFonts: true,
      floatPrecision: "smart",
      autoPaging: false,
      pagesplit: false,
    },
    pagebreak: { mode: ["avoid-all"] },
  };
};

/**
 * טוען תמונה עם טיפול בשגיאות וגישה דרך פרוקסי אם צריך
 * @param {string} originalUrl - כתובת התמונה המקורית
 * @param {Object} signatures - אובייקט המכיל חתימות ידועות לגיבוי
 * @returns {Promise<HTMLImageElement>} הבטחה המחזירה אלמנט תמונה טעון
 */
export const loadImageWithProxy = (originalUrl, signatures) => {
  return new Promise((resolve, reject) => {
    // בדוק האם מדובר בחתימה חיצונית
    const lowerUrl = originalUrl.toLowerCase();
    const filename = originalUrl.split("/").pop().toLowerCase();

    // בחר את החתימה המתאימה לפי שם הקובץ
    let localImageSrc = null;

    if (lowerUrl.includes("yaron") || filename.includes("yaron")) {
      localImageSrc = signatures.yaron;
    } else if (
      lowerUrl.includes("aguda") ||
      filename.includes("aguda_sig") ||
      filename.includes("bbbb")
    ) {
      localImageSrc = signatures.aguda;
    } else if (lowerUrl.includes("logo")) {
      localImageSrc = signatures.logo;
    }

    // אם יש לנו חתימה מקומית - השתמש בה
    if (localImageSrc) {
      const localImg = new Image();
      localImg.onload = () => resolve(localImg);
      localImg.onerror = (err) => {
        console.error("Error loading local signature:", err);
        reject(err);
      };
      localImg.src = localImageSrc;
      return;
    }

    // אם זו תמונה חיצונית שאינה חתימה ידועה - נסה לטעון ישירות
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.onload = () => resolve(img);
    img.onerror = (err) => {
      console.error("Error loading image:", err, "URL:", originalUrl);

      // במקרה של כישלון, החזר את חתימת האגודה כברירת מחדל
      const fallbackImg = new Image();
      fallbackImg.onload = () => resolve(fallbackImg);
      fallbackImg.onerror = (fallbackErr) => reject(fallbackErr);
      fallbackImg.src = signatures.aguda;
    };
    img.src = originalUrl;
  });
};

/**
 * ממיר תמונה לייצוג Base64
 * @param {string} url - כתובת התמונה
 * @param {Object} signatures - אובייקט החתימות לגיבוי
 * @returns {Promise<string>} הבטחה המחזירה ייצוג Base64 של התמונה
 */
export const convertImageToBase64 = (url, signatures) => {
  return new Promise((resolve, reject) => {
    // בדוק אם מדובר בחתימה מקומית
    const lowerUrl = url.toLowerCase();
    const filename = url.split("/").pop().toLowerCase();

    let localImageSrc = null;

    if (lowerUrl.includes("yaron") || filename.includes("yaron")) {
      localImageSrc = signatures.yaron;
    } else if (
      lowerUrl.includes("aguda") ||
      filename.includes("aguda_sig") ||
      filename.includes("bbbb")
    ) {
      localImageSrc = signatures.aguda;
    }

    // אם מדובר בחתימה מקומית
    if (localImageSrc) {
      const img = new Image();
      img.onload = () => {
        try {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0);
          const dataURL = canvas.toDataURL("image/jpeg");
          resolve(dataURL);
        } catch (err) {
          console.error("Error converting local image to base64:", err);
          reject(err);
        }
      };
      img.onerror = (err) => {
        console.error("Error loading local image:", err);
        reject(err);
      };
      img.src = localImageSrc;
      return;
    }

    // עבור תמונות חיצוניות
    loadImageWithProxy(url, signatures)
      .then((img) => {
        try {
          const canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0);
          const dataURL = canvas.toDataURL("image/jpeg");
          resolve(dataURL);
        } catch (err) {
          console.error("Error converting image to base64:", err);
          reject(err);
        }
      })
      .catch(reject);
  });
};

/**
 * מקטין את גודל כל החתימות במסמך, במיוחד בדוחות תקבולים
 * @param {HTMLElement} element - אלמנט HTML שמכיל את המסמך
 * @param {string} documentType - סוג המסמך (101, 102, 104)
 */
export const resizeSignaturesInDocument = (element, documentType) => {
  try {
    // הקטנה מיוחדת לחתימות בדוחות תקבולים
    if (documentType === "104") {
      // מצא את כל החתימות האפשריות
      const signatures = element.querySelectorAll(
        'img[src*="yaron"], img[src*="signature"], .signature-area img, .signature-box img'
      );

      console.log(`נמצאו ${signatures.length} חתימות לשינוי גודל בדוח תקבולים`);

      // עבור על כל החתימות והקטן אותן
      signatures.forEach((sig) => {
        sig.style.cssText = `
          width: 60px !important;
          max-width: 60px !important;
          height: auto !important;
          opacity: 0.85;
          display: block;
        `;

        // גם שנה את המעטפת אם יש
        const wrapper = sig.closest(".signature-area, .signature-box");
        if (wrapper) {
          wrapper.style.cssText = `
            width: auto !important;
            max-width: 80px !important;
            text-align: right !important;
            margin: 10px 0 !important;
            border: none !important;
          `;
        }
      });
    }
  } catch (error) {
    console.error("שגיאה בשינוי גודל חתימות:", error);
  }
};

/**
 * יוצר PDF מתוך מסמך HTML
 * @param {HTMLElement} element - אלמנט HTML ליצירת PDF ממנו
 * @param {string} filename - שם הקובץ
 * @param {Function} onComplete - פונקציית קולבק לאחר סיום היצירה
 * @param {Function} onError - פונקציית קולבק במקרה של שגיאה
 * @param {string} documentType - סוג המסמך (אופציונלי)
 */
export const generatePdfFromElement = (
  element,
  filename,
  onComplete,
  onError,
  documentType
) => {
  // קודם נשנה את גודל החתימות אם יש צורך
  if (documentType === "104") {
    resizeSignaturesInDocument(element, documentType);
  }

  const options = getPdfOptions(filename, documentType);

  html2pdf()
    .set(options)
    .from(element)
    .save()
    .then(() => {
      console.log("PDF נוצר בהצלחה");
      if (onComplete) onComplete();
    })
    .catch((err) => {
      console.error("שגיאה ביצירת PDF:", err);
      if (onError) onError(err);
    });
};

/**
 * יוצר עיצוב גלובלי למסמך PDF
 * @param {string} documentType - סוג המסמך (101, 102, 104)
 * @returns {HTMLStyleElement} אלמנט style עם ה-CSS הגלובלי
 */
export const createGlobalStyle = () => {
  const globalStyle = document.createElement("style");
  globalStyle.innerHTML =
    documentStyles +
    `
    /* סגנונות נוספים מותאמים להדפסה ו-PDF */
    @media print {
      /* הסרת מסגרות מאלמנטים במצב PDF */
      .date-field, .service-date, .period-date,
      [class*="date"], [id*="date"], [class*="תאריך"] {
        border: none !important;
        background-color: transparent !important;
        box-shadow: none !important;
        outline: none !important;
      }
      
      /* הקטנת פונט וריווח לכניסה בעמוד */
      body, .document-container {
        font-size: 10px !important;
        line-height: 1.1 !important;
      }
      
      /* הסרת קישוטים דקורטיביים בהדפסה */
      *::before, *::after {
        display: none !important;
        content: none !important;
      }
    }
  `;
  return globalStyle;
};
