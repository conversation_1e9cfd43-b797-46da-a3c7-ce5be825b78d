<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Trumot extends CI_Controller {
    
    private $data;
    private $folderView;
    
    
    public function __construct() {
        parent::__construct();
        
        $this->data['code'] = 'seb-webProject!wd+=111@$%+OtzarHaaretz';
        $this->data['usersCode'] = 'seoject!wd+=111@$%+OtzarHaaretz-web';
        $this->data['current_language'] = 'he';
    }
    
    private function _loader($param = FALSE, $is_error = FALSE) {
        
        $this->data['platform'] = "desktop";
        
        
        $this->data['folderView'] = $this->folderView = $this->data['current_language'] . '/' . $this->data['platform'] . '/';
        
        if($is_error) {
            set_status_header($is_error);
            $this->data['controller'] = 'welcome';
            $this->data['method'] = 'error404';
            $this->data['param'] = $param;
        } else {
            $this->data['controller'] = $this->router->fetch_class();
            $this->data['method'] = $this->router->fetch_method();
            $this->data['param'] = $param;
        }
        
        $this->data['settings'] = $this->msite->get_settings();
        
        $this->data['pages'] = $this->msite->get_all_pages(0);
        $this->data['page_key'] = $page_key = $this->data['controller'] . '_' . $this->data['method'];
        $this->data['body_class'] = $this->data['controller'] . '-' . $this->data['method'];
        
        
        $page = isset($this->data['pages'][$page_key]) ? $this->data['pages'][$page_key] : $this->data['pages']['welcome_index'];
        $this->data['page'] = $this->msite->get_page_with_objects($page->Id(),$this->data['current_language']);
        $seo_id = $this->msite->get_seo_id($this->data['controller'], $this->data['method'], $this->data['param']);
        $this->data['page']->Set('seo', $this->msite->get_seo($seo_id));
        $this->data['alert'] = $this->session->flashdata('alert', false);
        
        $this->data['accessibility'] = 
                $this->session->has_userdata('accessibility') ? 
                $this->session->userdata('accessibility') : 
                array('zoom' => 0, 'accessibility' => false, 'display' => false, 'links' => false, 'texts' => false);
        
        //Menu
//        $this->msite->set_where("status='1'");
//        $this->msite->sort_objects("sort", "DESC");
//        $this->data['menu_items'] = $this->msite->get_all_objects('menu_items');
        
        //print_r($this->data['categories']);die();
        
        //$this->data['contactData'] = $this->msite->get_page_with_objects(16,$this->data['current_language']);
        
        //header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, OPTIONS');
    }
    

    
    private function _loaderWS($param = FALSE, $is_error = FALSE) {
         
        $this->load->model('msiteWs');
        $this->load->model('OtzarHaretz');
        
        
        if($param === 'uploadMethod') {
            
            $postCode = $this->input->post('siteCode');
            if( $postCode != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
        elseif($this->input->get('sebas')==1) {
            $output['ok'] = 'GETSebas_Loader';
        }
        
        else {
           $postCode = $this->msiteWs->getPostFromJson(array('siteCode'));
           if( $postCode['siteCode'] != md5($this->data['code']) ) {
                die('siteCodeERROR');
            }
        }
        
    }
    
    
    
    public function index($param = FALSE) {
        $this->_loaderWS($param);
        
       
        $output['trumot'] = 1;
        
        //$output['page'] = $this->data['page']->Arr();
        
        $this->data = $output;
        
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    public function getLead($param = FALSE) {

        //https://otzarhaaretz.wdev.co.il/api/trumot/getLead?FirstName=%D7%91%D7%93%D7%99%D7%A7%D7%94&LastName=%D7%A9%D7%9D_%D7%9E%D7%A9%D7%A4%D7%97%D7%94&Phone=0546464312&Email=<EMAIL>
        
        //WORDPRESS:
        //https://stackoverflow.com/questions/42530626/getting-order-data-after-successful-checkout-hook

        //  /wp-content/themes/hello-theme-child-master
        
        // http://otzar-haretz.co.il/
        
        
        $this->load->model('OtzarHaretz');
        
        $data = array(
            'GroupId' => '1113007582',
            'sendMailFalse' => $this->input->get_post('sendMailFalse', TRUE) ?  1 : '',
            'FirstName' => $this->input->get_post('FirstName', TRUE) ?  $this->input->get_post('FirstName', TRUE) : '',
            'LastName' => $this->input->get_post('LastName', TRUE) ?  $this->input->get_post('LastName', TRUE) : '',
            'Phone' => $this->input->get_post('Phone', TRUE) ?  $this->OtzarHaretz->cleanPhone($this->input->get_post('Phone', TRUE)) : '',
            'Email' => $this->input->get_post('Email', TRUE) ?  $this->input->get_post('Email', TRUE) : '',
            'OrderId' => $this->input->get_post('OrderId', TRUE) ?  $this->input->get_post('OrderId', TRUE) : '',
            'OrderName' => $this->input->get_post('OrderName', TRUE) ?  $this->input->get_post('OrderName', TRUE) : '',
            'Comments' => $this->input->get_post('Comments', TRUE) ?  $this->input->get_post('Comments', TRUE) : '',
            'CustomerIp' => $this->input->get_post('CustomerIp', TRUE) ?  $this->input->get_post('CustomerIp', TRUE) : '',
            'TotalPrice' => $this->input->get_post('TotalPrice', TRUE) ?  $this->input->get_post('TotalPrice', TRUE) : '',
            'ProductId' => $this->input->get_post('ProductId', TRUE) ?  $this->input->get_post('ProductId', TRUE) : '',
            'address' => $this->input->get_post('Address', TRUE) ?  $this->input->get_post('Address', TRUE) : '',
            'city' => $this->input->get_post('City', TRUE) ?  $this->input->get_post('City', TRUE) : '',
            'tz' => $this->input->get_post('Tz', TRUE) ?  $this->input->get_post('Tz', TRUE) : ''
        );
        
        
        $this->db->select('id');
        $this->db->from('leadsLandpage');
        $this->db->where('phone',$data['Phone']);
        $this->db->where('email',$data['Email']);
        $result = $this->db->get();
        $leadExiste = $result->row_array();
        
        //print_r($data);
        
        echo "<br/><br/><br/>Response:<br/>";
        
        if(empty($leadExiste)) {
            $insertContactTrumot = $this->insertContact($data); // Client Lead Trumot
            $addLeadtoDb = $this->addLeadtoDb($data);

            if( !empty($addLeadtoDb) ) {
                $sendLoginToUser = $this->sendLoginToUser($addLeadtoDb); // Mail
            } else $sendLoginToUser = 'falseDb';

            $response = array (
                'insertContactTrumot' => isset($insertContactTrumot) ?  $insertContactTrumot : 'false',
                'addLeadtoDb' => isset($addLeadtoDb) ?  $addLeadtoDb : 'false',
                'sendLoginToUser' => isset($sendLoginToUser) ?  'sent' : 'false2'
            );
        }
        
        else {
            $addBuysDb = $this->addBuysDb($data,$leadExiste['id']);
            $response = 'lead exist'.' - InsertBuy: '.$addBuysDb;
        }
        
        print_r($response);
        
        if(isset($sendLoginToUser) && !empty($sendLoginToUser)) {
            echo $sendLoginToUser;
        }
        
    }
    
    
     public function manualInsertClient($jPData = FALSE) {
        
        $this->_loaderWS();
        
        $output['funcName'] = $this->router->fetch_method(); //$this->router->fetch_class();
        
        $pageAutoriced = array('superAdmin'); //all //superAdmin  //adminOnly  //userOnly
        $jsonPosts = $this->msiteWs->getPostFromJson(
                array('userId','token','userCredential',
                    'firstName',
                    'lastName',
                    'phone',
                    'email',
                    'tz',
                    'city',
                    'address',
                    'OrderId',
                    'comments'
                    )
                );
        $checkUserCredentials = $this->msiteWs->checkUserCredentials($pageAutoriced,$jsonPosts,$this->data['usersCode']);
        
        if($checkUserCredentials != 'unauthorized') {
            
            $this->db->select('username');
            $this->db->from('app_Users');
            $this->db->where('id',$jsonPosts['userId']);
            $result = $this->db->get();
            $user = $result->row_array();
            
            if(empty($user)) {
                die('ERROR');
            }
            
            
            $OrdersName = array (                
                
                '1' => array('value' => 650, 'name' => 'מנוי בשווי 650 ₪'),
                '2'=> array('value' => 1300, 'name' => 'מינוי בשווי ₪1300'),
                '3'=> array('value' => 1800, 'name' => 'מינוי בשווי ₪1800')
            );
            
            $data = array(
                'GroupId' => '1113007582',
                'sendMailFalse' => 1,
                'FirstName' => $jsonPosts['firstName'],
                'LastName' => $jsonPosts['lastName'],
                'Phone' => $jsonPosts['phone'],
                'Email' => $jsonPosts['email'],
                'OrderId' => 'הוספת ידנית_'.$user['username'].'_'.date("d.m.Y_H:i:s"),
                'OrderName' => $OrdersName[$jsonPosts['OrderId']]['name'],
                'Comments' => $jsonPosts['comments'],
                'CustomerIp' => '',
                'TotalPrice' => $OrdersName[$jsonPosts['OrderId']]['value'],
                'ProductId' => '',
                'address' => $jsonPosts['address'],
                'city' => $jsonPosts['city'],
                'tz' => $jsonPosts['tz']
            );
            
            
            $this->db->select('id');
            $this->db->from('leadsLandpage');
            $this->db->where('phone',$data['Phone']);
            $this->db->where('email',$data['Email']);
            $result = $this->db->get();
            $leadExiste = $result->row_array();

            $response = '';
            
            if(empty($leadExiste)) {
                
                $insertContactTrumot = $this->insertContact($data); // Client Lead Trumot
                $addLeadtoDb = $this->addLeadtoDb($data);

                if( !empty($addLeadtoDb) ) {
                    $sendLoginToUser = $this->sendLoginToUser($addLeadtoDb); // Mail
                } else $sendLoginToUser = 'falseDb';

                $response = array (
                    'insertContactTrumot' => isset($insertContactTrumot) ?  $insertContactTrumot : 'false',
                    'addLeadtoDb' => isset($addLeadtoDb) ?  $addLeadtoDb : 'false',
                    'sendLoginToUser' => isset($sendLoginToUser) ?  'sent' : 'false2'
                );
                
                
            }

            else {
                
                $message = 'קיים במערכת לקוח עם אותו מייל וטלפון.'.' ';
                $message .= 'להוספת מנוי ללקוח, יש להיכנס לעריכת לקוח.';
                
                $output['error'] = $message;
                //$addBuysDb = $this->addBuysDb($data,$leadExiste['id']);
                //$response = 'lead exist'.' - InsertBuy: '.$addBuysDb;
            }
            
            
            $output['response'] = $response;
            
        }
        
        else {
            $output = $checkUserCredentials;
        }
        
        
        
        $this->data = $output;
         
        return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($this->data));
    }
    
    
    
    
    
    public function sendLoginToUser($userId = FALSE) {
        
        //echo $userId;
        //$userId = 45;
        
        $this->db->select('*');
        $this->db->from('leadsLandpage');
        $this->db->where('id',$userId);
        $result = $this->db->get();
        //$output = $allDataGet->result_array();
        
        $userDataDb = $result->row_array();
        
        if(!empty($userDataDb)) {
            $mail = $this->makeMail($userDataDb);
            
        } else $mail = 'noMail';
        
        return $mail;
        
    }
    
    
    public function addLeadtoDb($data = FALSE) {
        
        $this->load->model('OtzarHaretz');
        
        $username = $this->OtzarHaretz->CheckUsernameClientExist($data['Phone']);
        
        $this->load->helper('string');
        $random = random_string('numeric', 4);
        
        //otzar
        
        $params = array(
            'created_at' => date("Y-m-d H:i:s"),
            'status' => 1,
            'firstName' => $data['FirstName'],
            'lastName' => $data['LastName'],
            'phone' => $data['Phone'],
            'email' => $data['Email'],
            'user' => $username,
            'password' => md5($random),
            'OrderId' => $data['OrderId'],
            'OrderName' => $data['OrderName'],
            'Comments' => $data['Comments'],
            'CustomerIp' => $data['CustomerIp'],
            'TotalPrice' => $data['TotalPrice'],
            'ProductId' => $data['ProductId'],
            'address' => $data['address'],
            'city' => $data['city'],
            'tz' => $data['tz']
        );

        $insert_id = $this->msite->insert_object('leadsLandpage', $params);
        return $insert_id;
    }
    
    public function addBuysDb($data = FALSE,$idUser = FALSE) {
        
        $params = array(
            'created_at' => date("Y-m-d H:i:s"),
            'userId' => $idUser,
            'OrderId' => $data['OrderId'],
            'OrderName' => $data['OrderName'],
            'Comments' => $data['Comments'],
            'CustomerIp' => $data['CustomerIp'],
            'TotalPrice' => $data['TotalPrice'],
            'ProductId' => $data['ProductId']
        );

        $insert_id = $this->msite->insert_object('buys', $params);
        return $insert_id;
    }
    


    public function insertContact($data = FALSE) {
        
        //https://otzarhaaretz.wdev.co.il/api/trumot/insertContact/
            
        //$this->_loaderWS($param);

        $companyHash = 'vAjFSlAn2cEzToNOzwmR9nfsyK0aAXkGGUC7c822Eo3RmIkyjF410J1T/9BKP46xnp9ucGMQs6asQWtfXttYOQeSPn78nZ8RucFThm67RXf70HhcVtSGPhJOPtPljzWQ';
        
        $values = array();

        
        
        
        if($this->input->get('testSebas')==1) {
            
            $hazmana = "מס' הזמנה: 1 | ";
            $hazmana .= "עלות שובר: 2";
            $commentsHazmana = ' | 3';
            
            $Remark = $hazmana.' - '.$commentsHazmana;
            
            $values = array(
                'FirstName' => 'בדיקה',
                'LastName' => "סבס",
                'GroupId' => '1113007582',
                'Phone' => '0546666662',
                'Email' => '<EMAIL>'
            );
        } else {
            
            $hazmana = "מס' הזמנה: ".$data['OrderId'].' | ';
            $hazmana .= "עלות שובר: ".$data['TotalPrice'];
            $commentsHazmana = !empty($data['Comments']) ? ' | '.$data['Comments'] : '';

            $Remark = $hazmana.$commentsHazmana;
        
            $values = array(

                'FirstName' => $data['FirstName'],
                'LastName' => $data['LastName'],
                'Email' => $data['Email'],
                'GroupId' => $data['GroupId'],
                'Phone' => $data['Phone']
            );
            
        }
        
        $Address = array (
            "Country" => "",
            "City" => $data['city'],
            "Street" => $data['address'],
            "StreetNumber" => "",//מספר רחוב
            "Mikud" => "",//מיקוד
            "Pob" => ""//תיבת דואר
        );
        
        $CallOptions = array(
            "Email" => $values['Email'],//אימייל
            "HomePhone" => $values['Phone'],//טלפון בבית
            "MobilePhone" => $values['Phone'],//טלפון נייד
            "Fax" => ""//פקס
        );
        
        $CreditCard = array (
            "CreditNumber" => "",//מספר כרטיס
            "ExpiryDateMonth" => '',//תוקף הכרטיס - חודש
            "ExpiryDateYear" => '', //תוקף הכרטיס - שנה
            "Cvv" => "",//שלוש ספרות בגב הכרטיס
            "Mz" => ""//מספר זהות של בעל הכרטיס
        );
        
        $PartnerDetails = array (
            "FirstName" => "", 
            "Email" => "", // אימייל של בן הזוג
            "MzNumber" => $data['tz'] // מספר זהות של בן הזוג
        );
        

        
        $data = array(
            'CompanyHash' => $companyHash,
            'MzNumber' =>  '', //מספר זהות
            "FirstName" => $values['FirstName'],
            "LastName" => $values['LastName'],
            "GroupId" => $values['GroupId'], //מספר הקבוצה אליו יכנס איש הקשר
            "Remark" => $Remark,
            "Address" => $Address,
            "CallOptions" => $CallOptions,
            "CreditCard" => $CreditCard,
            "PartnerDetails" => $PartnerDetails
        );
        
        $data_string = json_encode($data);
        $url = 'https://www.trumot.net/Api/InsertContact/';
        
        
        //header('Content-Type: text/html; charset=windows-1255');
        
        $sendCurlResponse = $this->sendCurl($data_string,$url);
        $sendCurlResponse = stripslashes(str_replace('"', "", $sendCurlResponse));
        $sendCurlResponse = str_replace('{', "", $sendCurlResponse);
        $sendCurlResponse = str_replace('}', "", $sendCurlResponse);
        
        //echo $sendCurlResponse;
        
        $arrayResponse = explode(",",$sendCurlResponse);
        //print_r($arrayResponse);
        
        if(isset($arrayResponse[0]) && $arrayResponse[0] == 'Result:-1') {
            $return = "ERROR -1";
        }
        
        else if(isset($arrayResponse[0]) && $arrayResponse[0] != 'Result:-1') {
            $return = "OK: ".$arrayResponse[0];
        }
        
        else {
            $return = "ERROR NO RESPONSE";
        }
        
        return $return;
        
    }
    
    
    public function makeMail($data= false) {
        
        $html_body = '';
        
        
        //START
            
        $this->load->helper('string');
        $random = random_string('numeric', 4);

        $newData = array (
            'password' => md5($random),
            'smsSent' => 1
        );

        $this->db->where('id', $data['id']);
        $update = $this->db->update('leadsLandpage', $newData);


        $url = 'https://bit.ly/2WJ5Ihk';
        $phone = $data['phone'];

        $message = 'שלום '.$data['firstName'].' '.$data['lastName'].'. ';
        $message .= 'להלן פרטי הגישה שלך למערכת מימוש השוברים של אוצר הארץ, להתחברות לחצו כאן >> ';
        $message .= $url;

        $message .= ' | '."שם משתמש: ".$data['user'].' | ';
        $message .="סיסמה: ".$random;

        $message .= ' | '.'למרכז ההדרכה>> '.'https://bit.ly/38B9WKc';

        $smsResponse = $this->OtzarHaretz->sendSMS($phone, $message, $from = 'OtzarHaretz', $param = FALSE);
        $smsResponse = json_decode($smsResponse, TRUE);

        //email email !!!! ----------------------------------------------------

        $data['textTitle'] = 'שלום '.$data['firstName'].' '.$data['lastName'];
        $data['text'] = "ברוכים הבאים לאוצר הארץ שמיטה לכתחילה".'<br/><br/>';

        $data['textR'] = 'כעת, יש באפשרותכם לממש את השוברים באחת החנויות של אוצר הארץ, ולהנות מתוצרת אוצר הארץ שמיטה לכתחילה.'.'<br/>';
        $data['textR'] .= 'לצורך מימוש השוברים עבור פירות וירקות המהודרים בכל שנת השמיטה בקלות ובנוחות, פיתחנו מערכת יחודית, במאמץ רב ומחשבה מרובה.'.'<br/><br/>';

        $data['textR'] .= 'בברכת שנה טובה לכל בית ישראל.'.'<br/><br/>';

        $data['textC'] = 'להלן פרטי הגישה האישיים שלכם למערכת >> '.'<br/><br/>';

        $data['urlApp'] = $url;

        $data['textD'] = '<br/><br/>'.'לנוחיותכם, קישור למרכז ההדרכה של המערכת >>'.'<br/>';
        $aUrl = 'https://bit.ly/38B9WKc';

        $data['textD'] .= '<a href="'.$aUrl.'" >'.$aUrl.'</a>'.'<br/><br/>';

        $data['password'] = $random;

        $html_body = $this->bodyMail($data);
        $to_emails = $data['email'];
        $subject = $data['firstName'].' '.''.'פרטי הגישה שלך למערכת אוצר הארץ';

        $sendMail = $this->send_emailPHPMailer($to_emails, $subject,$html_body);
        
        if(false) {
        
            $params['name'] = $data['firstName'].' '.$data['lastName'];
            $params['user'] = $data['user'];
            $params['password'] = $data['password'];
            $params['text'] = "תודה שהצטרפתם ל'אוצר הארץ'!".'<br/><br/>';
            $params['text2'] = "אנו ב'אוצר הארץ' שמנו לעצמנו למטרה לחבר את הציבור לקיום השמיטה בארץ ישראל, לשווק פירות מהודרים תוך חיזוק החקלאות היהודית בארץ, להפיץ קדושת שביעית לכל בית בישראל ולדאוג לקיום שמיטה כהלכתה במדינת ישראל.";
            $params['text2'] .= "<br/><br/>";
            $params['text2'] .= "בהצטרפותכם ל'אוצר הארץ' אתם לוקחים חלק בחיזוק שמירת השמיטה בארץ, והופכים לשותפים אמיתיים לחקלאים הרבים הפועלים לגדל פירות קדושים בקדושת שביעית באופן המהודר ביותר.";
            $params['text2'] .= "<br/><br/>";
            $params['text2'] .= "דמי המינוי ששילמתם מופקדים בחשבון 'אוצר הארץ' ובכל חודש עומד לזכותכם סכום קצוב בו תוכלו לרכוש פירות וירקות בחנויות אוצר הארץ. כך, בסיכום השנה שווי דמי המינוי חוזרים אליכם בחזרה במלואם. ניתן להגדיל את דמי המינוי בהתאם לצריכה הצפויה במהלך השנה, וכך להשתמש במינוי לרכישה גדולה יותר של פירות וירקות במהלך השמיטה.";
            $params['text2'] .= "<br/><br/>";
            $params['text2'] .= "לשאלות וברורים התקשרו לטלפון"."<br/>";
            $params['text2'] .= "<a href='tel:*9273' style='color: black;font-size: 18px;' ><strong>9273*</strong></a>";
            $html_body = $this->bodyMailNoUser($params);
            $to_emails = $data['email'];
            $subject = 'אוצר הארץ';
            $sendMail = $this->send_email($to_emails, $subject,$html_body);
            
        }
        
        

        
        return $html_body;
        
    }
    
    
    
    private function bodyMailNoUser($params) {
        
         //table lead style
        
        $td_style_title="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 22px;
                  padding: 20px 0 5px;color:#009eb3;";  
        
        $td_style_1="background-color: rgb(235, 235, 235); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;";  

        $td_style_2="background-color: rgb(213, 213, 213); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;padding:10px 0;";
        
        $td_style_p ="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 14px;
                  padding:0px 0;color: #100f15;"; 
        
        $td_style_p2 ="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:regular;text-align: right;font-size: 14px;
                  padding:0px 20px;color: #100f15;"; 
        
        $imgTop = base_url().IMG.'mailImg/top.jpg?v='.VERSION;
        $imgDown = base_url().IMG.'mailImg/down-noEnter.jpg?v='.VERSION;
        $logo = base_url().IMG.'mailImg/logo.jpg?v='.VERSION;
        
        $tableData = "<table style='background: #f3f3f3;margin:0;padding:0;' align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' >";
        
        $tableData .= "<tbody>";
        $tableData .= "<tr><td colspan=2 style='$td_style_title'>שלום {$params['name']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['text']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_p2'>{$params['text2']}</td></tr>";
        
        
        $tableData .= "</tbody>";
        $tableData .="</table>";
       

        $body_mail= "
        <div style='text-align: center;text-direction:rtl; font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;'>
        <br/><br/>";
        
        $body_mail .= "<table align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' style='width: 345px;'>
        <tbody>";
        
        $body_mail .= "<tr><td style='padding: 0px;' border='0' cellpadding='0' cellspacing='0'><img style='display: inherit;' src='$imgTop' border='0'></td></tr>";
        $body_mail .= "<tr><td style='padding: 0px 10px' >$tableData</td></tr>";
                
        $body_mail .= "<tr>
                <td style='text-align:center'>
                  <img style='display: inherit;' src='$imgDown' border='0'>
                </td></tr>";
        
        $body_mail .= "<tr>
                <td style='text-align:center'>
                  <a style='padding: 0;' href='https://waveproject.co.il/'>
                  <img style='display: inherit;' src='$logo' border='0'>
                  </a>
                </td></tr>";
                
                
                
        $body_mail .= "</tbody></table><br/><br/></div>";
        
        return $body_mail;  
    }
    
    
    
    
    
    
    
    
    private function bodyMail($params) {
        
         //table lead style
        
        
        $td_style_title="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 22px;
                  padding: 20px 0 5px;color:#009eb3;";  
        
        $td_style_1="background-color: rgb(235, 235, 235); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;";  

        $td_style_2="background-color: rgb(213, 213, 213); width: 173px; text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;
                  border-bottom: 2px solid #f3f3f3;padding:10px 0;";
        
        $td_style_p ="text-align: center;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:bold;font-size: 14px;
                  padding:0px 0;color: #100f15;"; 
        
        $td_style_pR ="text-align: right;direction:rtl;
                  font-family:Arial, Helvetica, sans-serif;font-weight:regular;font-size: 14px;
                  padding:0px 20px;color: #100f15;"; 
        
        $imgTop = base_url().IMG.'mailImg/top.jpg?v='.VERSION;
        $imgDown = base_url().IMG.'mailImg/down-noEnter.jpg?v='.VERSION;
        $logo = base_url().IMG.'mailImg/logo.jpg?v='.VERSION;
        
        $tableData = "<table style='background: #f3f3f3;margin:0;padding:0;' align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' >";
        
        $tableData .= "<tbody>";
        $tableData .= "<tr><td colspan=2 style='$td_style_title'>{$params['textTitle']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['text']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_pR'>{$params['textR']}</td></tr>";
        
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['textC']}</td></tr>";
        
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['urlApp']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>קישור</span>
                </td>
            </tr>";
        
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['user']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>שם משתמש</span>
                </td>
            </tr>";
                
        $tableData .= "<tr>
                <td style='{$td_style_1}'>
                    <span style='color:black;'>{$params['password']}</span>
                </td>

                <td style='{$td_style_2}'>
                    <span style='color:black;'>סיסמה</span>
                </td>
            </tr>";
                
        $tableData .= "<tr><td colspan=2 style='$td_style_p'>{$params['textD']}</td></tr>";
                
        $tableData .= "</tbody>";
        $tableData .="</table>";
       

        $body_mail= "
        <div style='text-align: center;text-direction:rtl; font-family:Arial, Helvetica, sans-serif;font-weight:normal;font-size: 12px;'>
        <br/><br/>";
        
        $body_mail .= "<table align='center' dir='LTR'; border='0' cellpadding='0' cellspacing='0' style='width: 345px;'>
        <tbody>";
        
        $body_mail .= "<tr><td style='padding: 0px;' border='0' cellpadding='0' cellspacing='0'><a style='padding: 0;margin:0;display:block;' href='https://site.otzar-haretz.co.il'><img style='display: inherit;' src='$imgTop' border='0'></a></td></tr>";
        $body_mail .= "<tr><td style='padding: 0px 10px' >$tableData</td></tr>";
                
        $body_mail .= "<tr>
                <td style='text-align:center'>
                  <a style='padding: 0;' href='https://waveproject.co.il/'>
                  <img style='display: inherit;' src='$imgDown' border='0'>
                  </a>
                </td></tr>";
        
        $body_mail .= "<tr>
            <td style='text-align:center'>
              <a style='padding: 0;' href='https://waveproject.co.il/'>
              <img style='display: inherit;' src='$logo' border='0'>
              </a>
            </td></tr>";
                
                
                
        $body_mail .= "</tbody></table><br/><br/></div>";
        
        return $body_mail;  
    }
    
    
    
    public function send_emailPHPMailer($to, $subject,$html_body) {
        
        // Load PHPMailer library
        $this->load->library('phpmailer_lib');
        
        // PHPMailer object
        $mail = $this->phpmailer_lib->load();
        
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );
        
        
        $mail->isSMTP(); 
        $mail->SMTPDebug = 2;                                     
        $mail->Host = 'in-v3.mailjet.com'; 
        $mail->SMTPAuth = true;                              
        $mail->Username = 'fe313beca5ed02d82af856c9a94e0e6b';                 
        $mail->Password = '53edd86632c33b0ef4ceaae3eeb1dfe5';                           
        $mail->SMTPSecure = 'ssl';                            
        $mail->Port = 465;   

        $mail->CharSet = 'UTF-8';                            
        $mail->setFrom('<EMAIL>', 'אוצר הארץ');
        $mail->addAddress($to); 
        
        //$mail->addAddress('<EMAIL>'); 
        //$mail->addReplyTo('<EMAIL>', 'no-reply');
        
        $mail->isHTML(true);                                  
        $mail->Subject = $subject;
        $mail->Body = $html_body;
        $mail->send();
        
        
    }
    
    public function send_email($to_emails, $subject,$html_body) {
        
        //https://codeigniter.com/userguide3/libraries/email.html
        
//        $to_emails = $this->input->post('to_emails');
//        $from = $this->input->post('from_email');
//        $subject = $this->input->post('subject');
//        $html_body = $this->input->post('html_body');
        
        $pass = 'Z&.~0.6I{xOO';
        //<EMAIL>
        
        $this->load->library("email");
        
        $config = Array(
            'protocol' => 'mail',
            'smtp_host' => 'in-v3.mailjet.com',
            'smtp_port' => 587,
            'smtp_user' => 'fe313beca5ed02d82af856c9a94e0e6b',
            'smtp_pass' => '53edd86632c33b0ef4ceaae3eeb1dfe5',
            'mailtype'  => 'html', 
            'charset'   => 'UTF-8'
        );
        
        $this->email->initialize($config);
        $this->email->from('<EMAIL>', 'אוצר הארץ');
        $this->email->to($to_emails);
        

        $this->email->subject($subject);
        $this->email->message($html_body);
        $response['success'] = $this->email->send();
        
        
//        $this->output->set_status_header('200');
//            
//        return $this->output
//                ->set_content_type('application/json')
//                ->set_output(json_encode($response));
    }
    
    
    public function sendCurl($data_string,$url) {
        
        
        $ch = curl_init( $url );
        # Setup request to send json via POST.
        //$payload = json_encode( array( "customer"=> $data ) );
        curl_setopt( $ch, CURLOPT_POSTFIELDS, $data_string );
        curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
        # Return response instead of printing.
        curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
        # Send request.
        $result = curl_exec($ch);
        curl_close($ch);
        # Print response.
        
        return $result;
        
    }
    
    
}





