import React, { useState, useEffect, useRef } from "react";
import loader from "../../img/preLoader.gif";
import { Form, Button } from "react-bootstrap";
import { toast } from "react-toastify";
import { RestUrls } from "../../Components/-Helpers-/config";
import Spinner from 'react-bootstrap/Spinner';
// ייבוא DatePicker
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { registerLocale, setDefaultLocale } from "react-datepicker";
import he from 'date-fns/locale/he';

import getDataFromApi from "../../Components/-Helpers-/api/getDataFromApi";
import CustomFloatInput from "../../Components/-Helpers-/forms/CustomFloatInput";

import man from "../../img/sherut-leumi/svg/man.svg";
import woman from "../../img/sherut-leumi/svg/woman.svg";

import { animateScroll as scroll } from "react-scroll";
import { NavLink } from "react-router-dom";

import ModalDefaul from "../../Components/-Helpers-/ModalDefaul";
import Axios from "axios";
import Swal from "sweetalert2";

// API Configuration
const API_URL = process.env.REACT_APP_ENVIRONMENT === 'dev' 
  ? process.env.REACT_APP_API_BASE_URL_DEV 
  : 'https://sherut-leumi.wdev.co.il/api/register/newRegister';

// Utility functions
const formatPhoneNumber = (value) => {
  // Remove all non-digit characters
  const numbers = value.replace(/\D/g, '');
  
  // Don't add hyphen if less than 3 digits
  if (numbers.length < 3) return numbers;
  
  // Add hyphen after area code
  if (numbers.length <= 10) {
    return `${numbers.slice(0, 3)}-${numbers.slice(3)}`;
  }
  
  // Limit to 10 digits
  return `${numbers.slice(0, 3)}-${numbers.slice(3, 10)}`;
};

const get17YearsAgo = () => {
  const date = new Date();
  date.setFullYear(date.getFullYear() - 17);
  return date;
};

const formatErrorMessage = (error) => {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.response?.data?.message) return error.response.data.message;
  return 'אירעה שגיאה. אנא נסו שוב.';
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  // Ensure we're handling the date in local time
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`; // Keep ISO format for input value
};

const formatDisplayDate = (date) => {
  if (!date) return '';
  const [year, month, day] = date.split('-');
  return `${day}/${month}/${year}`;
};

const calculateAge = (birthDate) => {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

const getDateLimits = () => {
  const today = new Date();
  
  // Max date is today minus 17 years
  const maxDate = new Date();
  maxDate.setFullYear(today.getFullYear() - 17);
  
  // Min date is today minus 22 years
  const minDate = new Date();
  minDate.setFullYear(today.getFullYear() - 22);
  
  return {
    min: formatDate(minDate),
    max: formatDate(maxDate)
  };
};

// Error boundary component
class FormErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Form Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-container">
          <h3>אירעה שגיאה</h3>
          <p>{formatErrorMessage(this.state.error)}</p>
          <Button
            onClick={() => {
              this.setState({ hasError: false, error: null });
              this.props.onReset?.();
            }}
          >
            נסו שוב
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Form Validation Helper
const validateFormData = (formData, formState) => {
  const fieldNamesHebrew = {
    FirstName: 'שם פרטי',
    LastName: 'שם משפחה',
    IDNO: 'תעודת זהות',
    Mobile: 'טלפון נייד',
    BirthDate: 'תאריך לידה',
    CityCode: 'עיר/ישוב',
    Email: 'דואר אלקטרוני',
    Category: 'מסלול התנדבות',
    PrvSchool: 'בית ספר',
    YearYad: 'שנת שירות'
  };

  const errors = {};
  const missingFields = [];

  // Required fields validation
  const requiredFields = ['FirstName', 'LastName', 'IDNO', 'Mobile', 'BirthDate', 'CityCode', 'Email', 'Category'];
  
  // Add conditional required fields
  if (formData.Category && [1, 2, 3].includes(Number(formData.Category))) {
    requiredFields.push('PrvSchool', 'YearYad');
  }

  // Validate each field
  requiredFields.forEach(field => {
    const value = formData[field];
    const isEmpty = !value || value.toString().trim() === '' || 
                   (field === 'CityCode' && value === 0) ||
                   (field === 'Category' && value === 0);

    if (isEmpty) {
      errors[field] = `שדה חובה חסר: ${fieldNamesHebrew[field]}`;
      missingFields.push(field);
    }
  });

  // Special validations
  if (formData.IDNO && !/^\d{9}$/.test(formData.IDNO.toString().replace(/\D/g, ''))) {
    errors.IDNO = 'מספר תעודת זהות אינו תקין. נדרשים 9 ספרות';
  }

  if (formData.Email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.Email.trim())) {
    errors.Email = 'כתובת דואר אלקטרוני אינה תקינה';
  }

  if (formData.Mobile) {
    const mobilePattern = /^\d{3}-\d{7}$/;
    if (!mobilePattern.test(formData.Mobile)) {
      errors.Mobile = 'מספר טלפון נייד חייב להיות בפורמט: XXX-XXXXXXX';
    }
  }

  // Birthdate validation
  if (formData.BirthDate) {
    const birthDate = new Date(formData.BirthDate);
    if (isNaN(birthDate.getTime())) {
      errors.BirthDate = 'תאריך לידה אינו תקין';
    } else {
      const age = new Date().getFullYear() - birthDate.getFullYear();
      if (age < 18 || age > 22) {
        errors.BirthDate = 'גיל המתנדב חייב להיות בין 18 ל-22';
      }
    }
  }

  // Form state validations
  if (!formState.rishum) errors.rishum = 'יש לאשר את תנאי הרישום';
  if (!formState.takanon) errors.takanon = 'יש לאשר את התקנון';
  if (!formState.sex) errors.sex = 'יש לבחור מין';

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    missingFields: missingFields.map(field => fieldNamesHebrew[field] || field)
  };
};

// Form validation hooks
const useFormValidation = (formData, formState) => {
  const validateField = (name, value) => {
    switch (name) {
      case 'IDNO':
        return value.length === 9 ? '' : 'תעודת זהות חייבת להכיל 9 ספרות';
      case 'Email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? '' : 'כתובת אימייל לא תקינה';
      case 'Mobile':
        const cleanMobile = value.replace(/\D/g, '');
        return cleanMobile.length === 10 ? '' : 'מספר טלפון לא תקין';
      case 'BirthDate':
        const birthDate = new Date(value);
        const minDate = get17YearsAgo();
        return birthDate <= minDate ? '' : 'גיל מינימלי נדרש הוא 17 שנים';
      default:
        return value ? '' : 'שדה חובה';
    }
  };

  const validateForm = () => {
    const errors = {};
    let isValid = true;

    // Validate all fields
    Object.entries(formData).forEach(([key, value]) => {
      if (key !== 'imageUrl') { // Skip optional fields
        const error = validateField(key, value);
        if (error) {
          errors[key] = error;
          isValid = false;
        }
      }
    });

    // Validate checkboxes
    if (!formState.rishum) {
      errors.rishum = 'נא לאשר את הרישום למאגר';
      isValid = false;
    }
    if (!formState.takanon) {
      errors.takanon = 'נא לאשר את התקנון';
      isValid = false;
    }

    return { isValid, errors };
  };

  return { validateField, validateForm };
};

// Custom hook for form state management
const useFormState = (initialData) => {
  const [formData, setFormData] = useState(initialData);
  const [formState, setFormState] = useState({
    rishum: false,
    takanon: false,
    isLoading: false,
    errors: {
      FirstNameError: null,
      LastNameError: null
    },
    showModal: false,
    modalContent: null,
    sex: '',
    checkInputsPage: false
  });

  const updateFormData = (name, value) => {
    console.log(`Updating ${name} with value:`, value);
    
    // Handle direct property updates
    if (typeof name === 'string') {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    } 
    // Handle object updates (for compatibility with older code)
    else if (typeof name === 'object') {
      const key = Object.keys(name)[0];
      const val = name[key];
      console.log(`Updating ${key} with value:`, val);
      
      setFormData(prev => ({
        ...prev,
        [key]: val
      }));
    }

    // Clear errors for the updated field
    const fieldName = typeof name === 'string' ? name : Object.keys(name)[0];
    
    if (formState.errors[fieldName]) {
      setFormState(prev => ({
        ...prev,
        errors: {
          ...prev.errors,
          [fieldName]: null
        }
      }));
    }
  };

  const resetForm = () => {
    setFormData(initialData);
    setFormState(prev => ({
      ...prev,
      rishum: false,
      takanon: false,
      errors: {
        FirstNameError: null,
        LastNameError: null
      },
      showModal: false,
      modalContent: null,
      sex: '',
      checkInputsPage: false
    }));
  };

  return {
    formData,
    formState,
    setFormState,
    updateFormData,
    resetForm
  };
};

const SmallForm = (props) => {
  const environment = process.env.REACT_APP_ENVIRONMENT;
  const initialFormData = {
    FirstName: '',
    LastName: '',
    IDNO: '',
    Mobile: '',
    BirthDate: '',
    CityCode: 0,
    Email: '',
    Category: 0,
    PrvSchool: '',
    YearYad: '',
    sex: '',
    imageUrl: ''
  };

  const {
    formData,
    formState,
    setFormState,
    updateFormData,
    resetForm
  } = useFormState(initialFormData);

  const { validateField, validateForm } = useFormValidation(formData, formState);

  const [cities, setCities] = useState([]);
  const [schools, setSchools] = useState([]);
  const [categories, setCategories] = useState([]);
  const formRef = useRef(null);

  // Enhanced error handling for API calls
  const fetchInitialData = async () => {
    try {
      setFormState(prev => ({ ...prev, isLoading: true }));
      
      const [citiesResponse, categoriesResponse] = await Promise.all([
        fetch('/api/cities'),
        fetch('/api/categories')
      ]);

      if (!citiesResponse.ok || !categoriesResponse.ok) {
        throw new Error('Failed to fetch initial data');
      }

      const [citiesData, categoriesData] = await Promise.all([
        citiesResponse.json(),
        categoriesResponse.json()
      ]);

      setCities(citiesData);
      setCategories(categoriesData);

    } catch (error) {
      console.error('Error fetching initial data:', error);
      toast.error('שגיאה בטעינת נתונים. אנא רעננו את הדף.');
    } finally {
      setFormState(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setFormState(prev => ({
        ...prev,
        [name]: checked
      }));
    } else if (name === 'Mobile') {
      // Format phone number as user types
      const formattedNumber = formatPhoneNumber(value);
      updateFormData(name, formattedNumber);
    } else if (name === 'FirstName' || name === 'LastName') {
      // Filter out non-Hebrew characters
      const hebrewOnly = value.replace(/[^\u0590-\u05FF\s]/g, '');
      updateFormData(name, hebrewOnly);

      // Validate and set/clear error for FirstName
      if (name === 'FirstName') {
        if (!/^[\u0590-\u05FF\s]*$/.test(hebrewOnly) && hebrewOnly !== '') {
          setFormState(prev => ({
            ...prev,
            errors: { ...prev.errors, FirstNameError: 'יש להזין רק אותיות בעברית' }
          }));
        } else {
          setFormState(prev => ({
            ...prev,
            errors: { ...prev.errors, FirstNameError: null }
          }));
        }
      }

      // Validate and set/clear error for LastName
      if (name === 'LastName') {
        if (!/^[\u0590-\u05FF\s]*$/.test(hebrewOnly) && hebrewOnly !== '') {
          setFormState(prev => ({
            ...prev,
            errors: { ...prev.errors, LastNameError: 'יש להזין רק אותיות בעברית' }
          }));
        } else {
          setFormState(prev => ({
            ...prev,
            errors: { ...prev.errors, LastNameError: null }
          }));
        }
      }
    } else {
      updateFormData(name, value);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    sendForm();
  };

  const sendForm = () => {
    // Set loading state
    setFormState(prev => ({...prev, loading: true}));
    
    console.log("Sending form data:", formData);
    console.log("Form state:", formState);
    
    // Prepare data for API
    const sendObj = {
      FirstName: formData.FirstName || "",
      LastName: formData.LastName || "",
      IDNO: formData.IDNO ? formData.IDNO.toString().replace(/\D/g, '') : "",
      Mobile: formData.Mobile ? formData.Mobile.replace(/-/g, '') : "",
      BirthDate: formData.BirthDate || "",
      CityCode: formData.CityCode || "",
      Email: formData.Email || "",
      Category: formData.Category || "",
      PrvSchool: formData.PrvSchool || "",
      YearYad: formData.YearYad || "",
      sex: formState.sex || ""
    };
    
    // Send to API
    sendtoAdmin(
      "newRegister",
      "register", 
      sendObj,
      "responseNewRegister",
      "register"
    );
  };

  const sendtoAdmin = async (url, controller, objectToSend, setStateName = "data", auth = "all") => {
    try {
      console.log(`Sending request to ${url} with data:`, objectToSend);
      
      const getData = await getDataFromApi(url, objectToSend, controller, auth);
      console.log('Registration response:', getData);
      
      // Check for successful registration based on responseClient.Result
      if (getData?.responseClient?.Result === "Success") {
        // Store user data if available
        if (getData.volunteer) {
          localStorage.setItem("userData", JSON.stringify({
            ...getData.volunteer,
            SessionKey: getData.SessionKey
          }));
        }

        // Show success message
        toast.success('ההרשמה הושלמה בהצלחה!', {
          position: "top-center",
          autoClose: 2000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });

        // Redirect to login page after success message
        setTimeout(() => {
          window.open("/login", "_self");
        }, 2000);

        setFormState(prev => ({
          ...prev,
          loading: false,
          rishum: true,
          takanon: true,
          btnSendClass: "success"
        }));
      } else {
        // Handle errors
        const errorMessage = getData?.responseClient?.ErrorMessage || getData?.error || 'תקלה בתהליך ההרשמה. אנא נסו שוב.';
        
        console.error('Registration error:', errorMessage);
        
        setFormState(prev => ({
          ...prev,
          loading: false,
          showModal: true,
          modalContent: {
            title: "שגיאה בהרשמה",
            message: errorMessage,
            type: "error"
          }
        }));
      }
    } catch (error) {
      console.error("API Error:", error);
      
      setFormState(prev => ({
        ...prev, 
        loading: false,
        showModal: true,
        modalContent: {
          title: "שגיאה בהרשמה",
          message: 'אירעה שגיאה בתקשורת עם השרת. אנא נסו שוב מאוחר יותר.',
          type: "error"
        }
      }));
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setFormState(prev => ({
      ...prev,
      showModal: false,
      modalContent: null
    }));
  };

  const kidomot = [
    { id: "050", value: "050" },
    { id: "051", value: "051" },
    { id: "052", value: "052" },
    { id: "053", value: "053" },
    { id: "054", value: "054" },
    { id: "055", value: "055" },
    { id: "056", value: "056" },
    { id: "057", value: "057" },
    { id: "058", value: "058" },
    { id: "059", value: "059" },
    { id: "072", value: "072" },
    { id: "073", value: "073" },
    { id: "074", value: "074" },
    { id: "076", value: "076" },
    { id: "077", value: "077" },
    { id: "078", value: "078" }
  ];

  const CategoryOptions = [
    { id: "1", value: 'חילוני' },
    { id: "2", value: "דתי" },
    { id: "3", value: "מיעוטים" }
  ];

  const isMobile = props.siteInfo?.isMobile || false;
  const m_picTop = RestUrls.pagesPictures + "register/m_registerTop.jpg?v=2";
  const bgImage = RestUrls.pagesPictures + "register/bg.jpg?v=2";

  // Fetch years data
  const getDataApi = () => {
    console.log("Fetching years data");
    setFormState(prev => ({...prev, loading: true}));
    
    // Instead of API call, set static years data for immediate use
    const staticYears = [
      { id: "תשפה", value: "שנה זו (תשפ״ה)" },
      { id: "תשפו", value: "שנה הבאה (תשפ״ו)" },
      { id: "תשפז", value: "עוד שנתיים (תשפ״ז)" }
    ];
    
    setFormState(prevState => ({
      ...prevState, 
      loading: false, 
      dataApi: { years: staticYears }
    }));
  };
  
  const sendtoAdmin2 = async (url, controller, objectToSend, setStateName = "data", auth = "all") => {
    try {
      // For debugging, we'll skip the actual API call
      console.log("Years data would be fetched from:", url);
      
      // Use static data instead
      const staticYears = [
        { id: "תשפה", value: "שנה זו (תשפ״ה)" },
        { id: "תשפו", value: "שנה הבאה (תשפ״ו)" },
        { id: "תשפז", value: "עוד שנתיים (תשפ״ז)" }
      ];
      
      setFormState(prevState => ({
        ...prevState, 
        loading: false, 
        dataApi: { years: staticYears }
      }));
    } catch (error) {
      console.error("API Error when getting years:", error);
      // במקרה של שגיאת API, נגדיר ערכים ברירת מחדל
      setFormState(prevState => ({
        ...prevState, 
        loading: false, 
        dataApi: { 
          years: [
            { id: "תשפה", value: "שנה זו (תשפ״ה)" },
            { id: "תשפו", value: "שנה הבאה (תשפ״ו)" },
            { id: "תשפז", value: "עוד שנתיים (תשפ״ז)" }
          ]
        }
      }));
    }
  };

  // Call getDataApi on component mount
  useEffect(() => {
    if (!formState.dataApi) {
      getDataApi();
    }
  }, [formState.dataApi]);

  // הוספת סגנון CSS לתצוגת תאריכים
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .date-display {
        position: absolute;
        top: 38px;
        right: 40px;
        color: #555;
        font-size: 12px;
        z-index: 10;
      }
      
      /* טיפול בפורמט תאריך עבור דפדפנים תומכי RTL */
      input[type="date"]::-webkit-calendar-picker-indicator {
        margin-left: 0;
        margin-right: -10px;
      }
      
      /* הסתרת ברירת המחדל של הדפדפן */
      input[type="date"]::-webkit-datetime-edit {
        position: relative;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // רישום לוקל עברית
  registerLocale('he', he);

  // Wrap the component with error boundary
  return (
    <FormErrorBoundary onReset={resetForm}>
      <div className="flex min-h-screen w-full items-center justify-center bg-gray-50 px-4 py-6 md:py-12 lg:py-16 rtl">
        <div className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-3xl bg-white shadow-lg rounded-xl overflow-hidden transition-all animate__animated animate__fadeIn">
          {/* Loader overlay */}
          {formState.isLoading && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                     <Spinner animation="border" role="status">
        <span className="visually-hidden">Loading...</span>
      </Spinner>

            </div>
          )}

          <div className="flex flex-col lg:flex-row">
            {/* Left side image panel */}
            <div className="w-full lg:w-2/5 h-40 md:h-48 lg:h-auto bg-blue-600 relative overflow-hidden">
              <img 
                className="w-full h-full object-cover" 
                src={bgImage} 
                alt="רקע" 
              />
              {isMobile && (
                <img 
                  className="absolute inset-0 w-full h-full object-cover z-10" 
                  src={m_picTop} 
                  alt="top" 
                />
              )}
            </div>

            {/* Right side form panel */}
            <div className="w-full lg:w-3/5 px-6 sm:px-8 md:px-10 lg:px-8 py-8 md:py-8">
              {/* Header section */}
              <header className="text-center mb-6 lg:mb-8">
                <h1 className="text-2xl sm:text-3xl lg:text-3xl font-bold text-gray-800 mb-2">הרשמה לאתר</h1>
                <p className="text-gray-600 text-sm md:text-base">בכדי להתחיל בתהליך השירות הלאומי עלייך להרשם לאתר</p>
              </header>

              {/* Form fields */}
              <div className="space-y-4 md:space-y-5">
                <form onSubmit={(e) => {
                  e.preventDefault();
                  // בדיקה שכל השדות הנדרשים מלאים לפני שליחה
                  const validation = validateFormData(formData, formState);
                  if (validation.isValid) {
                    handleSubmit(e);
                  } else {
                    // עדכון מצב הטופס להצגת שגיאות
                    setFormState(prev => ({
                      ...prev,
                      checkInputsPage: true,
                      errors: validation.errors
                    }));
                  }
                }}>
                  {/* Name Fields - Row 1 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        שם פרטי <span className="text-red-600">*</span>
                      </label>
                      <input
                        type="text"
                        name="FirstName"
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
                          formState.errors.FirstNameError
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                        placeholder="שם פרטי"
                        value={formData.FirstName || ''}
                        onChange={handleInputChange}
                      />
                      {/* Error message for FirstName */}
                      {formState.errors.FirstNameError && (
                        <p className="mt-1 text-xs text-red-600 px-2 flex items-center">
                          <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </span>
                          {formState.errors.FirstNameError}
                        </p>
                      )}
                      {/* Success indicator for FirstName */}
                      {formData.FirstName && !formState.errors.FirstNameError && (
                        <div className="mt-1 text-xs text-green-600 text-left ltr px-2 flex items-center">
                          <span className="inline-flex items-center justify-center bg-green-100 text-green-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                            </svg>
                          </span>
                          <span>שם תקין</span>
                        </div>
                      )}
                    </div>
                    <div className="relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        שם משפחה <span className="text-red-600">*</span>
                      </label>
                      <input
                        type="text"
                        name="LastName"
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
                          formState.errors.LastNameError
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                        placeholder="שם משפחה"
                        value={formData.LastName || ''}
                        onChange={handleInputChange}
                      />
                      {/* Error message for LastName */}
                      {formState.errors.LastNameError && (
                        <p className="mt-1 text-xs text-red-600 px-2 flex items-center">
                          <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </span>
                          {formState.errors.LastNameError}
                        </p>
                      )}
                      {/* Success indicator for LastName */}
                      {formData.LastName && !formState.errors.LastNameError && (
                        <div className="mt-1 text-xs text-green-600 text-left ltr px-2 flex items-center">
                          <span className="inline-flex items-center justify-center bg-green-100 text-green-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                            </svg>
                          </span>
                          <span>שם תקין</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* ID Field - Row 2 */}
                  <div className="grid grid-cols-1 gap-4">
                    <div className="relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        תעודת זהות <span className="text-red-600">*</span>
                      </label>
                      <input
                        type="text"
                        name="IDNO"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="תעודת זהות (9 ספרות)"
                        value={formData.IDNO || ''}
                        onChange={(e) => updateFormData('IDNO', e.target.value)}
                      />
                    </div>
                  </div>

                  {/* Phone - Row 3 */}
                  <div className="grid grid-cols-1 gap-4">
                    <div className="relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        טלפון נייד
                      </label>
                      <input
                        type="tel"
                        inputMode="numeric"
                        className={`w-full px-3 py-2 border ${
                          formState.errors?.Mobile 
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        } rounded-md shadow-sm focus:outline-none focus:ring-2 ltr text-left`}
                        placeholder="050-1234567"
                        value={formData.Mobile || ''}
                        onChange={handleInputChange}
                        name="Mobile"
                        maxLength={11}
                        dir="ltr"
                      />
                      
                      {/* Success indicator */}
                      {/^\d{3}-\d{7}$/.test(formData.Mobile || '') && (
                        <div className="mt-1 text-xs text-green-600 text-left ltr px-2 flex items-center">
                          <span className="inline-flex items-center justify-center bg-green-100 text-green-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                            </svg>
                          </span>
                          <span>המספר תקין</span>
                        </div>
                      )}
                      
                      {/* Error message */}
                      {formState.errors?.Mobile && (
                        <p className="mt-1 text-xs text-red-600 px-2 flex items-center">
                          <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </span>
                          יש להזין מספר טלפון תקין (050-1234567)
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Birth Date - Row 4 (Standalone) */}
                  <div className="grid grid-cols-1 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        תאריך לידה <span className="text-red-600">*</span>
                      </label>
                      <DatePicker
                        selected={formData.BirthDate ? new Date(formData.BirthDate) : null}
                        onChange={(date) => updateFormData('BirthDate', date ? date.toISOString().split('T')[0] : '')}
                        dateFormat="dd/MM/yyyy"
                        locale="he"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholderText="בחר/י תאריך"
                        showYearDropdown
                        scrollableYearDropdown
                        yearDropdownItemNumber={100}
                        isClearable
                      />
                      <small className="text-muted block text-xs text-gray-500 mt-1">בחר/י תאריך לידה</small>
                    </div>
                  </div>

                  {/* Email - Row 5 (Standalone) */}
                  <div className="grid grid-cols-1 gap-4 mb-4">
                    <div className="relative">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        דוא"ל <span className="text-red-600">*</span>
                      </label>
                      <input
                        type="email"
                        name="Email"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder='<EMAIL>'
                        value={formData.Email || ''}
                        onChange={(e) => updateFormData('Email', e.target.value)}
                      />
                    </div>
                  </div>

                  {/* City and School - Row 6 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        עיר / ישוב <span className="text-red-600">*</span>
                      </label>
                      <CustomFloatInput
                        name="CityCode"
                        dbParams={{
                          function: environment === "dev" ? "getCities4SelectDev" : "getCities4Select",
                          controller: "app",
                          valueField: "id",
                        }}
                        updateValue={(name, value) => updateFormData(name, value)}
                        value={formData.CityCode || ""}
                        placeholder="עיר / ישוב"
                        cssClass={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
                          formState.errors?.CityCode 
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                        zIndexClass="topZindex"
                        validationRules={{ required: true }}
                        typeInput="searchDB"
                      />
                      {formState.errors?.CityCode && (
                        <p className="mt-1 text-xs text-red-600 flex items-center">
                          <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </span>
                          יש לבחור עיר / ישוב
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        בית ספר <span className="text-red-600">*</span>
                      </label>
                      <CustomFloatInput
                        name="PrvSchool"
                        dbParams={{
                          function: environment === "dev" ? "getSchools4SelectDev" : "getSchools4Select",
                          controller: "app",
                          valueField: "id",
                        }}
                        updateValue={(name, value) => updateFormData(name, value)}
                        value={formData.PrvSchool || ""}
                        placeholder="בית ספר"
                        cssClass={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
                          formState.errors?.PrvSchool 
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                        validationRules={{ required: true }}
                        typeInput="searchDB"
                      />
                      {formState.errors?.PrvSchool && (
                        <p className="mt-1 text-xs text-red-600 flex items-center">
                          <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </span>
                          יש לבחור בית ספר
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Gender Selection - Row 7 */}
                  <div className="mt-2">
                    <label className={`block text-sm font-medium ${
                      formState.checkInputsPage && !formState.sex
                        ? "text-red-600"
                        : "text-gray-700"
                    }`}>
                      מין <span className="text-red-600">*</span>
                    </label>

                    <div className={`flex justify-center gap-6 mt-3 ${formState.checkInputsPage && !formState.sex ? "border-2 border-red-300 rounded-lg p-2 bg-red-50" : ""}`}>
                      <button
                        type="Button"
                        onClick={() => {
                          console.log("Selected gender: male (man)");
                          setFormState(prev => ({...prev, sex: "man", checkInputsPage: false}));
                        }}
                        className={`flex flex-col items-center p-3 rounded-lg transition-all ${
                          formState.sex === "man"
                            ? "bg-blue-100 border-2 border-blue-500 scale-105"
                            : "bg-gray-50 border-2 border-gray-200 hover:bg-gray-100"
                        }`}
                      >
                        <img src={man} alt="זכר" className="w-10 h-10 md:w-12 md:h-12 mb-2" />
                        <span className="text-sm font-medium">זכר</span>
                      </button>

                      <button
                        type="Button"
                        onClick={() => {
                          console.log("Selected gender: female (woman)");
                          setFormState(prev => ({...prev, sex: "woman", checkInputsPage: false}));
                        }}
                        className={`flex flex-col items-center p-3 rounded-lg transition-all ${
                          formState.sex === "woman"
                            ? "bg-pink-100 border-2 border-pink-500 scale-105"
                            : "bg-gray-50 border-2 border-gray-200 hover:bg-gray-100"
                        }`}
                      >
                        <img src={woman} alt="נקבה" className="w-10 h-10 md:w-12 md:h-12 mb-2" />
                        <span className="text-sm font-medium">נקבה</span>
                      </button>
                    </div>
                    
                    {formState.checkInputsPage && !formState.sex && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                          </svg>
                        </span>
                        שדה חובה: יש לבחור מין
                      </p>
                    )}
                  </div>

                  {/* Category and Year - Row 8 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        מסלול התנדבות <span className="text-red-600">*</span>
                      </label>
                      <select
                        name="Category"
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
                          formState.errors?.Category 
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                        value={formData.Category || ""}
                        onChange={(e) => updateFormData('Category', e.target.value)}
                      >
                        <option value="" disabled>בחר מסלול התנדבות</option>
                        {CategoryOptions.map(option => (
                          <option key={option.id} value={option.id}>{option.value}</option>
                        ))}
                      </select>
                      {formState.errors?.Category && (
                        <p className="mt-1 text-xs text-red-600 flex items-center">
                          <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </span>
                          {formState.errors.Category}
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        שנת שירות <span className="text-red-600">*</span>
                      </label>
                      <select
                        name="YearYad"
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 ${
                          formState.errors?.YearYad 
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        }`}
                        value={formData.YearYad || ""}
                        onChange={(e) => updateFormData('YearYad', e.target.value)}
                      >
                        <option value="" disabled>בחר שנת שירות</option>
                        {formState.dataApi?.years ? (
                          formState.dataApi.years.map(option => (
                            <option key={option.id} value={option.id}>{option.value}</option>
                          ))
                        ) : (
                          <option value="" disabled>טוען שנים...</option>
                        )}
                      </select>
                      {formState.errors?.YearYad && (
                        <p className="mt-1 text-xs text-red-600 flex items-center">
                          <span className="inline-flex items-center justify-center bg-red-100 text-red-600 rounded-full w-5 h-5 mr-1">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </span>
                          {formState.errors.YearYad}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Checkboxes - Row 9 */}
                  <div className="space-y-3 mt-1">
                    <div className="flex items-start gap-2 rtl">
                      <input
                        type="checkbox"
                        id="takanon"
                        checked={formState.takanon}
                        onChange={() => {
                          console.log("Toggling takanon checkbox:", !formState.takanon);
                          setFormState(prev => ({...prev, takanon: !prev.takanon, checkRadio1: false}));
                        }}
                        className={`mt-1 h-4 w-4 ${formState.checkRadio1 && !formState.takanon ? 'border-red-500' : ''}`}
                      />
                      <label htmlFor="takanon" className="text-sm text-gray-700">
                        <span>קראתי את </span>
                        <a
                          href="https://sherut-leumi.co.il/%D7%AA%D7%A7%D7%A0%D7%95%D7%9F-%D7%90%D7%AA%D7%A8/"
                          target="_blank"
                          rel="noreferrer"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          התקנון
                        </a>
                        <span> אני מאשר/ת את הכתוב</span>
                      </label>
                    </div>
        
                    <div className="flex items-start gap-2 rtl">
                      <input
                        type="checkbox"
                        id="rishum"
                        checked={formState.rishum}
                        onChange={() => {
                          console.log("Toggling rishum checkbox:", !formState.rishum);
                          setFormState(prev => ({...prev, rishum: !prev.rishum, checkRadio2: false}));
                        }}
                        className={`mt-1 h-4 w-4 ${formState.checkRadio2 && !formState.rishum ? 'border-red-500' : ''}`}
                      />
                      <label htmlFor="rishum" className="text-sm text-gray-700">
                        אני מאשר/ת רישום למאגר לקוחות ואני מסכימ/ה לקבל דיוור ללא המילה פרסומות בכותרות
                      </label>
                    </div>
                  </div>

                  {/* Submit Button - Row 10 */}
                  <div className="text-center mt-6">
                    <Button
                      type="submit"
                      disabled={formState.loading}
                      className={`w-full py-3 px-6 text-base rounded-lg text-white font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                        formState.loading ? "opacity-70 cursor-not-allowed" : ""
                      } ${
                        formState.btnSendClass === "success" 
                          ? "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500" 
                          : "bg-red-600 hover:bg-red-700 focus:ring-red-500"
                      }`}
                    >
                      {formState.loading ? (
                        <span className="flex items-center justify-center">
       <Spinner animation="border" role="status">
        <span className="visually-hidden">Loading...</span>
      </Spinner>
     מעבד נתונים...
                        </span>
                      ) : (
                        "לסיום ההרשמה וקבלת סיסמא בSMS"
                      )}
                    </Button>

                    <div className="mt-2 text-xs text-gray-600">
                      אנא ודא שמילאת את כל השדות החובה המסומנים ב-*
                    </div>

                    <div className="mt-4">
                      <NavLink
                        className="text-gray-700 hover:text-blue-600 transition-colors text-sm"
                        role="menuitem"
                        onClick={() => scroll.scrollTo(0)}
                        to="/login"
                      >
                        <span>
                          כבר רשומים? <strong className="text-blue-600">לחצו להתחברות</strong>
                        </span>
                      </NavLink>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        {formState.showModal && (
          <ModalDefaul
            variant={formState.modalContent.type}
            params={{
              title: formState.modalContent.title,
              text: formState.modalContent.message,
            }}
            callBack={handleModalClose}
          />
        )}
      </div>
    </FormErrorBoundary>
  );
};

export default SmallForm;


