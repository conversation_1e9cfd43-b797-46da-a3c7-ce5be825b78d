<?php

class Obj {
    
    static $Langs = array(
        'he' => 'עברית',
        'en' => 'English',
        'ar' => 'عربيه',
    );
    
    static $Field_types = array(
        "short" => "טקסט קצר",
        "long" => "טקסט ארוך",
        "html" => "טקסט HTML",
        "map" => "מפת גוגל",
        "integer" => "מספר שלם",
        "double" => "מספר עשרוני",
        "date" => "תאריך",
        "datetime" => "תאריך ושעה",
        "time" => "שעה",
        "image" => "תמונה",
        "file" => "קובץ",
        "email" => "אימייל",
        "tel" => "טלפון",
        "video" => "סרטון",
        "color" => "צבע",
        "choice" => "בחירה בודדת",
        "multiple" => "בחירה מרובה",
        "multipleTable" => "בחירה מרובה מתוך טבלה",
        "boolean" => "כן/לא",
        //"gallery" => "גלריה",
        "table" => "מתוך טבלה",
        "search" => "חיפוש בטבלה",
    );
    
    private $obj;
    
    public function __construct($obj = NULL) {
        $this->obj = $obj;
    }
    public function merge($arr) {
        $this->obj = array_merge ($this->obj, $arr);
    }
    public function Set($key, $value) {
        $this->obj[$key] = $value;
    }
    public function Id() {
        return isset($this->obj['id']) ? $this->obj['id'] : FALSE;
    }
    public function JsonToArray($param) {
        return isset($this->obj[$param]) ? json_decode($this->obj[$param], TRUE) : array();
    }
    public function Arg($param) {
        return isset($this->obj[$param]) ? htmlspecialchars($this->obj[$param]) : FALSE;
    }
    
    public function MultiTableChoseArray($param) {
        return isset($this->obj[$param]) ? explode('_|_', $this->obj[$param])  : FALSE;
    }
    
    public function TextPreview($param, $length = 250) {
        $string = isset($this->obj[$param]) ? strip_tags($this->obj[$param]) : FALSE;
        if($string && strlen($string) > $length) {
            $stringCut = substr($string, 0, $length);
            $string = substr($stringCut, 0, strrpos($stringCut, ' ')).''; 
        }
        return $string;
    }
    public function Text($param) {
        return isset($this->obj[$param]) ? $this->obj[$param] : FALSE;
    }
    public function Number($param, $decimals = 0) {
        return isset($this->obj[$param]) ? number_format($this->obj[$param], $decimals) : FALSE;
    }
    public function Boolean($param) {
        return isset($this->obj[$param]) && $this->obj[$param] > 0 ? TRUE : FALSE;
    }
    public function Img($param) {
        return isset($this->obj[$param]) && trim($this->obj[$param]) !== '' && file_exists(apppicIMG . $this->obj[$param]) ? base_url(picIMG . $this->obj[$param]) : FALSE;
    }
    public function File($param) {
        return isset($this->obj[$param]) && trim($this->obj[$param]) !== '' && file_exists(appFILES . $this->obj[$param]) ? base_url(FILES . $this->obj[$param]) : FALSE;
    }
    public function Datetime($param, $format = 'd/m/Y H:i') {
        return isset($this->obj[$param]) && !empty($this->obj[$param]) ? date($format, strtotime($this->obj[$param])) : date($format, strtotime(date("Y-m-d H:i:s")));
    }
    public function Arr($param = FALSE) {
        if(!$param) {
            return $this->obj;
        }
        return isset($this->obj[$param]) && !empty($this->obj[$param]) ? $this->obj[$param] : FALSE;
    }
    
    public function Seo($param) {
        if($param === 'url') {
            
            if(get_cookie('current_language', false) != 'he' and !empty(get_cookie('current_language', false))) {
                $arg = get_cookie('current_language', false).'_friendly';
            }
            
            else {
                 $arg = 'friendly';
            }
            
            
            return base_url(str_replace(" ", "", $this->obj['seo']->Arg($arg)) . '/');
            
        } else if($param === 'image') {
            return $this->obj['seo']->Img('image') ? $this->obj['seo']->Img('image') : base_url(dIMG . 'facebook.png');
        }
		return str_replace(" ", " ",$this->obj['seo']->Arg($param));
		//return str_replace("kingkoil.ak-digital.co.il", "kingkoil.co.il",$this->obj['seo']->Arg($param));
		
    }
}