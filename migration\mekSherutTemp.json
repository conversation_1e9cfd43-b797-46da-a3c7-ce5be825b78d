{"table": "mekSherutTemp", "controller": "mekSherut", "method": "", "explain": "mekSherutTemp", "title": "", "description": "", "seo_title": "", "seo_description": "", "image": "", "fields": {"MOSADNA": {"sort": "200", "width": "col-md-12", "name": "MOSADNA", "type": "short", "explain": "MOSADNA", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "TEKEN": {"sort": "190", "width": "col-md-12", "name": "TEKEN", "type": "long", "explain": "TEKEN", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "MAS_NOTES": {"sort": "180", "width": "col-md-12", "name": "MAS_NOTES", "type": "long", "explain": "MAS_NOTES", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "TAFKID": {"sort": "170", "width": "col-md-12", "name": "TAFKID", "type": "long", "explain": "TAFKID", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "PICTURE1": {"sort": "160", "width": "col-md-12", "name": "PICTURE1", "type": "short", "explain": "PICTURE1", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "PICTURE2": {"sort": "150", "width": "col-md-12", "name": "PICTURE2", "type": "short", "explain": "PICTURE2", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "PICTURE3": {"sort": "140", "width": "col-md-12", "name": "PICTURE3", "type": "short", "explain": "PICTURE3", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "PICTURE4": {"sort": "130", "width": "col-md-12", "name": "PICTURE4", "type": "short", "explain": "PICTURE4", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "YEAR": {"sort": "120", "width": "col-md-12", "name": "YEAR", "type": "short", "explain": "YEAR", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Maslol": {"sort": "110", "width": "col-md-12", "name": "Maslol", "type": "short", "explain": "Maslol", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Thum_Key": {"sort": "100", "width": "col-md-12", "name": "Thum_Key", "type": "long", "explain": "Thum_Key", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Thum_Value": {"sort": "95", "width": "col-md-12", "name": "Thum_Value", "type": "short", "explain": "Thum_Value", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Grain": {"sort": "90", "width": "col-md-12", "name": "Grain", "type": "integer", "explain": "Grain", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "City_Key": {"sort": "85", "width": "col-md-12", "name": "City_Key", "type": "short", "explain": "City_Key", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "City_Value": {"sort": "80", "width": "col-md-12", "name": "City_Value", "type": "integer", "explain": "City_Value", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "City_Zone": {"sort": "75", "width": "col-md-12", "name": "City_Zone", "type": "integer", "explain": "City_Zone", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "City_ZoneName": {"sort": "70", "width": "col-md-12", "name": "City_ZoneName", "type": "short", "explain": "City_ZoneName", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Rak_Key": {"sort": "65", "width": "col-md-12", "name": "Rak_Key", "type": "short", "explain": "Rak_Key", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Rak_Value": {"sort": "60", "width": "col-md-12", "name": "Rak_Value", "type": "integer", "explain": "Rak_Value", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}, "Rak_Phone": {"sort": "55", "width": "col-md-12", "name": "Rak_Phone", "type": "short", "explain": "Rak_Phone", "lang": "0", "options": {"yes": "כן", "no": "לא", "width": "0", "height": "0", "choices": "", "multiples": "", "table": "", "field_value": "", "field_text": ""}}}}