<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class ForeignWorkers extends CI_Model {
    
    //const TABLE = 'leads';
    
    const TABLE_TASKS = 'first_tasks';
    const TABLE_TASKSTYPES = 'first_task_type';
    const TABLE_TASKSTATUS = 'first_taskStatus';
    const TABLE_WORKERS = 'first_ForeignWorkers';
    const TABLE_USERS = 'first_Users';
    const TABLE_SENIORS = 'first_seniors';
    

    public function __construct() {
        parent::__construct();
        
    }
    
    public function DataSummary($params) {
        
        
        $this->db->select('id');
        $this->db->from('first_ForeignWorkers');
        $this->db->where('workerStatus', 'פעיל');
        $result= $this->db->get();
        //$workers = $result->row_array();
        $workers = $result->result_array();
        
        $count = count($workers);
        
        $linkedWorker = 0;
        
        if($count > 0) {
            
            foreach ($workers as $key => $worker) {
                
                $this->db->select('id');
                $this->db->from('first_placement');
                $this->db->where('ForeignWorkerName', $worker['id']);
                $this->db->where('status', 1);
                $result= $this->db->get();
                $placemendRow = $result->row_array();
                
                if(!empty($placemendRow)) {
                    $linkedWorker++;
                }
                
            }
            
            $percentLinked = ( $linkedWorker * 100 ) / $count;
            
        }
        
        
        
        $graph1 = array(
            'title' => 'אחוזי השמה',
            'percent' => $percentLinked,
            'count' => $count,
            //'percent1' => $percentLinked
        );
        
        $graph2 = array(
            'title' => 'דיווח למת"ש',
            'percent' => 45
        );
        
        $graph3 = array(
            'title' => 'השמות חדשות',
            'percent' => 100
        );
        
        $graph4 = array(
            'title' => 'דוח פירוט פעולות מת"ש',
            'percent' => 80
        );
        
        $results = array(
            'graph1' => $graph1,
            'graph2' => $graph2,
            'graph3' => $graph3,
            'graph4' => $graph4
        );
        
        
        
        return $results;
        
    }
    
    
    
    public function get_all_tasks($order = null, $userId = false, $limit = false) {
        
        $table = self::TABLE_TASKS;
        $table1 = self::TABLE_TASKSTYPES;
        $table2 = self::TABLE_TASKSTATUS;
        $table3 = self::TABLE_USERS;
        $table4 = self::TABLE_SENIORS;
        $table5 = self::TABLE_WORKERS;
        
        
        if($order == 'upTask') {
                
            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".upTask", "ASC");

        } else if($order == 'doneTask') {

            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".doneTask", "ASC");

        } else if($order == 'typeTask') {

            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".typeTask", "ASC");

        } else if($order == 'taskStatus') {

            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".taskStatus", "ASC");

        } else if($order == 'userId') {

            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".userId", "ASC");
            
        } else if($order == 'workerId') {

            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".workerId", "ASC");

        } else if($order == 'seniorId') {

            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".seniorId", "ASC");

        } else {
            $this->foreignWorkers->sort_objects(ForeignWorkers::TABLE_TASKS . ".doneTask", "ASC");
        }
        
//        $table2 = self::TABLE_filter_money;
//        $table3 = self::TABLE_filter_time;
        $select_table = array(
                $table . '.id',
                $table . '.upTask',
                $table . '.doneTask',
                $table . '.comments'
                );
        
        if(!empty($userId)) {
            $this->db->where('userId',$userId);
        }
        
        if($limit) {
            $this->db->limit($limit);
        }
        
        $this->db->select($select_table);
        //$this->db->select($table . '.*');
        
        
        
        $this->db->from($table);
        
        $this->db->join($table1, $table1 . ".id = ".$table.".typeTask", "LEFT");
        $select_table1 = array($table1 . '.name as typeTaskName');
        $this->db->select($select_table1);
        
        $this->db->join($table2, $table2 . ".id = ".$table.".taskStatus", "LEFT");
        $select_table2 = array($table2 . '.name as taskStatusName');
        $this->db->select($select_table2);
        
        $this->db->join($table3, $table3 . ".id = ".$table.".userId", "LEFT");
        $select_table3 = array($table3 . '.name as userIdName');
        $this->db->select($select_table3);
        
        $this->db->join($table5, $table5 . ".id = ".$table.".workerId", "LEFT");
        $select_table5 = array($table5 . '.name as workerIdName');
        $this->db->select($select_table5);
        
        $this->db->join($table4, $table4 . ".id = ".$table.".seniorId", "LEFT");
        $select_table4 = array($table4 . '.name as seniorIdName');
        $this->db->select($select_table4);
        
        //$select_table1 = array($table1 . '.name as typeTask',$table4 . '.is_vip as user_is_vip');
        
        $result = $this->db->get();
        
        $data=array();
        
        if($result->num_rows() > 0) {
        
            foreach($result->result_array() as $row) {
                
                //changeDateFormat($stDate, $stFormatFrom = "d/m/Y H:i", $stFormatTo = "Y/m/d H:i")
                
                $data[] = array (
                  
                    'id' => $row['id'],
                    'upTask' => changeDateFormat($row['upTask'], 'Y-m-d', 'd/m/Y'),
                    'doneTask' => changeDateFormat($row['doneTask'], 'Y-m-d', 'd/m/Y'),
                    'comments' => $row['comments'],
                    'typeTaskName' => $row['typeTaskName'],
                    'taskStatusName' => $row['taskStatusName'],
                    'userIdName' => $row['userIdName'],
                    'seniorIdName' => $row['seniorIdName'],
                    'workerIdName' => $row['workerIdName']
                );
                                
            }
        }
        
        return $data;
        
        
    }
    
    
    public function set_where($where) {
        $this->db->where($where);
    }
    
    public function sort_objects($order = "id", $sort = "DESC") {
        $this->db->order_by($order, $sort);
    }
    
    
    public function getRowValuesFromNameDB($fromDB, $fromFieldName, $id4Search) {
        
        $this->db->select('*');
        $this->db->from($fromDB);
        $this->db->where($fromFieldName, $id4Search);
        $result= $this->db->get();
        $results = $result->row_array();
        
        if(!empty($results)) {
            return $results;
        } else {
            return false;
        }
        
    }
    
    public function getDataforFormsPopulate($param,$table,$contactSenior = FALSE, $recommendsWorkers = FALSE) {
        
        if($recommendsWorkers) {
            $this->msite->set_where("workerId='$param'");
        } else {
            $this->msite->set_where("seniorId='$param'");
        }
        
        $this->msite->set_where("status='1'");
        
        $active = $this->msiteWs->objects_to_ArrayNoseoData($this->msite->get_all_objects($table));
        
        
        
        //print_r($active);die('sebas');
        
        $this->msite->set_where("status='0'");
        
        if($recommendsWorkers) {
            $this->msite->set_where("workerId='$param'");
        } else {
            $this->msite->set_where("seniorId='$param'");
        }
        $history = $this->msiteWs->objects_to_ArrayNoseoData($this->msite->get_all_objects($table));
        
        
        $tableData[] = array (
            'data' =>  isset($active[0]['data']) ? $active[0]['data'] : false,
            'history' => !empty($history) ? $history : false
        );
        
        if(!empty($active)) {
            if($contactSenior || $recommendsWorkers) {
                return $active;
            } else {
                return $tableData;
            }
        } else {
            $data[]=array('data'=>'');
            return array();
        }
    }
    
    public function getformFields($object = FALSE, $excludeFieldsArray = FALSE, $action = FALSE ) {
        
            
            $object_file = $object ? read_file(APPPATH . 'migration/' . $object . '.json') : FALSE;
            $formData = $object_file ? json_decode($object_file, TRUE) : FALSE;
            
            $counter = 0;
            
            foreach ($formData['fields'] as $value) {
                
                $counter ++;
                
                if($action == 'newWorker') {
                    
                    if($counter < 11) {
                        if ($value['name'] != 'seniorId' && $value['name'] != 'workerId') {    //NOT FOR DISPLAY
                            $formAllData['fieldsArray'][] = $value;
                        }
                    }
                    
                } else {
                    
                    if ($value['name'] != 'seniorId' && $value['name'] != 'workerId') {    //NOT FOR DISPLAY
                        $formAllData['fieldsArray'][] = $value;
                    }
                    
                }
                
            }
            
            if (!empty($excludeFieldsArray)) {
                foreach ($formAllData['fieldsArray'] as $key => $value) {
                    foreach ($excludeFieldsArray as $exclude) {
                        if ( $formAllData['fieldsArray'][$key]['name'] === $exclude ) {
                            unset($formAllData['fieldsArray'][$key]);
                        }
                    }
                }
            }
            
            
            usort($formAllData['fieldsArray'], function($a, $b) {
                
                $sortBy = 'sort';
                
                if($a[$sortBy]==$b[$sortBy]) return 0;
                return $a[$sortBy] < $b[$sortBy]?1:-1;
            });
            
            return $formAllData['fieldsArray'];
            
    }
    
    
    public function is_DoubleInsert($table = FALSE) {
        
        $this->db->select_max('created_at');
        $this->db->select('id');
        $result = $this->db->get($table);
        
        if($result->num_rows() > 0) {
            $row = $result->row_array();
            
            $now =  date("Y-m-d H:i:s");
            
            $a = new DateTime($row['created_at']);
            $b = new DateTime($now);
            
            $interval = $a->diff($b);

//            print_r($interval->format());die('asd');
            //$output = $interval->format("%H");
            
            $seconds = $interval->format("%s");
            
            if($seconds < 5) {
                $output = $row['id'];
            } else {
                $output = FALSE;
            }
            
            
            
        } else {
            $output = false;
        }
        
        return $output;
        
        //print_r($row);die();
        
    }
    
    
    public function getLinkedClientsToWorker($workerId) {
        
        $this->db->select('seniorId,inscriptionDate,toDate,id,status');
        $this->db->from('first_placement');
        //$this->db->where('status', 1);
        $this->db->where('ForeignWorkerName', $workerId);
        $this->db->where('ForeignWorkerName', $workerId);
        $this->db->order_by('InscriptionDate', 'DESC');
        
        $allPlacesGet = $this->db->get();
        $allPlaces = $allPlacesGet->result_array();
        
        
        if(!empty($allPlaces)) {
            
            //print_r($allPlaces);
            $totalDaysAll = 0;
            $count = count($allPlaces);
            
            //$reversed = array_reverse($allPlaces);
            
            foreach ($allPlaces as $key => $value) {
                
                if($value['status'] == 1) {
                    $value['toDate'] = date("Y-m-d H:i:s");
                }
                
                $rowDays = str_replace('+','',$this->getTotalPeriod($value['inscriptionDate'], $value['toDate']));
                $totalDaysAll = $totalDaysAll + $rowDays;
            }
            
            
            foreach ($allPlaces as $key => $value) {
                
                if($value['status']==1) {
                    $value['toDate'] = date("Y-m-d H:i:s");
                }
                
                $rowDays = str_replace('+','',$this->getTotalPeriod($value['inscriptionDate'], $value['toDate']));
                
//                if($key > 0) {
//                    $lastKey = $key - 1;
//                    $totalDaysAll = $rowDays - $clients[$lastKey]['totalTime'];
//                } else {
//                    $totalDaysAll = $rowDays;
//                }
                
                $totalDays = $rowDays;
                
                if( $key  == 0 ) {
                    $totalDaysAll = '('.$totalDaysAll.' סך הכל'.')';
                    
                } else {
                    $totalDaysAll = '';
                }
                
                $clients[] = array(
                    
                        'dataSenior' => $this->getClientsByIDs($value['seniorId']),
                        'idPlaces' => $value['id'],
                        'datePlacement' => $value['inscriptionDate'],
                        'datePlacementStatus' => $value['status'],
                        'toDate' => $value['toDate'],
                        'totalTime' => $totalDays.' '.'ימים',
                        'totalTimeFinal' => $totalDaysAll
                    );
            }
            
            return $clients;
            
        }
    }
    
    
    public function sortArray($a, $b, $field) {
        
        return strcmp($a[$field], $b[$field]);
        
    }
    
    public function getTotalPeriod($fromDay,$toDay) {

        //https://www.geeksforgeeks.org/php-date_diff-function/
        
        //https://www.php.net/manual/es/dateinterval.format.php
            
        if(!empty($fromDay) && !empty($toDay)) {
            
            $datetime1 = date_create($fromDay);
            $datetime2 = date_create($toDay);

            // calculates the difference between DateTime objects
            $interval = date_diff($datetime1, $datetime2);

            // printing result in days format
            //$totalTime = $interval->format('%y שנים, %m חודשים, %d ימים');
            $totalTime = $interval->format('%R%a');
            
            
        } else {
            $totalTime = '';
        }
        
        
        //echo "sebas: ".$totalTime; die();
        
        return $totalTime;
        
    }
    
    public function getClientsByIDs($id) {
        
        //print_r($arrayId);
        
        $this->db->select('id,tz,phone,name,surname,sort');
        $this->db->from('first_seniors');
        
        $this->db->where('status',1);
        $this->db->where('id', $id);
        
        $allClientsGet= $this->db->get();
        $results = $allClientsGet->row_array();
        
        return $results;
    }
    
    
    
    public function getClientsWorkers4Search() {
        
        $this->db->select('id,name,surname');
        $this->db->from('first_seniors');
        $this->db->where('status',1);
        $allClientsGet= $this->db->get();
        
        $allClients = $allClientsGet->result_array();
        
        $this->db->select('id,name,surname');
        $this->db->from('first_ForeignWorkers');
        $this->db->where('status',1);
        $allWorkersGet = $this->db->get();
        
        $allWorkers = $allWorkersGet->result_array();
        
        
        if(!empty($allClients) && !empty($allWorkers)) {
            
            foreach ($allClients as $client) {
                $allData[] = array (
                    'id' => $client['id'],
                    'name' => $client['name'].' '.$client['surname'].' (לקוח)',
                    'searchType' => 'clients'
                );
            };
            
            foreach ($allWorkers as $worker) {
                $allData[] = array (
                    'id' => $worker['id'],
                    'name' => $worker['name'].' '.$worker['surname'].' (עובד)',
                    'searchType' => 'foreignWorkers'
                );
            };
            
        } else $allData = array();
        
        
        return $allData;
        
    }
    
    
    public function getSMSPhones($clientsIDString, $sendPage = FALSE) {
        
        if(!$sendPage) {die('ERROR PAGE');}
        
        $ids = explode(',', $clientsIDString);
        
        $phones = array();
        
        foreach ($ids as $id) {
            
            $this->db->select('phoneSMS');
            
            if($sendPage == 'clients') {
                $this->db->from('first_seniors');
            } else if ($sendPage == 'workers') {
                $this->db->from('first_ForeignWorkers');
            } else {
                die('ERROR PAGE');
            }
            
            
            $this->db->where('id',$id);
            $phoneClient = $this->db->get();
            $phoneDB = $phoneClient->row_array();
            
            if(!empty($phoneDB)) {
                
                $phone = $phoneDB['phoneSMS'];
                
                if(!empty($phone)) {
                    $phone = trim($phone);
                    $phone = preg_replace('/[^0-9]/', '', $phone);
                    $phones[] = $phone;
                }
                
            }
        }
        
        return array(
            'phones' => implode(',', $phones)
        );
    }
    
    
    public function getBitlyUrl($long_url) {
        
        
        
        $apiv4 = 'https://api-ssl.bitly.com/v4/bitlinks';
        $genericAccessToken = '45d736689fd9e0855dfdfe1c29c708498c5130ac';

        $data = array(
            'long_url' => $long_url
        );
        $payload = json_encode($data);

        $header = array(
            'Authorization: Bearer ' . $genericAccessToken,
            'Content-Type: application/json',
            'Content-Length: ' . strlen($payload)
        );

        $ch = curl_init($apiv4);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($ch);
        $resultToJson = json_decode($result);

        if (isset($resultToJson->link)) {
            return $resultToJson->link;
        }
        else {
            return false;
        }
    }
    
    
    public function sendSMS($phone = FALSE, $message = FALSE, $from = 'Workers', $param = FALSE) {
        
        
        if(!empty($phone) && !empty($message)) {
            
            if($param == 'multi') {
                
                $phonenumbers = explode(',', $phone);
                if(!empty($phonenumbers)) {
                    foreach ($phonenumbers as $value) {
                        $phones[] =  preg_replace('/[^0-9]/', '', $value);
                    }
                }
                $phone = implode(',', $phones);
                
            } else {
                $phone = preg_replace('/[^0-9]/', '', $phone);
            }
            
            
            $ch = curl_init();

	    curl_setopt($ch, CURLOPT_URL,"http://api.multisend.co.il/MultiSendAPI/sendsms");
	    curl_setopt($ch, CURLOPT_POST, 1);
	    curl_setopt($ch, CURLOPT_POSTFIELDS,
	                "user=nathan&password=nathan1234&from=".$from."&recipient=".$phone."&message=".$message);

	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

	    $server_output = curl_exec ($ch);

	    curl_close ($ch);
            
            
        }
    }
    
    
    public function getStayZman($data) {
        
        $enterDate = isset($data['dateEnter']) ? $data['dateEnter'] : '';
        
        if(!empty($enterDate)) {
            
        
        $now = date("Y-m-d H:i:s");
        
        $datetime1 = date_create($enterDate);
        $datetime2 = date_create($now);
        
        $interval = date_diff($datetime1, $datetime2);
        $totalTime = $interval->format('%y שנים, %m חודשים, %d ימים');
        
        return $totalTime;
        
        } 
        
        else {
            return "אין תאריך כניסה לארץ";
        }
        
    }
    
    public function getSelectedPlacement($form, $clientId, $action = false) {
        
        if(!empty($form)) {
            
            $table = array();
            
            foreach ($form as $value) {
                
                if($value['type'] == 'table') {
                    
                    
                    $this->db->select('ForeignWorkerName');
                    $this->db->from('first_placement');
                    $this->db->where('status', 1);
                    $this->db->where("seniorId='$clientId'");
                    $result= $this->db->get();
                    $placement = $result->row_array();
                    $placementId = $placement['ForeignWorkerName'];
                    
                    if(!empty($placementId)) {
                        
                        $rows = $this->msite->get_all_objects($value['options']['table']);
                        $this->msite->set_where("id='$placementId'");
                        $rows = $this->msite->get_all_objects($value['options']['table'], NULL, array('id','name'));
                        $rowsData = $this->msiteWs->objects_to_ArrayNoseoData($rows);
                        
                    } else {
                        $rowsData = array();
                    }
                    

                    $table[$value['name']] = $rowsData;
                    
                    $item1 = array(
                            'id' => '0',
                            'name' => '---'.'ללא שיבוץ'.'---'
                        );
                    
                    $item = array(
                        'data' => $item1
                    );

                    // MOVE LELO SHIVUTZ TO TOP
                    array_unshift($table[$value['name']] , $item);
                    
                    
                    // NOT WORKING WORKERS
                    // 
                    //print_r($table); die();
                    
                    $placements = array();
                    
                    $this->db->select('ForeignWorkerName');
                    $this->db->from('first_placement');
                    $this->db->where('status', 1);
                    $result= $this->db->get();
                    $placements = $result->result_array();

                    $placementsFreeWorkers = array();

                    $rows = $this->msite->get_all_objects($value['options']['table']);
                    $rows = $this->msite->get_all_objects($value['options']['table'], NULL, array('id','name'));
                    $rowsData = $this->msiteWs->objects_to_ArrayNoseoData($rows);
                    

                    if(!empty($placements) && !empty($rowsData)) {
//                            
                        foreach ($rowsData as $valueTable) {
                            
                            $notBusy = true;

                            //print_r($placements);die();
//                                
                            foreach ($placements as $placement) {
                                if($valueTable['data']['id'] == $placement['ForeignWorkerName'] ) {
                                    $notBusy = false;
                                }
                            };

                            if($notBusy) {
                                    $placementsFreeWorkers[] = $valueTable;
                            };
                            
                        }

                    }
                }
            }
            
            
            
            if(isset($placementsFreeWorkers) && !empty(($placementsFreeWorkers))) {
                $table['placementsFreeWorkers'] = $placementsFreeWorkers;
            }
               
            return $table;
            
        } else {
            return array();
        }
        
        
    }
    
    
    public function getRowTablesforSelectPlacement($form,$action = false) {
        
        if(!empty($form)) {
            
            $table = array();
            
            foreach ($form as $value) {
                
                if($value['type'] == 'table') {
                    
                    $this->msite->sort_objects("name", "ASC");
                    //$rows = $this->msite->get_all_objects($value['options']['table']);
                    $rows = $this->msite->get_all_objects($value['options']['table'], NULL, array('id','name'));
                    $rowsData= $this->objects_to_ArrayNoseoData($rows);
                    
                    $table[$value['name']] = $rowsData;
                    
                    if($value['name'] == 'ForeignWorkerName') {
                        
                        $item1 = array(
                            'id' => '0',
                            'name' => '---'.'ללא שיבוץ'.'---'
                        );
                    
                        $item = array(
                            'data' => $item1
                        );
                    
                        // MOVE LELO SHIVUTZ TO TOP
                        array_unshift($table[$value['name']] , $item);
                        
                        //print_r($table); die();
                        $this->db->select('ForeignWorkerName');
                        $this->db->from('first_placement');
                        $this->db->where('status', 1);
                        $result= $this->db->get();
                        $placements = $result->result_array();
                        
                        $placementsFreeWorkers = array();
                        
                        
                        
                        if(!empty($placements) && !empty($table['ForeignWorkerName'])) {
//                            
                            foreach ($table['ForeignWorkerName'] as $valueTable) {
//                                
                                $notBusy = true;
                                
                                //print_r($placements);die();
//                                
                                foreach ($placements as $placement) {
                                    if($valueTable['data']['id'] == $placement['ForeignWorkerName'] ) {
                                        $notBusy = false;
                                    }
                                };
                                
                                if($notBusy) {
                                        $placementsFreeWorkers[] = $valueTable;
                                };
                            }
                            
                        }
                        
                    }
                
                }
            }
            
            if(isset($placementsFreeWorkers) && !empty(($placementsFreeWorkers))) {
                
                $table['placementsFreeWorkers'] = $placementsFreeWorkers;
                
            }
            
            
            
            return $table;
        } else {
            return false;
        }
    }
    
    
}